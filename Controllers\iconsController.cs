using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data.SqlClient;
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

namespace kasvol.Controllers
{
    [Authorize]
    public class iconsController : BaseController
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly KasvolDbContext _db;

        public iconsController(KasvolDbContext context, IWebHostEnvironment webHostEnvironment)
        {
            _db = context;
            _webHostEnvironment = webHostEnvironment;
        }
        [HttpPost]
        public JsonResult Deletelink(string idd)
        {
            int id = Convert.ToInt32(idd);
            
            var icon = _db.icons.Find(id);
            if (icon != null)
            {
                _db.icons.Remove(icon);
                _db.SaveChanges();
            }

            return Json(new { isValid = true });



        }
        // GET: icons
        [HttpPost]
        public async Task<ActionResult> upload( IFormFile[] postedFile)
        {

            string codd = Guid.NewGuid().ToString().Replace("-", string.Empty).Substring(0, 5);

            string path = Path.Combine(_webHostEnvironment.WebRootPath, "kasvolfactory", "vi", "Imagecenter", codd);



            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }


            if (postedFile != null)
            {
                foreach (IFormFile file in postedFile)
                {

                    if (file != null)
                    {
                        string normalImagePath = System.IO.Path.Combine(path, file.FileName);
                        string webPFileName = System.IO.Path.GetFileNameWithoutExtension(file.FileName) + ".webp";
                        string webPImagePath = System.IO.Path.Combine(path, webPFileName);
                        // using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data.SqlClient;
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                        string ext = System.IO.Path.GetExtension(file.FileName);
                        if (ext != ".webp")
                        {
                            var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                            using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data.SqlClient;
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                        }
                        string fileName = Path.GetFileName(file.FileName);
                        // using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data.SqlClient;
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                        var usersfile1 = new icons();

                        usersfile1.content = "kasvolfactory/vi/Imagecenter/" + codd + "/" + webPFileName;
                        usersfile1.guid = codd;
                        usersfile1.title = System.IO.Path.GetFileNameWithoutExtension(file.FileName);
                     
                        _db.icons.Add(usersfile1);
                        _db.SaveChanges();


                    }
                }



            }

            return RedirectToAction("Index");
        }
        public ActionResult  Index(string word, int? take)
        {
            int kk = 12;
            if (take != null)
            {
                kk = take ?? 12;
            }
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            var icons = _db.icons;
            if (word != null && take == null) { return View(icons.Where(a => a.title.Contains(word)).OrderByDescending(a => a.iconsid).Take(kk).ToList()); }
            else if (word == null && take != null) { return View(icons.OrderByDescending(a => a.iconsid).Take(kk).ToList()); }
            else if (word != null && take != null)
            { return View(icons.Where(a => a.title.Contains(word)).OrderByDescending(a => a.iconsid).Take(kk).ToList()); }
            else if (take == 0)
            {
                return View(icons.OrderByDescending(a => a.iconsid).ToList());
            }
            else
            {
                return View(icons.OrderByDescending(a => a.iconsid).Take(kk).ToList());
            }
        }

        // GET: icons/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            icons icons = _db.icons.Find(id);
            if (icons == null)
            {
                return NotFound();
            }
            return View(icons);
        }

        // GET: icons/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: icons/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("iconsid,title,content,type,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] icons icons)
        {
            if (ModelState.IsValid)
            {
                _db.icons.Add(icons);
                _db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(icons);
        }

        // GET: icons/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            icons icons = _db.icons.Find(id);
            if (icons == null)
            {
                return NotFound();
            }
            return View(icons);
        }

        // POST: icons/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("iconsid,title,content,type,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] icons icons)
        {
            if (ModelState.IsValid)
            {
                _db.Entry(icons).State = EntityState.Modified;
                _db.SaveChanges();
                return RedirectToAction("Index");
            }
            return View(icons);
        }

        // GET: icons/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            icons icons = _db.icons.Find(id);
            if (icons == null)
            {
                return NotFound();
            }
            return View(icons);
        }

        // POST: icons/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            icons icons = _db.icons.Find(id);
            _db.icons.Remove(icons);
            _db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}






