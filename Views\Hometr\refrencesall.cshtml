
@model X.PagedList.IPagedList<kasvol.Models.Reference>
@using X.PagedList.Mvc;
<link href="~/Content/PagedList.css?v=1" rel="stylesheet" type="text/css" />
@{
    ViewBag.OGUrlcanon = ViewBag.OGUrl;
    ViewBag.OGUrlcar = ViewBag.OGUrl.Replace("https://kasvol.com/tr/", "https://kasvol.com/");
    ViewBag.OGUrlcen = ViewBag.OGUrl.Replace("https://kasvol.com/tr/", "https://kasvol.com/en/");
}
@section MetaTags
{
    <meta name="description" content="@ViewBag.OGDesc">
    <meta name="keywords" content="@ViewBag.keyword ">
    <meta name="Yahoo,Google,Lycos,AOL,Excite,Dmoz,Altavista" content="@ViewBag.keyword ">

    <meta name="abstract" content="@ViewBag.keyword ">

}
<script type="application/ld+json">
    {
      "@("@")context": "https://schema.org",
      "@("@")type": "BreadcrumbList",
      "itemListElement": [{
        "@("@")type": "ListItem",
        "position": 1,
        "name": "@Resources.Resource.home",
        "item": "https://kasvol.com/tr"
      },{
        "@("@")type": "ListItem",
        "position": 2,
        "name": "@ViewBag.title",
        "item": "https://kasvol.com/tr/our-clients"
      }]
    }
</script>
<section class="subpage-head" data-bg="/assets/img/subpage-head.webp">
    <h2>@Resources.Resource.all_references</h2>
    <ul>
        <li><a href="@Url.Action("Index", "Hometr")"><i class="fi flaticon-hut"></i>@Resources.Resource.home</a></li>
        <li><a href="#"><i class="fi flaticon-next-1" style="transform: skewY(180deg);"></i>@Resources.Resource.all_references</a></li>
    </ul>
    <svg xmlns="http://www.w3.org/2000/svg"
         xmlns:xlink="http://www.w3.org/1999/xlink"
         width="652px" height="268px">
        <path fill-rule="evenodd" stroke-width="1px" stroke="rgb(53, 67, 83)" fill-opacity="0" fill="rgb(236, 235, 233)"
              d="M516.946,263.994 L455.612,263.994 L455.612,227.182 L458.556,227.182 L464.363,227.182 L516.946,227.182 C567.948,227.182 606.201,186.828 606.201,135.151 C606.201,83.470 567.948,43.115 516.946,43.115 L464.363,43.115 L458.556,43.115 L455.612,43.115 L455.612,6.304 L516.946,6.304 C592.034,6.304 647.995,59.398 647.995,135.151 C647.995,206.651 588.490,263.994 516.946,263.994 ZM380.906,74.265 L290.941,157.348 L185.914,55.892 L185.914,3.994 L290.941,108.954 L394.366,6.304 L421.990,6.304 L421.990,263.994 L380.906,263.994 L380.906,74.265 ZM81.916,116.743 L159.839,116.743 C203.759,116.743 233.512,147.893 233.512,190.369 C233.512,228.598 203.759,263.994 159.839,263.994 L8.246,263.994 L8.246,227.182 L155.588,227.182 C177.550,227.182 192.425,212.315 192.425,190.369 C192.425,168.422 177.550,153.555 155.588,153.555 L77.666,153.555 C33.746,153.555 3.994,122.407 3.994,79.929 C3.994,40.992 33.746,6.304 77.666,6.304 L149.265,6.304 L149.265,43.115 L81.916,43.115 C59.955,43.115 45.080,57.983 45.080,79.929 C45.080,101.876 59.955,116.743 81.916,116.743 Z" />
    </svg>
</section>
<section class="projects-page blog-page subpage">
    @using kasvol.service;
    @{kasvolservices t = new kasvolservices(); }
    <h1 style="color:#bf9d7f">@Resources.Resource.all_references</h1>
    <div class="container" style="padding: 15px 25px !important; margin-bottom: 25px !important; line-height: 1.8 !important; font-size: 18px !important; ">
        <p> @Html.Raw(WebUtility.HtmlDecode(t.getblockc(15, ViewBag.lang)))</p>
    </div>
    <div>

        <div class="list" style="text-align:center">
            <div class="row">

                @foreach (var item in Model)

                {
                    string value = t.getvalue(ViewBag.lang, item.title);
                    string valueart = "/" + t.getimage(item.guid);
                    <div class="col-md-3">
                        <img data-src="@valueart" class="img-fluid" style="width:200px;height:200px" alt="@value">

                        <h3> @value <br> </h3>
                    </div>

                }

            </div>
        </div>
        <br />
        <div class="row" style="direction:rtl">
            <div class="col-lg-12 " style="text-align:center;">
                @Resources.Resource.proj23 @(Model.PageCount < Model.PageNumber ? 0 : Model.PageNumber) @Resources.Resource.proj24 @Model.PageCount

                @Html.PagedListPager(Model, page => Url.Action("refrencesall", new { page }))
            </div>
        </div>
    </div>
</section>


