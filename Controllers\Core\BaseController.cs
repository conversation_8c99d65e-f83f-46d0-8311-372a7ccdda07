using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using Microsoft.Extensions.Configuration;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using improved culture management
                await SetCultureAsync(context.HttpContext);
using System.Globalization;
using Microsoft.AspNetCore.Mvc.Filters;
using strongly-typed helper
                SetViewData("notif", await _db.orders.CountAsync(a => a.isneedapproved && !a.isapproved));
using Microsoft.Extensions.Logging;
using kasvol.service;

namespace kasvol.Controllers.Core
{
    /// <summary>
    /// Base controller that provides common functionality for all controllers in the application.
    /// Handles culture settings, notifications, and provides helper methods for derived controllers.
    /// </summary>
    public abstract class BaseController : Controller
    {
        protected readonly KasvolDbContext _db;
        protected readonly IConfiguration _configuration;
        protected readonly kasvolservices _kasvolServices;
        protected readonly ILogger<BaseController> _logger;

        /// <summary>
        /// Initializes a new instance of the BaseController class with required dependencies
        /// </summary>
        /// <param name="db">Database context</param>
        /// <param name="configuration">Application configuration</param>
        /// <param name="kasvolServices">Service layer for business logic</param>
        /// <param name="logger">Logger for diagnostic information</param>
        public BaseController(KasvolDbContext db, IConfiguration configuration, kasvolservices kasvolServices, ILogger<BaseController> logger = null)
        {
            _db = db ?? throw new ArgumentNullException(nameof(db));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _kasvolServices = kasvolServices ?? throw new ArgumentNullException(nameof(kasvolServices));
            _logger = logger;
        }
        
        /// <summary>
        /// Sets a strongly-typed value in ViewData dictionary
        /// </summary>
        protected void SetViewData<T>(string key, T value)
        {
            ViewData[key] = value;
        }
        
        /// <summary>
        /// Gets a strongly-typed value from ViewData dictionary
        /// </summary>
        protected T GetViewData<T>(string key, T defaultValue = default)
        {
            if (ViewData.TryGetValue(key, out var value) && value is T typedValue)
            {
                return typedValue;
            }
            return defaultValue;
        }

        /// <summary>
        /// Executes before the action method is invoked to set up common data and handle culture settings
        /// </summary>
        /// <param name="context">The context for the action being executed</param>
        /// <param name="next">The delegate to execute the next middleware in the pipeline</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            try
            {
                // Set notifications for orders using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using Microsoft.Extensions.Configuration;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using improved culture management
                await SetCultureAsync(context.HttpContext);
using System.Globalization;
using Microsoft.AspNetCore.Mvc.Filters;
using strongly-typed helper
                SetViewData("notif", await _db.orders.CountAsync(a => a.isneedapproved && !a.isapproved));
using Microsoft.Extensions.Logging;
using kasvol.service;

SetViewData("notif1", await _db.orders.CountAsync(a => !a.isneedapproved && a.isapproved && !a.issold));

                // Get orders from last week that exceed max discount
                DateTime oneWeekAgo = DateTime.Now.AddDays(-7);
                
                _logger?.LogInformation("Retrieving orders from the last 7 days");
                
                var recentOrders = await _db.orders
                    .Where(a => a.datecreated >= oneWeekAgo)
                    .ToListAsync();

                var ordersExceedingDiscount = new List<Order>();
                int maxDiscount = _kasvolServices.maxdis();
                
                _logger?.LogInformation($"Processing {recentOrders.Count} recent orders with max discount of {maxDiscount}%");

                foreach (var order in recentOrders)
                {
                    var orderDetails = await _db.orderDetails
                        .Where(a => a.ordercode == order.kasvolcode)
                        .ToListAsync();

                    foreach (var detail in orderDetails)
                    {
                        detail.newUnitPrice ??= detail.UnitPrice;
                        
                        try
                        {
                            // Avoid division by zero
                            if (detail.UnitPrice == 0)
                            {
                                _logger?.LogWarning($"Order {order.kasvolcode} has item with zero unit price");
                                continue;
                            }
                            
                            decimal discountPercentage = (decimal)(((detail.UnitPrice - detail.newUnitPrice) / detail.UnitPrice) * 100);

                            if (discountPercentage > maxDiscount)
                            {
                                ordersExceedingDiscount.Add(order);
                                _logger?.LogDebug($"Order {order.kasvolcode} exceeds max discount with {discountPercentage:F2}%");
                                break;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, $"Error calculating discount for order {order.kasvolcode}: {{ErrorMessage}}", ex.Message);
                        }
                    }
                }

                SetViewData("notif2", ordersExceedingDiscount.Count);
                _logger?.LogInformation($"Found {ordersExceedingDiscount.Count} orders exceeding maximum discount");

                // Handle language settings using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using Microsoft.Extensions.Configuration;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using improved culture management
                await SetCultureAsync(context.HttpContext);
using System.Globalization;
using Microsoft.AspNetCore.Mvc.Filters;
using strongly-typed helper
                SetViewData("notif", await _db.orders.CountAsync(a => a.isneedapproved && !a.isapproved));
using Microsoft.Extensions.Logging;
using kasvol.service;

// Execute the action
                await base.OnActionExecutionAsync(context, next);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "An error occurred during controller execution: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Gets the current user's ID from claims principal
        /// </summary>
        /// <returns>The user ID or null if not authenticated</returns>
        protected string GetUserId()
        {
            return User?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
        }

        /// <summary>
        /// Creates a standardized JSON response with the specified status code
        /// </summary>
        /// <param name="data">The data to serialize as JSON</param>
        /// <param name="statusCode">The HTTP status code to return</param>
        /// <returns>A JsonResult with the specified data and status code</returns>
        protected IActionResult JsonResponse(object data, int statusCode = 200)
        {
            return new JsonResult(data) { StatusCode = statusCode };
        }

        /// <summary>
        /// Sets the culture for the current request based on query string or cookie
        /// </summary>
        /// <param name="httpContext">The current HttpContext</param>
        /// <returns>Task representing the asynchronous operation</returns>
        protected async Task SetCultureAsync(HttpContext httpContext)
        {
            try
            {
                string languageCode = httpContext.Request.Query["lang"].ToString();
                string cultureName = "ar-AE"; // Default culture

                if (!string.IsNullOrEmpty(languageCode))
                {
                    cultureName = languageCode.ToLower() == "ar" ? "ar-AE" : "en-US";
                    _logger?.LogDebug($"Setting culture from query parameter: {cultureName}");
                }
                else if (httpContext.Request.Cookies.TryGetValue("CurrentLanguage", out string cookieValue))
                {
                    cultureName = cookieValue;
                    _logger?.LogDebug($"Setting culture from cookie: {cultureName}");
                }

                // Set culture
                var culture = new CultureInfo(cultureName);
                CultureInfo.CurrentCulture = culture;
                CultureInfo.CurrentUICulture = culture;

                // Set language cookie with improved options
                var cookieOptions = new CookieOptions
                {
                    HttpOnly = true,
                    Secure = httpContext.Request.IsHttps,
                    SameSite = SameSiteMode.Lax,
                    Expires = DateTimeOffset.UtcNow.AddYears(1)
                };
                
                httpContext.Response.Cookies.Append("CurrentLanguage", cultureName, cookieOptions);
                
                // Store the current culture in ViewData for views to access
                SetViewData("CurrentCulture", cultureName);
                SetViewData("IsRightToLeft", cultureName.StartsWith("ar"));
                
                await Task.CompletedTask; // For future async operations
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error setting culture: {ErrorMessage}", ex.Message);
            }
        }
    }
}
