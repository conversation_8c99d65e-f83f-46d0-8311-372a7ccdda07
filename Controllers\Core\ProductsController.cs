using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(fileStream);
using System;

namespace kasvol.Controllers.Core
{
    [Authorize]
    public class ProductsController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public ProductsController(KasvolDbContext db, IConfiguration configuration, kasvolservices kasvolServices,
            UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor)
            : base(db, configuration, kasvolServices, httpContextAccessor)
        {
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
        }

        [HttpPost]
        public async Task<JsonResult> editalt(int id, int alt)
        {
            Product page = await _db.Products.FindAsync(id);
            int dd = Convert.ToInt32(alt);

            var arProduct = await _db.Products
                .Where(d => d.guid == page.guid && d.lang == "ar")
                .SingleOrDefaultAsync();

            if (arProduct != null)
            {
                arProduct.arrange = dd;
            }

            var enProduct = await _db.Products
                .Where(d => d.guid == page.guid && d.lang == "en")
                .SingleOrDefaultAsync();

            if (enProduct != null)
            {
                enProduct.arrange = dd;
            }

            var trProduct = await _db.Products
                .Where(d => d.guid == page.guid && d.lang == "tr")
                .SingleOrDefaultAsync();

            if (trProduct != null)
            {
                trProduct.arrange = dd;
            }

            await _db.SaveChangesAsync();

            return Json(new { success = true });
        }

        // GET: Products
        public async Task<ActionResult> Index(string word, int? take)
        {
            int pageSize = 50;
            if (take.HasValue)
            {
                pageSize = take.Value;
            }

            var query = _db.Products.Where(a => a.lang == "ar").AsQueryable();

            if (!string.IsNullOrEmpty(word))
            {
                query = query.Where(a => a.ProductName.Contains(word));
            }

            if (pageSize > 0)
            {
                return View(await query.OrderByDescending(a => a.ProductId).Take(pageSize).ToListAsync());
            }
            else
            {
                return View(await query.OrderByDescending(a => a.ProductId).ToListAsync());
            }
        }

        // GET: Products/Details/5
        public async Task<ActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }

            Product product = await _db.Products.FindAsync(id);
            if (product == null)
            {
                return NotFound();
            }

            return View(product);
        }

        // GET: Products/Create
        public ActionResult Create()
        {
            ViewBag.CategoryId = new SelectList(_db.Pcategories.Where(a => a.lang == "ar"), "CategoryId", "CategoryName");
            return View();
        }

        // POST: Products/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Create([Bind("ProductId,ProductName,Description,UnitPrice,CategoryId,ImagePath,length,width,height,weight,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] Product product, IFormFile ImageFile)
        {
            product.IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();
            product.user = _userManager.GetUserId(User);
            product.datecreated = DateTime.Now;
            product.guid = Guid.NewGuid().ToString();
            product.lang = "ar";

            if (ModelState.IsValid)
            {
                if (ImageFile != null && ImageFile.Length > 0)
                {
                    // Handle image upload
                    string fileName = Path.GetFileName(ImageFile.FileName);
                    string uniqueFileName = Guid.NewGuid().ToString() + "_" + fileName;
                    string filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot/uploads", uniqueFileName);

                    using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(fileStream);
using System;

}

                    product.ImagePath = "/uploads/" + uniqueFileName;
                    product.ogimage = "/uploads/" + uniqueFileName;
                }

                _db.Products.Add(product);
                await _db.SaveChangesAsync();

                // Create English version
                Product enProduct = new Product
                {
                    ProductName = product.ProductName,
                    Description = product.Description,
                    UnitPrice = product.UnitPrice,
                    CategoryId = product.CategoryId,
                    ImagePath = product.ImagePath,
                    length = product.length,
                    width = product.width,
                    height = product.height,
                    weight = product.weight,
                    arrange = product.arrange,
                    datecreated = product.datecreated,
                    IP = product.IP,
                    user = product.user,
                    guid = product.guid,
                    lang = "en",
                    ogtitle = product.ogtitle,
                    ogdescription = product.ogdescription,
                    keywords = product.keywords,
                    ogimage = product.ogimage,
                    twimage = product.twimage
                };

                _db.Products.Add(enProduct);

                // Create Turkish version
                Product trProduct = new Product
                {
                    ProductName = product.ProductName,
                    Description = product.Description,
                    UnitPrice = product.UnitPrice,
                    CategoryId = product.CategoryId,
                    ImagePath = product.ImagePath,
                    length = product.length,
                    width = product.width,
                    height = product.height,
                    weight = product.weight,
                    arrange = product.arrange,
                    datecreated = product.datecreated,
                    IP = product.IP,
                    user = product.user,
                    guid = product.guid,
                    lang = "tr",
                    ogtitle = product.ogtitle,
                    ogdescription = product.ogdescription,
                    keywords = product.keywords,
                    ogimage = product.ogimage,
                    twimage = product.twimage
                };

                _db.Products.Add(trProduct);
                await _db.SaveChangesAsync();

                return RedirectToAction("Index");
            }

            ViewBag.CategoryId = new SelectList(_db.Pcategories.Where(a => a.lang == "ar"), "CategoryId", "CategoryName", product.CategoryId);
            return View(product);
        }

        // GET: Products/Edit/5
        public async Task<ActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }

            Product product = await _db.products.FindAsync(id);
            if (product == null)
            {
                return NotFound();
            }

            ViewBag.CategoryId = new SelectList(_db.pcategories.Where(a => a.lang == product.lang), "CategoryId", "CategoryName", product.CategoryId);
            return View(product);
        }

        // POST: Products/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Edit([Bind("ProductId,ProductName,Description,UnitPrice,CategoryId,ImagePath,length,width,height,weight,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] Product product, IFormFile ImageFile)
        {
            product.modifiedIP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();
            product.datemodified = DateTime.Now;

            if (ModelState.IsValid)
            {
                if (ImageFile != null && ImageFile.Length > 0)
                {
                    // Handle image upload
                    string fileName = Path.GetFileName(ImageFile.FileName);
                    string uniqueFileName = Guid.NewGuid().ToString() + "_" + fileName;
                    string filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot/uploads", uniqueFileName);

                    using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(fileStream);
using System;

}

                    product.ImagePath = "/uploads/" + uniqueFileName;
                    product.ogimage = "/uploads/" + uniqueFileName;
                }

                _db.Entry(product).State = EntityState.Modified;
                await _db.SaveChangesAsync();

                // Update other language versions
                var otherProducts = await _db.products
                    .Where(p => p.guid == product.guid && p.lang != product.lang)
                    .ToListAsync();

                foreach (var otherProduct in otherProducts)
                {
                    otherProduct.UnitPrice = product.UnitPrice;
                    otherProduct.CategoryId = product.CategoryId;
                    otherProduct.ImagePath = product.ImagePath;
                    otherProduct.length = product.length;
                    otherProduct.width = product.width;
                    otherProduct.height = product.height;
                    otherProduct.weight = product.weight;
                    otherProduct.arrange = product.arrange;
                    otherProduct.datemodified = product.datemodified;
                    otherProduct.modifiedIP = product.modifiedIP;
                    otherProduct.ogimage = product.ogimage;
                    otherProduct.twimage = product.twimage;
                }

                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }

            ViewBag.CategoryId = new SelectList(_db.pcategories.Where(a => a.lang == product.lang), "CategoryId", "CategoryName", product.CategoryId);
            return View(product);
        }

        // GET: Products/Delete/5
        public async Task<ActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }

            Product product = await _db.products.FindAsync(id);
            if (product == null)
            {
                return NotFound();
            }

            return View(product);
        }

        // POST: Products/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> DeleteConfirmed(int id)
        {
            Product product = await _db.products.FindAsync(id);
            string guid = product.guid;

            // Delete all language versions
            var productsToDelete = await _db.products
                .Where(p => p.guid == guid)
                .ToListAsync();

            foreach (var p in productsToDelete)
            {
                _db.products.Remove(p);
            }

            await _db.SaveChangesAsync();
            return RedirectToAction("Index");
        }
    }
}

