using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authorization;
using System;
using kasvol.service;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class service_itemsController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;

        public service_itemsController(KasvolDbContext db, UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor) : base(db, httpContextAccessor)
        {
            _userManager = userManager;
        }

        // GET: service_items
        public ActionResult Index()
        {
            return View(_db.ServiceItems.ToList());
        }

        // GET: service_items/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            service_items service_items = _db.ServiceItems.Find(id);
            if (service_items == null)
            {
                return NotFound();
            }
            return View(service_items);
        }

        // GET: service_items/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: service_items/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create( service_items service_items)
        {
            kasvolservices t = new kasvolservices();
            service_items.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            service_items.user = _userManager.GetUserId(User);
            service_items.title = t.setvalue(service_items.lang, service_items.title);
        
            if (ModelState.IsValid)
            {
                _db.ServiceItems.Add(service_items);
                _db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(service_items);
        }

        // GET: service_items/Edit/5
        public ActionResult Edit(int? id, string lang)
        {
            if (lang != null)
            {
                ViewBag.lang = lang;
            }
            if (id == null)
            {
                return BadRequest();
            }
            service_items service_items = _db.ServiceItems.Find(id);
            if (service_items == null)
            {
                return NotFound();
            }
            return View(service_items);
        }

        // POST: service_items/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit( service_items service_items)
        {
            kasvolservices t = new kasvolservices();
            service_items.title = t.updatevalue(service_items.lang, service_items.title, service_items.oldvalue);

            service_items.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();

            if (ModelState.IsValid)
            {
                var users = _db.ServiceItems
                    .Where(d => d.service_itemsid == service_items.service_itemsid)
                    .Single();

                users.title = service_items.title;
                users.datemodified = DateTime.Now;
                users.modifiedIP = service_items.modifiedIP;
                _db.SaveChanges();
                
                return RedirectToAction("Index");
            }
            return View(service_items);
        }

        // GET: service_items/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            service_items service_items = _db.ServiceItems.Find(id);
            if (service_items == null)
            {
                return NotFound();
            }
            return View(service_items);
        }

        // POST: service_items/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            service_items service_items = _db.ServiceItems.Find(id);
            _db.ServiceItems.Remove(service_items);
            _db.SaveChanges();
            return RedirectToAction("Index");
        }


    }
}


