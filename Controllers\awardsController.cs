using System.Collections.Generic;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "2"
                                            select d).Single();
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.medias.Add(media);
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.documents.Add(media);
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.awards
                                 where d.awardsid == awards.awardsid
                                 select d).Single();
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.documents
                                            where d.guid == page.guid
                                            select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

namespace kasvol.Controllers
{
    [Authorize]
    public class awardsController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;

                private readonly IWebHostEnvironment _webHostEnvironment;
public awardsController(IWebHostEnvironment webHostEnvironment, UserManager<ApplicationUser> userManager)
        {
            _userManager = userManager;
            _webHostEnvironment = webHostEnvironment;
        }


        private kasvoldb db = new kasvoldb();

        // GET: awards
        public ActionResult Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            return View(db.awards.ToList());
        }
        public async Task<ActionResult> uploadv(int? id)
        {
            kasvolservices t = new kasvolservices(); if (id == null)
            {
                return BadRequest();
            }
            awards page = await db.awards.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            return View(page);
        }
        public async Task<ActionResult> uploadpdf(int? id)
        {
            kasvolservices t = new kasvolservices(); if (id == null)
            {
                return BadRequest();
            }
            awards page = await db.awards.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            return View(page);
        }
        [HttpPost]
        public async Task<ActionResult> uploadpdf(int? id, IFormFile ImageFile)
        {
            kasvolservices t = new kasvolservices();
            awards page = await db.awards.FindAsync(id);

            string path = Server.MapPath("~/kasvolfactory/vi/winner/" + page.awardsid + "/pdf/");


            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }


            if (ImageFile != null)
            {
                try
                {
                    string normalImagePath = System.IO.Path.Combine(path, ImageFile.FileName);
                    string webPFileName = System.IO.Path.GetFileNameWithoutExtension(ImageFile.FileName) + ".pdf";
                    string webPImagePath = System.IO.Path.Combine(path, webPFileName);
                    // using System.Collections.Generic;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "2"
                                            select d).Single();
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.medias.Add(media);
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.documents.Add(media);
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.awards
                                 where d.awardsid == awards.awardsid
                                 select d).Single();
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.documents
                                            where d.guid == page.guid
                                            select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                    string ext = System.IO.Path.GetExtension(ImageFile.FileName);
             

                    string yy = t.getdoc(page.guid);
                    if (yy != null)
                    {
                        using System.Collections.Generic;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "2"
                                            select d).Single();
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.medias.Add(media);
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.documents.Add(media);
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.awards
                                 where d.awardsid == awards.awardsid
                                 select d).Single();
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.documents
                                            where d.guid == page.guid
                                            select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

useredit.link = "kasvolfactory/vi/winner/" + id + "/pdf/" + webPFileName;
                            useredit.title = webPFileName;
                            context.SaveChanges();
                        }

                    }
                    else
                    {
                        documents media = new documents();

                        media.arrange = 1;

                        media.guid = page.guid;

                     
                        media.link = "kasvolfactory/vi/winner/" + id + "/pdf/" + webPFileName;
                        media.title = webPFileName;
                        using System.Collections.Generic;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "2"
                                            select d).Single();
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.medias.Add(media);
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.documents.Add(media);
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.awards
                                 where d.awardsid == awards.awardsid
                                 select d).Single();
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.documents
                                            where d.guid == page.guid
                                            select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

db1.SaveChanges();
                        }
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine("Overflow. {0}", e.Message);
                }
            }




            return RedirectToAction("Index");
        }
        [HttpPost]
        public async Task<ActionResult> uploadv(int? id, IFormFile ImageFile, IFormFile ImageFile1)
        {
            kasvolservices t = new kasvolservices();
            awards page = await db.awards.FindAsync(id);

            string path = Server.MapPath("~/kasvolfactory/vi/winner/" + page.awardsid + "/");

            string path1 = Server.MapPath("~/kasvolfactory/vi/winner/" + page.awardsid + "/logo/");
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
            if (!Directory.Exists(path1))
            {
                Directory.CreateDirectory(path1);
            }


            if (ImageFile != null)
            {
                try
                {
                    string normalImagePath = System.IO.Path.Combine(path, ImageFile.FileName);
                    string webPFileName = System.IO.Path.GetFileNameWithoutExtension(ImageFile.FileName) + ".webp";
                    string webPImagePath = System.IO.Path.Combine(path, webPFileName);
                    // using System.Collections.Generic;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "2"
                                            select d).Single();
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.medias.Add(media);
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.documents.Add(media);
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.awards
                                 where d.awardsid == awards.awardsid
                                 select d).Single();
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.documents
                                            where d.guid == page.guid
                                            select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                    string ext = System.IO.Path.GetExtension(ImageFile.FileName);
                    if (ext != ".webp")
                    {
                        var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                        using System.Collections.Generic;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "2"
                                            select d).Single();
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.medias.Add(media);
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.documents.Add(media);
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.awards
                                 where d.awardsid == awards.awardsid
                                 select d).Single();
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.documents
                                            where d.guid == page.guid
                                            select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                    }

                    string yy = t.getimagewithid(page.guid, "1");
                    if (yy != null)
                    {
                        using System.Collections.Generic;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "2"
                                            select d).Single();
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.medias.Add(media);
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.documents.Add(media);
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.awards
                                 where d.awardsid == awards.awardsid
                                 select d).Single();
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.documents
                                            where d.guid == page.guid
                                            select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

useredit.name = "kasvolfactory/vi/winner/" + id + "/" + webPFileName;
                            useredit.filename = webPFileName;
                            context.SaveChanges();
                        }

                    }
                    else
                    {
                        media media = new media();

                        media.arrange = 1;

                        media.guid = page.guid;
                        media.lang = "1";
                        media.type = "awards";
                        media.name = "kasvolfactory/vi/winner/" + id + "/" + webPFileName;
                        media.filename = webPFileName;
                        using System.Collections.Generic;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "2"
                                            select d).Single();
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.medias.Add(media);
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.documents.Add(media);
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.awards
                                 where d.awardsid == awards.awardsid
                                 select d).Single();
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.documents
                                            where d.guid == page.guid
                                            select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

db1.SaveChanges();
                        }
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine("Overflow. {0}", e.Message);
                }
            }


            if (ImageFile1 != null)
            {
                try
                {
                    string normalImagePath = System.IO.Path.Combine(path1, ImageFile1.FileName);
                    string webPFileName = System.IO.Path.GetFileNameWithoutExtension(ImageFile1.FileName) + ".webp";
                    string webPImagePath = System.IO.Path.Combine(path1, webPFileName);
                    // using System.Collections.Generic;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "2"
                                            select d).Single();
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.medias.Add(media);
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.documents.Add(media);
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.awards
                                 where d.awardsid == awards.awardsid
                                 select d).Single();
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.documents
                                            where d.guid == page.guid
                                            select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                    string ext = System.IO.Path.GetExtension(ImageFile1.FileName);
                    if (ext != ".webp")
                    {
                        var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                        using System.Collections.Generic;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "2"
                                            select d).Single();
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.medias.Add(media);
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.documents.Add(media);
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.awards
                                 where d.awardsid == awards.awardsid
                                 select d).Single();
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.documents
                                            where d.guid == page.guid
                                            select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                    }

                    string yy = t.getimagewithid(page.guid,"2");
                    if (yy != null)
                    {
                        using System.Collections.Generic;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "2"
                                            select d).Single();
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.medias.Add(media);
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.documents.Add(media);
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.awards
                                 where d.awardsid == awards.awardsid
                                 select d).Single();
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.documents
                                            where d.guid == page.guid
                                            select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

useredit.name = "kasvolfactory/vi/winner/" + id + "/logo/" + webPFileName;
                            useredit.filename = webPFileName;
                            context.SaveChanges();
                        }

                    }
                    else
                    {
                        media media = new media();

                        media.arrange =2;
                        media.lang = "2";
                        media.guid = page.guid;

                        media.type = "awardslogo";
                        media.name = "kasvolfactory/vi/winner/" + id + "/logo/" + webPFileName;
                        media.filename = webPFileName;
                        using System.Collections.Generic;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "2"
                                            select d).Single();
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.medias.Add(media);
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.documents.Add(media);
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.awards
                                 where d.awardsid == awards.awardsid
                                 select d).Single();
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.documents
                                            where d.guid == page.guid
                                            select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

db1.SaveChanges();
                        }
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine("Overflow. {0}", e.Message);
                }
            }

            return RedirectToAction("Index");
        }
        // GET: awards/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            awards awards = db.awards.Find(id);
            if (awards == null)
            {
                return NotFound();
            }
            return View(awards);
        }

        // GET: awards/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: awards/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create( awards awards)
        {
            kasvolservices t = new kasvolservices();
            awards.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            awards.user = _userManager.GetUserId(User);
            awards.title = t.setvalue(awards.lang, awards.title);

            if (ModelState.IsValid)
            {
                db.awards.Add(awards);
                db.SaveChanges();
                media media = new media();

                media.arrange = 1;

                media.guid = awards.guid;

                media.type = "awards";
            
                using System.Collections.Generic;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "2"
                                            select d).Single();
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.medias.Add(media);
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.documents.Add(media);
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.awards
                                 where d.awardsid == awards.awardsid
                                 select d).Single();
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.documents
                                            where d.guid == page.guid
                                            select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

db1.SaveChanges();
                }
                return RedirectToAction("Index");
            }

            return View(awards);
        }

        // GET: awards/Edit/5
        public ActionResult Edit(int? id, string lang)
        {
            if (lang != null)
            {
                ViewBag.lang = lang;
            }
            if (id == null)
            {
                return BadRequest();
            }
            awards awards = db.awards.Find(id);
            if (awards == null)
            {
                return NotFound();
            }
            return View(awards);
        }

        // POST: awards/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit( awards awards)
        {
            kasvolservices t = new kasvolservices();
            awards.title = t.updatevalue(awards.lang, awards.title, awards.oldvalue);

            awards.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();

            if (ModelState.IsValid)
            {
                using System.Collections.Generic;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "2"
                                            select d).Single();
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.medias.Add(media);
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.documents.Add(media);
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.awards
                                 where d.awardsid == awards.awardsid
                                 select d).Single();
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.documents
                                            where d.guid == page.guid
                                            select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

users.title = awards.title;

                    users.datemodified = DateTime.Now;
                    users.modifiedIP = awards.modifiedIP;
                    context.SaveChanges();
                }
             
                return RedirectToAction("Index");
            }
            return View(awards);
        }

        // GET: awards/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            awards awards = db.awards.Find(id);
            if (awards == null)
            {
                return NotFound();
            }
            return View(awards);
        }

        // POST: awards/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            awards awards = db.awards.Find(id);
            db.awards.Remove(awards);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}






