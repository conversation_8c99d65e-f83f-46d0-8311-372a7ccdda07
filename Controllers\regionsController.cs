using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using System;
using kasvol.service;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using Microsoft.AspNetCore.Identity;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class regionsController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;

        public regionsController(UserManager<ApplicationUser> userManager)
        {
            _userManager = userManager;
        }


        private kasvoldb db = new kasvoldb();

        // GET: regions
        public async Task<ActionResult> Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            var regions = db.regions.Include(r => r.Country);
            return View(await regions.ToListAsync());
        }

        // GET: regions/Details/5
        public async Task<ActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            region region = await db.regions.FindAsync(id);
            if (region == null)
            {
                return NotFound();
            }
            return View(region);
        }

        // GET: regions/Create
        public ActionResult Create()
        {
            ViewBag.countryid = db.Countries.ToList();
         
            return View();
        }

        // POST: regions/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Create([Bind("regionid,regionname,countryid,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] region region)
        {
            kasvolservices t = new kasvolservices();
            region.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            region.user = _userManager.GetUserId(User);
            region.regionname = t.setvalue("ar", region.regionname);
            if (ModelState.IsValid)
            {
                db.regions.Add(region);
                await db.SaveChangesAsync();
                return RedirectToAction("Index");
            }

            ViewBag.countryid = db.Countries.ToList();
            return View(region);
        }

        // GET: regions/Edit/5
        public async Task<ActionResult> Edit(int? id, string lang)
        {
            if (lang != null)
            {
                ViewBag.lang = lang;
            }
            if (id == null)
            {
                return BadRequest();
            }
            region region = await db.regions.FindAsync(id);
            if (region == null)
            {
                return NotFound();
            }
            ViewBag.countryid = db.Countries.ToList();
            return View(region);
        }

        // POST: regions/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Edit([Bind("regionid,regionname,countryid,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] region region)
        {
            kasvolservices t = new kasvolservices();
            region.regionname = t.updatevalue(region.lang, region.regionname, region.oldvalue);

            region.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();
            if (ModelState.IsValid)
            {
                db.Entry(region).State = EntityState.Modified;
                await db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            ViewBag.countryid = db.Countries.ToList();
            return View(region);
        }

        // GET: regions/Delete/5
        public async Task<ActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            region region = await db.regions.FindAsync(id);
            if (region == null)
            {
                return NotFound();
            }
            return View(region);
        }

        // POST: regions/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> DeleteConfirmed(int id)
        {
            region region = await db.regions.FindAsync(id);
            db.regions.Remove(region);
            await db.SaveChangesAsync();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}


