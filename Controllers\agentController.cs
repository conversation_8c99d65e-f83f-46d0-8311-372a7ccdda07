using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc.Rendering;
using Newtonsoft.Json;
using the lang
                               Text = _kasvolServices.getvalue(lang, c.cityname)
                           }).ToList();
using (var context = new kasvoldb())
            {
                var agentedit = await context.orders
                    .Where(d => d.OrderId == order.OrderId)
                    .SingleOrDefaultAsync();
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using static kasvol.Controllers.OrdersController;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var context = new kasvoldb())
                {
                    var agentedit = await context.orders
                        .Where(d => d.OrderId == order.OrderId)
                        .SingleOrDefaultAsync();
using System.Linq;
using the lang
                                Text = _kasvolServices.getvalue(lang, r.regionname)
                            }).ToList();
using System;

namespace kasvol.Controllers
{
    [Authorize(Roles = "Agents,SuperAdmin,Sales Manager")]
    public class agentController : BaseController
    {
        private readonly kasvolservices _kasvolServices;
        private readonly kasvoldb _db;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public agentController(kasvolservices kasvolServices, kasvoldb db, UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor)
        {
            _kasvolServices = kasvolServices;
            _db = db;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
        }
        // GET: agent
        public ActionResult Index(string word, int? take)
        {
            string userid = _userManager.GetUserId(User);
         
            string agentid = _kasvolServices.agentid(userid);
            int kk = 50;
            if (take != null)
            {
                kk = take ?? 50;
            }
            if (Request.Cookies.TryGetValue("CurrentLanguage", out string cookieValue))
            {
                if (cookieValue == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (cookieValue == "en-En")
                {
                    ViewBag.ar = "English.json";
                }
                else if (cookieValue == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }
            }
            var posts = _db.orders.Where(a=>a.agentid==agentid);
            if (word != null && take == null) { return View(posts.Where(a => (a.CompanyName.Contains(word) || a.Phone.Contains(word) || a.Email.Contains(word))).OrderByDescending(a => a.OrderId).Take(kk).ToList()); }
            else if (word == null && take != null) { return View(posts.OrderByDescending(a => a.OrderId).Take(kk).ToList()); }
            else if (word != null && take != null)
            { return View(posts.Where(a => (a.CompanyName.Contains(word) || a.Phone.Contains(word) || a.Email.Contains(word))).OrderByDescending(a => a.OrderId).Take(kk).ToList()); }
            else if (take == 0)
            {
                return View(posts.OrderByDescending(a => a.OrderId).ToList());
            }
            else
            {
                return View(posts.OrderByDescending(a => a.OrderId).Take(kk).ToList());
            }
        }
        public ActionResult Create()
        {
            ViewBag.countryid = _db.Countries.ToList();
            ViewBag.regionid = _db.regions.ToList();
            ViewBag.cityid = _db.cities.ToList();
            string userid = _userManager.GetUserId(User);

            string agentid = _kasvolServices.agentid(userid);
            ViewBag.agentrole = _kasvolServices.agentrole(userid);

            return View();
        }
        [HttpGet]
        public JsonResult GetRegions(int countryId, string lang)
        {
            var regions = _db.regions
                            .Where(r => r.countryid == countryId)
                            .Select(r => new SelectListItem
                            {
                                Value = r.regionid.ToString(),
                                // Fetch the translated region name using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc.Rendering;
using Newtonsoft.Json;
using the lang
                               Text = _kasvolServices.getvalue(lang, c.cityname)
                           }).ToList();
using (var context = new kasvoldb())
            {
                var agentedit = await context.orders
                    .Where(d => d.OrderId == order.OrderId)
                    .SingleOrDefaultAsync();
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using static kasvol.Controllers.OrdersController;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var context = new kasvoldb())
                {
                    var agentedit = await context.orders
                        .Where(d => d.OrderId == order.OrderId)
                        .SingleOrDefaultAsync();
using System.Linq;
using the lang
                                Text = _kasvolServices.getvalue(lang, r.regionname)
                            }).ToList();
using System;

return Json(regions);
        }

        // Action to get cities by region ID, and use lang for translation
        [HttpGet]
        public JsonResult GetCities(int regionId, string lang)
        {
            var cities = _db.cities
                           .Where(c => c.regionid == regionId)
                           .Select(c => new SelectListItem
                           {
                               Value = c.cityid.ToString(),
                               // Fetch the translated city name using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc.Rendering;
using Newtonsoft.Json;
using the lang
                               Text = _kasvolServices.getvalue(lang, c.cityname)
                           }).ToList();
using (var context = new kasvoldb())
            {
                var agentedit = await context.orders
                    .Where(d => d.OrderId == order.OrderId)
                    .SingleOrDefaultAsync();
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using static kasvol.Controllers.OrdersController;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var context = new kasvoldb())
                {
                    var agentedit = await context.orders
                        .Where(d => d.OrderId == order.OrderId)
                        .SingleOrDefaultAsync();
using System.Linq;
using the lang
                                Text = _kasvolServices.getvalue(lang, r.regionname)
                            }).ToList();
using System;

return Json(cities);
        }
        public ActionResult addproducts(string id)
        {
            string userid = _userManager.GetUserId(User);

            string agentid = _kasvolServices.agentid(userid);
            ViewBag.agentrole = _kasvolServices.agentrole(userid);
            ViewBag.id = id;
            var products = _db.products.Where(a => a.lang == "ar").ToList();
            return View(products);
        }
        [HttpPost]
        public async Task<ActionResult> AddToOrder(string products, string ordercode, string sentence, bool? pwithvt=false, bool? pwithtrella = false, bool? pwithtrellavt = false, bool? pwithlo = false, bool? pwithlovt = false, bool? istotal = false,  double? totaltrilla = 0, double? totalloading = 0)
        { 
            var productList = JsonConvert.DeserializeObject<List<ProductOrderModel>>(products);
         
            string userid = _userManager.GetUserId(User);

            string agentid = _kasvolServices.agentid(userid);
            string agentrole = _kasvolServices.agentrole(userid);
           
            bool isneedapproved = false;
            bool isapproved = false;
            if (agentrole == "1")
            {
                isneedapproved = false;
                isapproved = true;
            }
            else
            {
                foreach (var productOrder in productList)
                {
                    var product = await _db.products.FindAsync(productOrder.Id);

                    if (productOrder.NewPrice == product.UnitPrice)
                    {
                        isneedapproved = false;
                        isapproved = true;
                    }
                    else {
                        isneedapproved = true;
                        break;
                    }
                }
            }

            if (productList == null || !productList.Any())
            {
                // Handle the case where no products are selected
                TempData["Message"] = Resources.Resource.String204;
                return RedirectToAction("addproducts"); // Redirect to a summary or appropriate page
            }

            // Create an order and add selected products
            var order = await _db.orders.FirstAsync(a => a.kasvolcode == ordercode);
            if (istotal.HasValue && istotal.Value)
            { 
                using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc.Rendering;
using Newtonsoft.Json;
using the lang
                               Text = _kasvolServices.getvalue(lang, c.cityname)
                           }).ToList();
using (var context = new kasvoldb())
            {
                var agentedit = await context.orders
                    .Where(d => d.OrderId == order.OrderId)
                    .SingleOrDefaultAsync();
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using static kasvol.Controllers.OrdersController;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var context = new kasvoldb())
                {
                    var agentedit = await context.orders
                        .Where(d => d.OrderId == order.OrderId)
                        .SingleOrDefaultAsync();
using System.Linq;
using the lang
                                Text = _kasvolServices.getvalue(lang, r.regionname)
                            }).ToList();
using System;

agentedit.shoppingvt = pwithtrellavt ?? false;
                    agentedit.droppingvt = pwithlovt ?? false;
                    agentedit.withvt = pwithvt ?? false;
                    agentedit.isjoint = istotal ?? false;  // Assuming you want to treat null as false
                    agentedit.shopping = totaltrilla;
                    agentedit.dropping = totalloading;
                    agentedit.isshopping = pwithtrella ?? false;
                    agentedit.isdropping = pwithlo ?? false;
                    agentedit.sentence = sentence;
                    agentedit.isapproved = isapproved;
                    agentedit.isneedapproved = isneedapproved;
                    await context.SaveChangesAsync();
                }
            }
            else
            {
                using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc.Rendering;
using Newtonsoft.Json;
using the lang
                               Text = _kasvolServices.getvalue(lang, c.cityname)
                           }).ToList();
using (var context = new kasvoldb())
            {
                var agentedit = await context.orders
                    .Where(d => d.OrderId == order.OrderId)
                    .SingleOrDefaultAsync();
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using static kasvol.Controllers.OrdersController;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var context = new kasvoldb())
                {
                    var agentedit = await context.orders
                        .Where(d => d.OrderId == order.OrderId)
                        .SingleOrDefaultAsync();
using System.Linq;
using the lang
                                Text = _kasvolServices.getvalue(lang, r.regionname)
                            }).ToList();
using System;

agentedit.shoppingvt = pwithtrellavt ?? false;  // Default to false if pwithtrellavt is null
                    agentedit.sentence = sentence;
                    agentedit.droppingvt = pwithlovt ?? false;  // Default to false if pwithlovt is null
                    agentedit.withvt = pwithvt ?? false;  // Default to false if pwithvt is null
                    agentedit.isshopping = pwithtrella ?? false;  // Default to false if pwithtrella is null
                    agentedit.isdropping = pwithlo ?? false;  // Default to false if pwithlo is null
                    agentedit.isapproved = isapproved;
                    agentedit.isneedapproved = isneedapproved;
                    await context.SaveChangesAsync();
                }
            }

            foreach (var productOrder in productList)
            {
                var product = await _db.products.FindAsync(productOrder.Id);
                OrderDetail yy = new OrderDetail
                {
                    ProductId = productOrder.Id,
                    UnitPrice = product.UnitPrice,
                    Quantity = productOrder.Quantity,
                    newUnitPrice = productOrder.NewPrice,
                    shopping = productOrder.Trilla,
                    dropping = productOrder.Loading,
                    OrderId = order.OrderId,
                    ordercode = ordercode,
                    user = _userManager.GetUserId(User),
                    IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString()
                };
                _db.orderDetails.Add(yy);
            }
            await _db.SaveChangesAsync();

            TempData["Message"] = "Done...!";
            return RedirectToAction("Index"); // Redirect to the order summary page
        }
        [HttpPost]
        public async Task<JsonResult> cancelorder(string id)
        {
            Order order = await _db.orders.FirstAsync(a => a.kasvolcode == id);
            using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc.Rendering;
using Newtonsoft.Json;
using the lang
                               Text = _kasvolServices.getvalue(lang, c.cityname)
                           }).ToList();
using (var context = new kasvoldb())
            {
                var agentedit = await context.orders
                    .Where(d => d.OrderId == order.OrderId)
                    .SingleOrDefaultAsync();
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using static kasvol.Controllers.OrdersController;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var context = new kasvoldb())
                {
                    var agentedit = await context.orders
                        .Where(d => d.OrderId == order.OrderId)
                        .SingleOrDefaultAsync();
using System.Linq;
using the lang
                                Text = _kasvolServices.getvalue(lang, r.regionname)
                            }).ToList();
using System;

agentedit.HasBeenShipped = true;

                await context.SaveChangesAsync();
            }
            // Perform your logic here (e.g., saving data, processing, etc.)
            bool success = true; // Example condition for success (you can replace with actual logic)

            if (success)
            {
                return Json(new { success = true, message = "Action performed successfully!" });
            }
            else
            {
                return Json(new { success = false, message = "Something went wrong, please try again!" });
            }
        }
        public async Task<ActionResult> offerprice(string id)
        {
            Order order = await _db.orders.FirstAsync(a => a.kasvolcode == id);
            List<OrderDetail> plist = await _db.orderDetails.Where(a => a.ordercode == id).ToListAsync();
            foreach (var item in plist)
            {
                if (item.newUnitPrice == null)
                {
                    item.newUnitPrice = item.UnitPrice;
                }
            }
                double? totaling = 0;
            if (order.isjoint)
            {
                foreach (var item in plist)
                {
                
                    double? tprice = item.newUnitPrice;
                    item.ogimage = "(" + item.Product.length + "X" + item.Product.width + "X" + item.Product.height + ")";
                    item.ogdescription = _kasvolServices.getimage(item.Product.guid);
                    if (order.withvt)
                    {
                        tprice = Math.Round((double)(item.newUnitPrice + (item.newUnitPrice * _kasvolServices.tax())), 2);


                    }
                    if (order.shopping != 0)
                    {
                        tprice = tprice + (Math.Round((double)(order.shopping / item.Quantity), 2));

                    }
                    if (order.dropping != 0)
                    {
                        tprice = tprice + (Math.Round((double)(order.dropping / item.Quantity), 2));

                    }
                    item.newUnitPrice = tprice;
                    item.UnitPrice = Math.Round((double)(tprice * item.Quantity));
                    totaling += item.UnitPrice;
                }


            }
            else
            {
                foreach (var item in plist)
                {
               
                    double? tprice = item.newUnitPrice;
                    item.ogimage = "(" + item.Product.length + "X" + item.Product.width + "X" + item.Product.height + ")";
                    item.ogdescription = _kasvolServices.getimage(item.Product.guid);
                    if (order.withvt)
                    {
                        tprice = Math.Round((double)(item.newUnitPrice + (item.newUnitPrice * _kasvolServices.tax())), 2);


                    }
                    if (order.isshopping)
                    {
                        tprice = tprice + (Math.Round((double)(item.shopping / item.Quantity), 2));
                        if (order.shoppingvt)
                        {
                            tprice = tprice + Math.Round((double)((item.shopping * _kasvolServices.tax()) / item.Quantity), 2);


                        }

                    }
                    if (order.isdropping)
                    {
                        tprice = tprice + (Math.Round((double)(item.dropping / item.Quantity), 2));
                        if (order.droppingvt)
                        {
                            tprice = tprice + Math.Round((double)((item.shopping * _kasvolServices.tax()) / item.Quantity), 2);


                        }

                    }
                    item.newUnitPrice = tprice;
                    item.UnitPrice = Math.Round((double)(tprice * item.Quantity));
                    totaling += item.UnitPrice;
                }

            }
            ViewBag.total = totaling;
            ViewBag.plist = plist;
            return View(order);
        }
        // POST: Orders/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Create([Bind("OrderId,kasvolcode,userid,FirstName,LastName,CompanyName,Phone,Email,Total,PaymentTransactionId,HasBeenShipped,businessyear,address,issold,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage,countryid,regionid,cityid,agentid")] Order order)
        {
            string userid = _userManager.GetUserId(User);

            string agentid = _kasvolServices.agentid(userid);
            order.agentid = agentid;
            order.IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();
            order.user = _userManager.GetUserId(User);
            if (ModelState.IsValid)
            {
                _db.orders.Add(order);
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }

            return View(order);
        }
   
    }
    
}
