using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using System;
using kasvol.service;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using Microsoft.AspNetCore.Identity;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class citiesController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;

        public citiesController(UserManager<ApplicationUser> userManager)
        {
            _userManager = userManager;
        }


        private kasvoldb db = new kasvoldb();

        // GET: cities
        public async Task<ActionResult> Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            var cities = db.cities.Include(c => c.Region);
            return View(await cities.ToListAsync());
        }

        // GET: cities/Details/5
        public async Task<ActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            city city = await db.cities.FindAsync(id);
            if (city == null)
            {
                return NotFound();
            }
            return View(city);
        }

        // GET: cities/Create
        public ActionResult Create()
        {
            ViewBag.regionid = db.regions.ToList();
       
            return View();
        }

        // POST: cities/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Create([Bind("cityid,cityname,regionid,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] city city)
        {
            kasvolservices t = new kasvolservices();
            city.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            city.user = _userManager.GetUserId(User);
            city.cityname = t.setvalue("ar", city.cityname);
            if (ModelState.IsValid)
            {
                db.cities.Add(city);
                await db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            ViewBag.regionid = db.regions.ToList();
            return View(city);
        }

        // GET: cities/Edit/5
        public async Task<ActionResult> Edit(int? id, string lang)
        {
            if (lang != null)
            {
                ViewBag.lang = lang;
            }
            if (id == null)
            {
                return BadRequest();
            }
            city city = await db.cities.FindAsync(id);
            if (city == null)
            {
                return NotFound();
            }
            ViewBag.regionid = db.regions.ToList();
            return View(city);
        }

        // POST: cities/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Edit([Bind("cityid,cityname,regionid,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] city city)
        {
            kasvolservices t = new kasvolservices();
            city.cityname = t.updatevalue(city.lang, city.cityname, city.oldvalue);

            city.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();
            if (ModelState.IsValid)
            {
                db.Entry(city).State = EntityState.Modified;
                await db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            ViewBag.regionid = db.regions.ToList();
            return View(city);
        }

        // GET: cities/Delete/5
        public async Task<ActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            city city = await db.cities.FindAsync(id);
            if (city == null)
            {
                return NotFound();
            }
            return View(city);
        }

        // POST: cities/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> DeleteConfirmed(int id)
        {
            city city = await db.cities.FindAsync(id);
            db.cities.Remove(city);
            await db.SaveChangesAsync();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}


