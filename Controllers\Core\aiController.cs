using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using System;
using kasvol.service;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers.Core
{
    [Authorize]
    public class aiController : BaseController
    {
        private readonly kasvoldb _db;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public aiController(kasvoldb db, IConfiguration configuration, kasvolservices kasvolServices, IHttpContextAccessor httpContextAccessor)
            : base(db, configuration, kasvolServices)
        {
            _db = db ?? throw new ArgumentNullException(nameof(db));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        }

        // GET: ai
        public async Task<IActionResult> Index()
        {
            // Example of proper async pattern
            // You can add any data retrieval or processing here
            var currentUser = _httpContextAccessor.HttpContext?.User?.Identity?.Name;
            ViewData["CurrentUser"] = currentUser;
            
            return View();
        }

        // Example of error handling pattern
        [HttpGet("error")]
        public IActionResult Error()
        {
            return View();
        }
    }
}
