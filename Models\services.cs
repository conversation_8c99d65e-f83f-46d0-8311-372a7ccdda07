using kasvol.Models;
using System.ComponentModel.DataAnnotations;

namespace kasvol.Models
{
    public class OurServices : Baseclass
    {[Key]
        public int servicesid { get; set; }
        public string title { get; set; }
        [DataType(DataType.MultilineText)]
        public string content { get; set; }
        public string slogan { get; set; }
        public int numofvisit { get; set; } = 0;
        public string auther { get; set; }
        public bool publish { get; set; }
        public bool allowcomments { get; set; }
        public bool allowwhatsapp { get; set; }
        public int service_itemsid { get; set; }
        public virtual service_items Service_Items { get; set; }

    }
}

