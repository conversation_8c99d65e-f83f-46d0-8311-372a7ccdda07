using Google.Apis.Auth.OAuth2;
using Google.Apis.Drive.v3;
using Google.Apis.Services;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Google.Apis.Upload;
using System;
using System.Threading;
using Google.Apis.Drive.v3.Data;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;

public class GoogleDriveService
{
    private DriveService _driveService;
    private readonly IWebHostEnvironment _webHostEnvironment;

    public GoogleDriveService(IWebHostEnvironment webHostEnvironment)
    {
        _webHostEnvironment = webHostEnvironment;
        string appDataPath = Path.Combine(_webHostEnvironment.ContentRootPath, "App_Data");
        string GoogleDriveServiceAccountKeyFilePath = Path.Combine(appDataPath, "kasvoldec.json");

      

        // تكوين خيارات المصادقة باستخدام ملف JSON لحساب الخدمة
        GoogleCredential credential;
        using (var stream = new FileStream(GoogleDriveServiceAccountKeyFilePath, FileMode.Open, FileAccess.Read))
        {
            credential = GoogleCredential.FromStream(stream)
                .CreateScoped(DriveService.ScopeConstants.Drive);
        }

        // إعداد خدمة الوصول إلى Google Drive
        _driveService = new DriveService(new BaseClientService.Initializer()
        {
            HttpClientInitializer = credential,
            ApplicationName = "Your Application Name"
        });
    }

    public async Task<string> CreateFolder(string folderName, string parentFolderId)
    {
        var folderMetadata = new Google.Apis.Drive.v3.Data.File()
        {
            Name = folderName,
            MimeType = "application/vnd.google-apps.folder",
            Parents = new List<string> { parentFolderId }
        };
        try
        {
            var folderRequest = _driveService.Files.Create(folderMetadata);
        folderRequest.Fields = "id";
        var folder = await folderRequest.ExecuteAsync();

        return folder.Id;
        }
        catch (Exception ex)
        {
            string errorMessage = ex.ToString();
            Console.WriteLine(errorMessage);
            throw;
        }
    }


public async Task<string> UploadFileToFolder(string fileUrl, string folderId)
{
    var fileMetadata = new Google.Apis.Drive.v3.Data.File()
    {
        Name = Path.GetFileName(fileUrl),
        Parents = new List<string> { folderId }
    };
        try
        {
            using (var stream = new FileStream(fileUrl, FileMode.Open))
            {
                var uploadRequest = _driveService.Files.Create(fileMetadata, stream, "application/octet-stream");
                uploadRequest.Fields = "id";

                var file = await uploadRequest.UploadAsync();

                var uploadedFile = uploadRequest.ResponseBody;
                return uploadRequest.ResponseBody.Id;
            }
        }
        catch (Exception ex)
        {
            string errorMessage = ex.ToString();
            Console.WriteLine(errorMessage);
            throw;
        }
    }
    public async Task<string> UploadFileToGoogleDrive(string filePath, string fileNameWithExtension, string folderLocationId, CancellationToken cancellationToken)
    {
        try
        {
            // RefreshTokenIfExpired();
            //Upload
            var fileMetadata = new Google.Apis.Drive.v3.Data.File()
            {
                Name = fileNameWithExtension,
            };

            //Check if the user wants to upload the file to a specific folder
            if (string.IsNullOrWhiteSpace(folderLocationId) == false)
            {
                fileMetadata.Parents = new List<string>
                {
                    folderLocationId
                };
            }




            using (var stream = new System.IO.FileStream(filePath, System.IO.FileMode.Open))
            {
                string extension = System.IO.Path.GetExtension(filePath);

                //Change the content type to match the file type
                string contentType = GetContentType(Path.GetExtension(filePath).ToLower());

                var request = _driveService.Files.Create(fileMetadata, stream, contentType);

                request.Fields = "id";
                await request.UploadAsync(cancellationToken);

                var uploadedFile = request.ResponseBody;
                return request.ResponseBody?.Id;
            }
        }
        catch (Exception ex)
        {
            string errorMessage = ex.ToString();
            Console.WriteLine(errorMessage);
            throw;
        }
    }
    private string GetContentType(string extension)
    {
        string contentType = extension switch
        {
            ".jpg" => "image/jpg",
            ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".bmp" => "image/bmp",
            ".tiff" => "image/tiff",
            ".txt" => "text/plain",
            ".pdf" => "application/pdf",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".xls" => "application/vnd.ms-excel",
            ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".rar" => "application/vnd.rar",
            ".zip" => "application/zip",
            ".ppt" => "application/vnd.ms-powerpoint",
            ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            ".htm" => "text/html",
            ".html" => "text/html",
            _ => throw new NotImplementedException(),
        };

        return contentType;
    }
    public async Task<string> CreateFolderInGoogleDrive(string folderName, string folderLocationId, CancellationToken cancellationToken, string serviceAccountKeyFilePath)
    {
        // RefreshTokenIfExpired();

        try
        {
  

            var fileMetadata = new Google.Apis.Drive.v3.Data.File()
            {
                Name = folderName,
                MimeType = "application/vnd.google-apps.folder"
            };

            //Check if the user wants to create the folder in a specific folder
            if (string.IsNullOrWhiteSpace(folderLocationId) == false)
            {
                fileMetadata.Parents = new List<string>
                {
                    folderLocationId
                };
            }

            FilesResource.CreateRequest request;

            request = _driveService.Files.Create(fileMetadata);
            request.Fields = "id";
            var folder = await request.ExecuteAsync();

            // تحديث صلاحية المجلد للقراءة فقط
            var permission = new Google.Apis.Drive.v3.Data.Permission
            {
                Type = "anyone",
                Role = "reader",
            };

            var permissionRequest = _driveService.Permissions.Create(permission, folder.Id);
            permissionRequest.Fields = "id";
            await permissionRequest.ExecuteAsync();
            return folder.Id;
        }
        catch (Exception ex)
        {
            string errorMessage = ex.ToString();
            Console.WriteLine(errorMessage);
            throw;
        }
    }
    public void EditFolderPermission(string role, string roleType, string fileId)
    {
        Permission permission = new()
        {
            Role = role,
            Type = roleType,
        };

        _driveService.Permissions.Update(permission, fileId, permission.Id);
    }
}



