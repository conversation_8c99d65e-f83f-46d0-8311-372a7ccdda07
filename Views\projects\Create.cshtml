@model kasvol.Models.Projects

@{
    ViewBag.Title = "Create";
    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}

<div id="progress" class="modal1">
    <div class="throbber">
        <div class="curtain">
        </div>
        <div class="curtain-content">
            <div>

                <div id="preloader">
                    <div id="loader"></div>
                </div>
            </div>
        </div>
    </div>
</div><div class="card card-custom">
    <div class="card-header flex-wrap py-5">
        <div class="card-title">
            <h3 class="card-label">
                @Resources.Resource.String52

            </h3>
        </div>


    </div>
    <div class="card-body" style="margin-bottom:125px">
        <h2 class="m--font-danger m--align-center"></h2>
        <div class="row">
            <div class="col-md-12">

                @using (Ajax.BeginForm("Create", "projects",
                                               new AjaxOptions
                                               {
                                                   OnSuccess = "OnSuccess",
                                                   OnFailure = "OnFailure",
                                                   OnBegin = "onLoginBegin",
                                                   LoadingElementId = "progress"
                                               }))
                {@Html.AntiForgeryToken()


                <div class="form-horizontal">

                    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                    <div class="form-group m-form__group row m-form__group row">
                        @Html.LabelFor(model => model.lang, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            <select class="form-control" id="lang" name="lang" required>
                                <option disabled value="">@Resources.Resource.String15  </option>
                                <option selected value="ar">AR</option>
                                <option value="en">EN</option>

                            </select>
                            @Html.ValidationMessageFor(model => model.lang, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group m-form__group row">
                         <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String4 </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.title, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.title, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String16 </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.slogan, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.slogan, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group m-form__group row">
                         <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String17 </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.oldslogan, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.oldslogan, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group m-form__group row">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String91 </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.auther, new { htmlAttributes = new { @class = "form-control", @Value = "kasvol" } })
                            @Html.ValidationMessageFor(model => model.auther, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group m-form__group row">
                         <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String18 </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.content, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.content, "", new { @class = "text-danger" })
                        </div>
                    </div>



                    <div class="form-group m-form__group row">

                        <input id="searchbox" type="text" placeholder="Search Box" class="form-control"><br />
                        <div id="map-canvas" style="width: 100%; height: 480px;"></div>
                    </div>
                    <div class="form-group m-form__group row ">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String92 </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.Latitude, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.Latitude, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row ">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String93 </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.Longitude, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.Longitude, "", new { @class = "text-danger" })
                        </div>
                    </div>



                    <div class="form-group m-form__group row">
                        <div class="col-md-12 " style="text-align:center">
                            <div class="checkbox checkbox-success">
                                <label style="margin:0 auto">
                                    @Html.EditorFor(model => model.publish)
                                    @Html.ValidationMessageFor(model => model.publish, "", new { @class = "text-danger" })
                                    <span class="lbl">@Resources.Resource.String88</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group m-form__group row">
                        <div class="col-md-12 " style="text-align:center">
                            <div class="checkbox checkbox-success">
                                <label style="margin:0 auto">
                                    @Html.EditorFor(model => model.allowcomments)
                                    @Html.ValidationMessageFor(model => model.allowcomments, "", new { @class = "text-danger" })
                                    <span class="lbl">@Resources.Resource.String90</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group m-form__group row">
                        <div class="col-md-12 " style="text-align:center">
                            <div class="checkbox checkbox-success">
                                <label style="margin:0 auto">
                                    @Html.EditorFor(model => model.allowwhatsapp)
                                    @Html.ValidationMessageFor(model => model.allowwhatsapp, "", new { @class = "text-danger" })
                                    <span class="lbl">@Resources.Resource.String89</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group m-form__group row">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String87</label>


                        <div class="col-sm-12">

                            <select class="form-control" name="project_Categoriesid" required>
                                <option selected disabled value="">@Resources.Resource.String15  </option>
                                @using kasvol.service;

                                @foreach (var item in ViewBag.project_Categories)
                                {
                                    kasvolservices t = new kasvolservices();


                                    string value = t.getvalue(ViewBag.lang, item.title);



                                    <option value="@item.project_categoriesid">@value</option>
                                }
                            </select>
                        </div>
                    </div>

                    <div class="form-group m-form__group row m-form__group row d-none">
                        @Html.LabelFor(model => model.arrange, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.arrange, new { htmlAttributes = new { @class = "form-control", @Value = 0 } })
                            @Html.ValidationMessageFor(model => model.arrange, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row d-none">
                        @Html.LabelFor(model => model.datecreated, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.datecreated, new { htmlAttributes = new { @class = "form-control", @Value = System.DateTime.Now.ToString("yyyy-MM-dd hh:mm") } })
                            @Html.ValidationMessageFor(model => model.datecreated, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row d-none">
                        @Html.LabelFor(model => model.datemodified, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.datemodified, new { htmlAttributes = new { @class = "form-control", @Value = System.DateTime.Now.ToString("yyyy-MM-dd hh:mm") } })
                            @Html.ValidationMessageFor(model => model.datemodified, "", new { @class = "text-danger" })
                        </div>
                    </div>


                    <div class="form-group m-form__group row d-none">
                        @Html.LabelFor(model => model.IP, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.IP, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.IP, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row d-none">
                        @Html.LabelFor(model => model.modifiedIP, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.modifiedIP, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.modifiedIP, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row d-none">
                        @Html.LabelFor(model => model.user, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.user, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.user, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row">
                         <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String5 </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.year, new { htmlAttributes = new { @class = "form-control", @Value = System.DateTime.Now.Year.ToString() } })
                            @Html.ValidationMessageFor(model => model.year, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row d-none">
                        @Html.LabelFor(model => model.guid, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.guid, new { htmlAttributes = new { @class = "form-control", @Value = Guid.NewGuid().ToString() } })
                            @Html.ValidationMessageFor(model => model.guid, "", new { @class = "text-danger" })
                        </div>
                    </div>



                    <div class="form-group m-form__group row">
                        @Html.LabelFor(model => model.ogtitle, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.ogtitle, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.ogtitle, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row">
                        @Html.LabelFor(model => model.ogdescription, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.ogdescription, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.ogdescription, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row">
                        @Html.LabelFor(model => model.keywords, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.keywords, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.keywords, "", new { @class = "text-danger" })
                        </div>
                    </div>




                    <div class="form-group m-form__group">
                        <div class=" col-md-12" style="text-align:center">
                            <input type="submit"  value="@Resources.Resource.String2" class="btn btn-info" />
                        </div>
                    </div>
                </div>
            }



                <div class=" col-md-12" style="text-align:center">
                    @Html.ActionLink("Back to List", "Index")
                </div>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" type="text/css" href="~/smart/css/smart-forms.css">
<link rel="stylesheet" type="text/css" href="~/smart/css/smart-addons.css">


@section Scripts {
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCGqxy5of7UFCbALocsTnizqWD27hZZXGU&callback=initAutocomplete&libraries=places&v=weekly&channel=2"></script>
    <script>
    function initialize() {

    var map = new google.maps.Map(document.getElementById('map-canvas'), {
    mapTypeId: google.maps.MapTypeId.ROADMAP
    });



    var input = document.getElementById('searchbox');
  

    var searchBox = new google.maps.places.SearchBox(input);


    google.maps.event.addListener(searchBox, 'places_changed', function() {
    var places = searchBox.getPlaces();

    if (places.length == 0) {
    return;
    }

    //get first place
    var place = places[0];

    var marker = new google.maps.Marker({
        map: map,
        draggable: true,
        animation: google.maps.Animation.DROP,
    title: place.name,
    position: place.geometry.location
    });
        google.maps.event.addListener(marker, 'dragend', function () {
            document.getElementsByName('Latitude')[0].value = marker.getPosition().lat();
            document.getElementsByName('Longitude')[0].value = marker.getPosition().lng();
      
        });

    //var bounds = new google.maps.LatLngBounds();
    //bounds.extend(place.geometry.location);
    //map.fitBounds(bounds);

    map.fitBounds(place.geometry.viewport);


    //save location goes here...
    var lat = place.geometry.location.lat();
    var lng = place.geometry.location.lng();
        document.getElementById('Latitude').value = (lat);
        document.getElementById('Longitude').value = (lng);


    });
    }
    initialize();</script>
    <script type="text/javascript">
        $('#title').keyup(function () {
            document.getElementById("slogan").value = document.getElementById("title").value.replace(/ /g, "-");
            document.getElementById("oldslogan").value = document.getElementById("title").value.replace(/ /g, "-");
            document.getElementById("ogtitle").value = document.getElementById("title").value;


        });

    </script>
    <script src="/dashboard/assets/js/pages/crud/forms/widgets/tagify.js"></script>
    <script type="text/javascript">
        var input = document.querySelector('input[name=keywords]');

        // initialize Tagify on the above input node reference
        new Tagify(input)
    </script>
    <script type="text/javascript">
        function OnSuccess() {
            location.href = '/projects/Index';
        }
        function OnFailure() {
            alert("Programmer will know this error ");
            $('#progress').hide();
        }

    </script>
    <script type="text/javascript">

        jQuery(document).ready(function ($) {

            $('#form0').validate();


        });

    </script>
    <script type="text/javascript">
        $("#form0").on("submit", function (event) {



            if ($('#form0').valid()) {
                $('#progress').show();
            }
        });

    </script>

    <script type="text/javascript" src="~/smart/js/jquery.validate.min.js"></script>
    <script type="text/javascript" src="~/smart/js/additional-methods.min.js"></script>
    <script src="/dashboard/assets/plugins/custom/tinymce/tinymce.bundle.js"></script>
    <!--end::Page Vendors-->
    <!--begin::Page Scripts(used by this page)-->
    <script src="/dashboard/assets/js/pages/crud/forms/editors/tinymce.js"></script>
}
