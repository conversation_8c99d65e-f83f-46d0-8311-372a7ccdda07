@model kasvol.Models.Sliders

@{
    ViewBag.Title = @Resources.Resource.String106;
    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}



<div id="progress" class="modal1">
    <div class="throbber">
        <div class="curtain">
        </div>
        <div class="curtain-content">
            <div>

                <div id="preloader">
                    <div id="loader"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12" style="text-align:center">

        <h1 style="color:#ff0000">
            @Resources.Resource.String106
        </h1>


        <div class="m-portlet" style="margin:0 auto">
            <div class="m-portlet__head">
                <div class="m-portlet__head-caption">



                    <h2 style="text-align:center;margin:0 auto">@Resources.Resource.String302</h2>

                </div>
            </div>
            <div class="m-portlet__body">

                <!--begin::Section-->
                <div class="m-section">
                    <dl class="dl-horizontal">
                        <dt>
                            @Html.Label(@Resources.Resource.String4)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.title)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.subtitle)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.subtitle)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.btntitle)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.btntitle)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.link)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.link)
                        </dd>

                        <dt>
                            @Html.Label(@Resources.Resource.String83)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.arrange)
                        </dd>

                        <dt>
                            @Html.Label(@Resources.Resource.String330)
                        </dt>

                        <dd>
                            @Model.datecreated.ToString("yyyy/MM/dd HH:mm")
                        </dd>
                        <dt>
                            @Html.Label(@Resources.Resource.String331)
                        </dt>

                        <dd>
                            @Model.datemodified.ToString("yyyy/MM/dd HH:mm")
                        </dd>


                    </dl>

                    @using (Ajax.BeginForm("Delete", "references",
                new AjaxOptions
                {
                    OnSuccess = "OnSuccess",
                    OnFailure = "OnFailure",
                    LoadingElementId = "progress"
                }))
                    {@Html.AntiForgeryToken()

                    <div class="form-actions no-color" style="text-align:center">
                        <input type="submit" value="@Resources.Resource.String308" class="btn btn-info" />
                        <br />
                        <br />
                        <div class=" col-md-12" style="text-align:center">
                            @Html.ActionLink(@Resources.Resource.String265, "Index")
                        </div>
                    </div>
                }
                </div>

                <!--end::Section-->
                <div class="m-separator m-separator--dashed"></div>
            </div>
        </div>


    </div>
</div>
<br />
<br />
<br />
<br />
<br />
<br />
@section Scripts {
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script src="~/Scripts/jquery.unobtrusive-ajax.min.js"></script>
    <script type="text/javascript">
        function OnSuccess() {
            location.href = '/references/Index';
        }
        function OnFailure() {
            alert("??? ??? ???? ?????? ???? ??????");
        }
    </script>

    <script src="~/Scripts/jquery.validate.js"></script>
}

