using (var context = new kasvoldb())
        //                {
        //                    var useredit = (from d in context.documents
        //                                    where d.guid == page.guid
        //                                    select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media);
using System.Collections.Generic;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Linq;
using System.Threading;
using (var context = new kasvoldb())
            {
                var useredit = (from d in context.documents
                                where d.guid == leadId && d.arrange == 2
                                select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media1);
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using System.IO;
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using SixLabors.ImageSharp;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.categories
                                 where d.CategoryID == postcat.CategoryID
                                 select d).Single();
using System.Threading.Tasks;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using System.Data;
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

namespace kasvol.Controllers
{
    [Authorize]
    public class PCategoriesController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;


                private readonly IWebHostEnvironment _webHostEnvironment;
//private GoogleDriveService _googleDriveService;

        //public PCategoriesController(, UserManager<ApplicationUser> userManager, IWebHostEnvironment webHostEnvironment)
        //{
        //    _googleDriveService = new GoogleDriveService();
        //}
        private kasvoldb db = new kasvoldb();

        // GET: postcats
        public ActionResult Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            return View(db.categories.ToList());
        }
        public async Task<ActionResult> uploadv(int? id)
        {
            kasvolservices t = new kasvolservices(); if (id == null)
            {
                return BadRequest();
            }
            Category page = await db.categories.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            return View(page);
        }
        //public async Task<ActionResult> uploadpdf(int? id)
        //{
        //    kasvolservices t = new kasvolservices(); if (id == null)
        //    {
        //        return BadRequest();
        //    }
        //    Category page = await db.categories.FindAsync(id);
        //    if (page == null)
        //    {
        //        return NotFound();
        //    }

        //    return View(page);
        //}
        private CancellationTokenSource _cancellationTokenSource = new();
        [HttpPost]
        //public async Task<ActionResult> uploadpdf(int? id, IFormFile ImageFile)
        //{
        //    kasvolservices t = new kasvolservices();
        //    postcat page = await db.postcats.FindAsync(id);

        //    string path = Server.MapPath("~/kasvolfactory/vi/category/" + page.postcatid + "/pdf/");


        //    if (!Directory.Exists(path))
        //    {
        //        Directory.CreateDirectory(path);
        //    }


        //    if (ImageFile != null)
        //    {
        //        try
        //        {
        //            string normalImagePath = System.IO.Path.Combine(path, ImageFile.FileName);
        //            string webPFileName = System.IO.Path.GetFileNameWithoutExtension(ImageFile.FileName) + ".pdf";
        //            string webPImagePath = System.IO.Path.Combine(path, webPFileName);
        //            // using (var context = new kasvoldb())
        //                {
        //                    var useredit = (from d in context.documents
        //                                    where d.guid == page.guid
        //                                    select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media);
using System.Collections.Generic;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Linq;
using System.Threading;
using (var context = new kasvoldb())
            {
                var useredit = (from d in context.documents
                                where d.guid == leadId && d.arrange == 2
                                select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media1);
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using System.IO;
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using SixLabors.ImageSharp;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.categories
                                 where d.CategoryID == postcat.CategoryID
                                 select d).Single();
using System.Threading.Tasks;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using System.Data;
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
        //            string ext = System.IO.Path.GetExtension(ImageFile.FileName);


        //            string yy = t.getdoc(page.guid);
        //            if (yy != null)
        //            {
        //                using (var context = new kasvoldb())
        //                {
        //                    var useredit = (from d in context.documents
        //                                    where d.guid == page.guid
        //                                    select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media);
using System.Collections.Generic;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Linq;
using System.Threading;
using (var context = new kasvoldb())
            {
                var useredit = (from d in context.documents
                                where d.guid == leadId && d.arrange == 2
                                select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media1);
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using System.IO;
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using SixLabors.ImageSharp;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.categories
                                 where d.CategoryID == postcat.CategoryID
                                 select d).Single();
using System.Threading.Tasks;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using System.Data;
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

//                    useredit.title = webPFileName;
        //                    context.SaveChanges();
        //                }
        //                var googleDriveFileId = await _googleDriveService.UploadFileToGoogleDrive(normalImagePath, webPFileName, "1RRYz1pOnD08phGYHpLENMG_JijuVetju", _cancellationTokenSource.Token);

        //                // Update EC Document information after the file being uploaded
        //                await AssociateFileWithEcDocument(page.guid, googleDriveFileId);

        //            }
        //            else
        //            {
        //                documents media = new documents();

        //                media.arrange = 1;

        //                media.guid = page.guid;


        //                media.link = "kasvolfactory/vi/category/" + id + "/pdf/" + webPFileName;
        //                media.title = webPFileName;
        //                using (var context = new kasvoldb())
        //                {
        //                    var useredit = (from d in context.documents
        //                                    where d.guid == page.guid
        //                                    select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media);
using System.Collections.Generic;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Linq;
using System.Threading;
using (var context = new kasvoldb())
            {
                var useredit = (from d in context.documents
                                where d.guid == leadId && d.arrange == 2
                                select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media1);
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using System.IO;
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using SixLabors.ImageSharp;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.categories
                                 where d.CategoryID == postcat.CategoryID
                                 select d).Single();
using System.Threading.Tasks;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using System.Data;
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

//                }
        //                var googleDriveFileId = await _googleDriveService.UploadFileToGoogleDrive(normalImagePath, webPFileName, "1RRYz1pOnD08phGYHpLENMG_JijuVetju", _cancellationTokenSource.Token);


        //                documents media1 = new documents();

        //                media1.arrange = 2;

        //                media1.guid = page.guid;


        //                media1.link = "https://drive.google.com/file/d/" + googleDriveFileId + "/preview?usp=drivesdk";
        //                media1.title = googleDriveFileId;
        //                media1.lang = "gdrive";
        //                using (var context = new kasvoldb())
        //                {
        //                    var useredit = (from d in context.documents
        //                                    where d.guid == page.guid
        //                                    select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media);
using System.Collections.Generic;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Linq;
using System.Threading;
using (var context = new kasvoldb())
            {
                var useredit = (from d in context.documents
                                where d.guid == leadId && d.arrange == 2
                                select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media1);
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using System.IO;
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using SixLabors.ImageSharp;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.categories
                                 where d.CategoryID == postcat.CategoryID
                                 select d).Single();
using System.Threading.Tasks;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using System.Data;
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

//                }
        //            }
        //        }
        //        catch (Exception e)
        //        {
        //            Console.WriteLine("Overflow. {0}", e.Message);
        //        }
        //    }



        //    return RedirectToAction("Index");
        //}
        async Task AssociateFileWithEcDocument(string leadId, string googleDriveFileId)
        {

            //Entering the following is not allowed by Salesforce because it will be generated automatically
            //EC_Doc_URL__c = $"https://drive.google.com/file/d/{googleDriveFileId}/preview?usp=drivesdk",
            //Download_URL__c = $"https://drive.google.com/u/0/uc?id={googleDriveFileId}&export=download",

            using (var context = new kasvoldb())
        //                {
        //                    var useredit = (from d in context.documents
        //                                    where d.guid == page.guid
        //                                    select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media);
using System.Collections.Generic;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Linq;
using System.Threading;
using (var context = new kasvoldb())
            {
                var useredit = (from d in context.documents
                                where d.guid == leadId && d.arrange == 2
                                select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media1);
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using System.IO;
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using SixLabors.ImageSharp;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.categories
                                 where d.CategoryID == postcat.CategoryID
                                 select d).Single();
using System.Threading.Tasks;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using System.Data;
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

useredit.link = "https://drive.google.com/file/d/" + googleDriveFileId + "/preview?usp=drivesdk";
                useredit.title = googleDriveFileId;
                useredit.lang = "gdrive";
                context.SaveChanges();
            }
        }

        [HttpPost]
        public async Task<ActionResult> uploadv(int? id, IFormFile ImageFile)
        {
            kasvolservices t = new kasvolservices();
            Category page = await db.categories.FindAsync(id);

            string path = Server.MapPath("~/kasvolfactory/vi/pcategory/" + page.CategoryID + "/");


            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }



            if (ImageFile != null)
            {
                try
                {
                    string normalImagePath = System.IO.Path.Combine(path, ImageFile.FileName);
                    string webPFileName = System.IO.Path.GetFileNameWithoutExtension(ImageFile.FileName) + ".webp";
                    string webPImagePath = System.IO.Path.Combine(path, webPFileName);
                    // using (var context = new kasvoldb())
        //                {
        //                    var useredit = (from d in context.documents
        //                                    where d.guid == page.guid
        //                                    select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media);
using System.Collections.Generic;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Linq;
using System.Threading;
using (var context = new kasvoldb())
            {
                var useredit = (from d in context.documents
                                where d.guid == leadId && d.arrange == 2
                                select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media1);
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using System.IO;
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using SixLabors.ImageSharp;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.categories
                                 where d.CategoryID == postcat.CategoryID
                                 select d).Single();
using System.Threading.Tasks;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using System.Data;
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                    string ext = System.IO.Path.GetExtension(ImageFile.FileName);
                    if (ext != ".webp")
                    {
                        var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                        using (var context = new kasvoldb())
        //                {
        //                    var useredit = (from d in context.documents
        //                                    where d.guid == page.guid
        //                                    select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media);
using System.Collections.Generic;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Linq;
using System.Threading;
using (var context = new kasvoldb())
            {
                var useredit = (from d in context.documents
                                where d.guid == leadId && d.arrange == 2
                                select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media1);
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using System.IO;
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using SixLabors.ImageSharp;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.categories
                                 where d.CategoryID == postcat.CategoryID
                                 select d).Single();
using System.Threading.Tasks;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using System.Data;
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                    }

                    string yy = t.getimagewithid(page.guid, "1");
                    if (yy != null)
                    {
                        using (var context = new kasvoldb())
        //                {
        //                    var useredit = (from d in context.documents
        //                                    where d.guid == page.guid
        //                                    select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media);
using System.Collections.Generic;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Linq;
using System.Threading;
using (var context = new kasvoldb())
            {
                var useredit = (from d in context.documents
                                where d.guid == leadId && d.arrange == 2
                                select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media1);
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using System.IO;
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using SixLabors.ImageSharp;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.categories
                                 where d.CategoryID == postcat.CategoryID
                                 select d).Single();
using System.Threading.Tasks;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using System.Data;
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

useredit.name = "kasvolfactory/vi/pcategory/" + id + "/" + webPFileName;
                            useredit.filename = webPFileName;
                            context.SaveChanges();
                        }

                    }
                    else
                    {
                        media media = new media();

                        media.arrange = 1;

                        media.guid = page.guid;
                        media.lang = "1";
                        media.type = "pcategory";
                        media.name = "kasvolfactory/vi/pcategory/" + id + "/" + webPFileName;
                        media.filename = webPFileName;
                        using (var context = new kasvoldb())
        //                {
        //                    var useredit = (from d in context.documents
        //                                    where d.guid == page.guid
        //                                    select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media);
using System.Collections.Generic;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Linq;
using System.Threading;
using (var context = new kasvoldb())
            {
                var useredit = (from d in context.documents
                                where d.guid == leadId && d.arrange == 2
                                select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media1);
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using System.IO;
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using SixLabors.ImageSharp;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.categories
                                 where d.CategoryID == postcat.CategoryID
                                 select d).Single();
using System.Threading.Tasks;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using System.Data;
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

db1.SaveChanges();
                        }
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine("Overflow. {0}", e.Message);
                }
            }




            return RedirectToAction("Index");
        }
        // GET: postcats/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            Category postcat = db.categories.Find(id);
            if (postcat == null)
            {
                return NotFound();
            }
            return View(postcat);
        }

        // GET: postcats/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: postcats/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(Category postcat)
        {
            kasvolservices t = new kasvolservices();
            postcat.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            postcat.user = _userManager.GetUserId(User);
            postcat.CategoryName = t.setvalue(postcat.lang, postcat.CategoryName);

            if (ModelState.IsValid)
            {
                db.categories.Add(postcat);
                db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(postcat);
        }

        // GET: postcats/Edit/5
        public ActionResult Edit(int? id, string lang)
        {
            if (lang != null)
            {
                ViewBag.lang = lang;
            }
            if (id == null)
            {
                return BadRequest();
            }
            Category postcat = db.categories.Find(id);
            if (postcat == null)
            {
                return NotFound();
            }
            return View(postcat);
        }

        // POST: postcats/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(Category postcat)
        {
            kasvolservices t = new kasvolservices();
            postcat.CategoryName = t.updatevalue(postcat.lang, postcat.CategoryName, postcat.oldvalue);
            postcat.ogtitle = t.updatevalue(postcat.lang, postcat.ogtitle, postcat.oldvalue2);
            postcat.ogdescription = t.updatevalue(postcat.lang, postcat.ogdescription, postcat.oldvalue3);
            postcat.keywords = t.updatevalue(postcat.lang, postcat.keywords, postcat.oldvalue4);

            postcat.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();

            if (ModelState.IsValid)
            {
                using (var context = new kasvoldb())
        //                {
        //                    var useredit = (from d in context.documents
        //                                    where d.guid == page.guid
        //                                    select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media);
using System.Collections.Generic;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Linq;
using System.Threading;
using (var context = new kasvoldb())
            {
                var useredit = (from d in context.documents
                                where d.guid == leadId && d.arrange == 2
                                select d).Single();
using (kasvoldb db1 = new kasvoldb())
        //                {
        //                    db1.documents.Add(media1);
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using System.IO;
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using SixLabors.ImageSharp;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.categories
                                 where d.CategoryID == postcat.CategoryID
                                 select d).Single();
using System.Threading.Tasks;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid && d.lang == "1"
                                            select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using System.Data;
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

users.CategoryName = postcat.CategoryName;
                    users.ogtitle = postcat.ogtitle;
                    users.ogdescription = postcat.ogdescription;
                    users.keywords = postcat.keywords;

                    users.datemodified = DateTime.Now;
                    users.modifiedIP = postcat.modifiedIP;
                    context.SaveChanges();
                }

                return RedirectToAction("Index");
            }
            return View(postcat);
        }

        // GET: postcats/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            Category postcat = db.categories.Find(id);
            if (postcat == null)
            {
                return NotFound();
            }
            return View(postcat);
        }

        // POST: postcats/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            Category postcat = db.categories.Find(id);
            db.categories.Remove(postcat);
            db.SaveChanges();
            return RedirectToAction("Index");
        }


        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}






