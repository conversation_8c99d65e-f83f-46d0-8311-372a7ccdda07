using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var image = Image.Load(normalImagePath))
        // { image.SaveAsWebp(stream, new WebpEncoder { Quality = 80 });
using (var context = new KasvolDbContext(_db.Database.GetDbConnection().ConnectionString))
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "en"
        //                                select d).Single();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var stream = new FileStream(normalImagePath, FileMode.Create)) { ImageFile.CopyTo(stream);
using kasvol.Models;
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "tr"
        //                                select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var context = new KasvolDbContext())
        //                {
        //                var useredit = (from d in context.VideosKasvols
        //                                    where d.guid == page.guid && d.lang == "ar"
        //                                    select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using kasvol.service;

namespace kasvol.Controllers
{
    [Authorize]
    public class videoskasvolsController : BaseController
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly KasvolDbContext _db;

        // GET: videoskasvols
        public videoskasvolsController(KasvolDbContext context, IWebHostEnvironment webHostEnvironment)
        {
            _db = context;
            _webHostEnvironment = webHostEnvironment;
        }
        public ActionResult Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
           
            return View(_db.VideosKasvols.Where(a => a.lang == "ar").ToList());
        }
        public async Task<ActionResult> uploadv(int? id)
        {
            kasvolservices t = new kasvolservices(); if (id == null)
            {
                return BadRequest();
            }
            VideosKasvol page = await _db.VideosKasvols.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            return View(page);
        }
        //[HttpPost]
        //public async Task<ActionResult> uploadv(int? videoskasvolsid, IFormFile ImageFile)
        //{
        //    kasvolservices t = new kasvolservices();
        //    VideosKasvol page = await db.VideosKasvols.FindAsync(videoskasvolsid);

        //    string path = Server.MapPath("~/kasvolfactory/vi/video/" + page.videoskasvolsid + "/");


        //    if (!Directory.Exists(path))
        //    {
        //        Directory.CreateDirectory(path);
        //    }


        //    if (ImageFile != null)
        //    {
               
        //        try
        //        {
        //            string normalImagePath = System.IO.Path.Combine(path, ImageFile.FileName);
        //            string webPFileName = System.IO.Path.GetFileNameWithoutExtension(ImageFile.FileName) + ".webp";
                  
        //            string webPImagePath = System.IO.Path.Combine(path, webPFileName);
             
        //            using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var image = Image.Load(normalImagePath))
        // { image.SaveAsWebp(stream, new WebpEncoder { Quality = 80 });
using (var context = new KasvolDbContext(_db.Database.GetDbConnection().ConnectionString))
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "en"
        //                                select d).Single();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var stream = new FileStream(normalImagePath, FileMode.Create)) { ImageFile.CopyTo(stream);
using kasvol.Models;
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "tr"
        //                                select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var context = new KasvolDbContext())
        //                {
        //                var useredit = (from d in context.VideosKasvols
        //                                    where d.guid == page.guid && d.lang == "ar"
        //                                    select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using kasvol.service;

}
        //        }
        //            string ext = System.IO.Path.GetExtension(ImageFile.FileName);
        //            if (ext != ".webp")
        //            {
        //                var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                        
        // using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var image = Image.Load(normalImagePath))
        // { image.SaveAsWebp(stream, new WebpEncoder { Quality = 80 });
using (var context = new KasvolDbContext(_db.Database.GetDbConnection().ConnectionString))
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "en"
        //                                select d).Single();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var stream = new FileStream(normalImagePath, FileMode.Create)) { ImageFile.CopyTo(stream);
using kasvol.Models;
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "tr"
        //                                select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var context = new KasvolDbContext())
        //                {
        //                var useredit = (from d in context.VideosKasvols
        //                                    where d.guid == page.guid && d.lang == "ar"
        //                                    select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using kasvol.service;

}

        //                            .Format(new WebPFormat())
        //                            .Quality(80)
        //                            .Save(webPFileStream);
                      
        //            }

                   
        //                using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var image = Image.Load(normalImagePath))
        // { image.SaveAsWebp(stream, new WebpEncoder { Quality = 80 });
using (var context = new KasvolDbContext(_db.Database.GetDbConnection().ConnectionString))
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "en"
        //                                select d).Single();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var stream = new FileStream(normalImagePath, FileMode.Create)) { ImageFile.CopyTo(stream);
using kasvol.Models;
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "tr"
        //                                select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var context = new KasvolDbContext())
        //                {
        //                var useredit = (from d in context.VideosKasvols
        //                                    where d.guid == page.guid && d.lang == "ar"
        //                                    select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using kasvol.service;

//                    useredit.coverimagelink = "kasvolfactory/vi/video/" + page.videoskasvolsid + "/" + webPFileName;
                    
        //                    context.SaveChanges();
        //                }
        //            using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var image = Image.Load(normalImagePath))
        // { image.SaveAsWebp(stream, new WebpEncoder { Quality = 80 });
using (var context = new KasvolDbContext(_db.Database.GetDbConnection().ConnectionString))
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "en"
        //                                select d).Single();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var stream = new FileStream(normalImagePath, FileMode.Create)) { ImageFile.CopyTo(stream);
using kasvol.Models;
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "tr"
        //                                select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var context = new KasvolDbContext())
        //                {
        //                var useredit = (from d in context.VideosKasvols
        //                                    where d.guid == page.guid && d.lang == "ar"
        //                                    select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using kasvol.service;

//                useredit.coverimagelink = "kasvolfactory/vi/video/" + page.videoskasvolsid + "/" + webPFileName;

        //                context.SaveChanges();
        //            }
        //            using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var image = Image.Load(normalImagePath))
        // { image.SaveAsWebp(stream, new WebpEncoder { Quality = 80 });
using (var context = new KasvolDbContext(_db.Database.GetDbConnection().ConnectionString))
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "en"
        //                                select d).Single();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var stream = new FileStream(normalImagePath, FileMode.Create)) { ImageFile.CopyTo(stream);
using kasvol.Models;
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "tr"
        //                                select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var context = new KasvolDbContext())
        //                {
        //                var useredit = (from d in context.VideosKasvols
        //                                    where d.guid == page.guid && d.lang == "ar"
        //                                    select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using kasvol.service;

//                useredit.coverimagelink = "kasvolfactory/vi/video/" + page.videoskasvolsid + "/" + webPFileName;

        //                context.SaveChanges();
        //            }


        //        }
        //        catch (Exception e)
        //        {
        //            Console.WriteLine("Overflow. {0}", e.Message);
        //        }
        //    }




        //    return RedirectToAction("Index");
        //}

        // GET: videoskasvols/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            VideosKasvol videoskasvols = _db.VideosKasvols.Find(id);
            if (videoskasvols == null)
            {
                return NotFound();
            }
            return View(videoskasvols);
        }

        // GET: videoskasvols/Create
        public ActionResult Create()
        {
            ViewBag.videocategory = _db.VideoCategories.ToList();
            return View();
        }

        // POST: videoskasvols/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(string videoar, string videoen, string videotr, string desar, string desen, string destr, string titlear, string titleen, string titletr,int videocategoryid)
        {
            kasvolservices t = new kasvolservices();

            string guid = Guid.NewGuid().ToString();

            VideosKasvol media = new VideosKasvol();

            media.arrange = 1;
            media.lang = "ar";
            media.guid = guid;
            media.title = titlear;
            media.ogdescription = desar;
            media.videocategoryid = videocategoryid;
            media.youtube = videoar;
            _db.VideosKasvols.Add(media);
            _db.SaveChanges();


            VideosKasvol media1 = new VideosKasvol();

            media1.arrange = 2;
            media1.lang = "en";
            media1.guid = guid;
            media1.title = titleen;
            media1.ogdescription = desen;
            media1.videocategoryid = videocategoryid;
            media1.youtube = videoen;
            _db.VideosKasvols.Add(media1);
            _db.SaveChanges();



            VideosKasvol media2 = new VideosKasvol();

            media2.arrange = 3;
            media2.lang = "tr";
            media2.guid = guid;
            media2.title = titletr;
            media2.ogdescription = destr;
            media2.videocategoryid = videocategoryid;
            media2.youtube = videotr;
            _db.VideosKasvols.Add(media2);
            _db.SaveChanges();




            return RedirectToAction("Index");

        }

        // GET: videoskasvols/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }

            VideosKasvol videoskasvols = _db.VideosKasvols.Find(id);
            if (videoskasvols == null)
            {
                return NotFound();
            }
            ViewBag.videocategory = _db.VideoCategories.ToList();
            return View(videoskasvols);
        }

        // POST: videoskasvols/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(int videoskasvolid, string videoar, string videoen, string videotr, string titlear, string titleen, string titletr, string desar, string desen, string destr)
        {
            string guid = _db.VideosKasvols.Where(a => a.videoskasvolid == videoskasvolid).Select(a => a.guid).Single();
            kasvolservices t = new kasvolservices();

            if (videoar != "")
            {
                using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var image = Image.Load(normalImagePath))
        // { image.SaveAsWebp(stream, new WebpEncoder { Quality = 80 });
using (var context = new KasvolDbContext(_db.Database.GetDbConnection().ConnectionString))
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "en"
        //                                select d).Single();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var stream = new FileStream(normalImagePath, FileMode.Create)) { ImageFile.CopyTo(stream);
using kasvol.Models;
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "tr"
        //                                select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var context = new KasvolDbContext())
        //                {
        //                var useredit = (from d in context.VideosKasvols
        //                                    where d.guid == page.guid && d.lang == "ar"
        //                                    select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using kasvol.service;

useredit.youtube = videoar;
                    useredit.title = titlear;
                    useredit.ogdescription = desar;
                    context.SaveChanges();
                }

            }
             if (videoen != "")
            {


                using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var image = Image.Load(normalImagePath))
        // { image.SaveAsWebp(stream, new WebpEncoder { Quality = 80 });
using (var context = new KasvolDbContext(_db.Database.GetDbConnection().ConnectionString))
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "en"
        //                                select d).Single();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var stream = new FileStream(normalImagePath, FileMode.Create)) { ImageFile.CopyTo(stream);
using kasvol.Models;
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "tr"
        //                                select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var context = new KasvolDbContext())
        //                {
        //                var useredit = (from d in context.VideosKasvols
        //                                    where d.guid == page.guid && d.lang == "ar"
        //                                    select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using kasvol.service;

useredit.youtube = videoen;
                    useredit.title = titleen;
                    useredit.ogdescription = desen;
                    context.SaveChanges();
                }

            }
             if (videotr != "")
            {
                using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var image = Image.Load(normalImagePath))
        // { image.SaveAsWebp(stream, new WebpEncoder { Quality = 80 });
using (var context = new KasvolDbContext(_db.Database.GetDbConnection().ConnectionString))
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "en"
        //                                select d).Single();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var stream = new FileStream(normalImagePath, FileMode.Create)) { ImageFile.CopyTo(stream);
using kasvol.Models;
using (var context = new kasvoldb())
        //            {
        //                var useredit = (from d in context.VideosKasvols
        //                                where d.guid == page.guid && d.lang == "tr"
        //                                select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var context = new KasvolDbContext())
        //                {
        //                var useredit = (from d in context.VideosKasvols
        //                                    where d.guid == page.guid && d.lang == "ar"
        //                                    select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var context = new KasvolDbContext())
                {
                    var useredit = (from d in context.VideosKasvols
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using kasvol.service;

useredit.youtube = videotr;
                    useredit.title = titletr;
                    useredit.ogdescription = destr;
                    context.SaveChanges();
                }

            }



            return RedirectToAction("Index");
        }

        // GET: videoskasvols/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            VideosKasvol videoskasvols = _db.VideosKasvols.Find(id);
            if (videoskasvols == null)
            {
                return NotFound();
            }
            return View(videoskasvols);
        }

        // POST: videoskasvols/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            VideosKasvol videoskasvols = _db.VideosKasvols.Find(id);
            _db.VideosKasvols.Remove(videoskasvols);
            _db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}




