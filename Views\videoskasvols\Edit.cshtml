@model kasvol.Models.VideosKasvol

@{ ViewBag.Title = @Resources.Resource.String114;

    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}
@using kasvol.service;
@{kasvolservices t = new kasvolservices(); }

@{


    string value1 = t.getvalue("en", Model.Videocategory.title);

    string valuear = t.vida(Model.guid, "ar");
    string valueen = t.vida(Model.guid, "en");
    string valuetr = t.vida(Model.guid, "tr");

    string valueart = t.vidtt(Model.guid, "ar");
    string valueent = t.vidtt(Model.guid, "en");
    string valuetrt = t.vidtt(Model.guid, "tr");
    string valueart1 = t.vidtt1(Model.guid, "ar");
    string valueent1 = t.vidtt1(Model.guid, "en");
    string valuetrt1 = t.vidtt1(Model.guid, "tr");

}
<div id="progress" class="modal1">
    <div class="throbber">
        <div class="curtain">
        </div>
        <div class="curtain-content">
            <div>

                <div id="preloader">
                    <div id="loader"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="card card-custom">
    <div class="card-header flex-wrap py-5">
        <div class="card-360Degreear">
            <h3 class="card-label">
                @Resources.Resource.String114

            </h3>

        </div>


    </div>
    <div class="card-body" style="margin-bottom:125px;text-align:center">






        @using (Ajax.BeginForm("Edit", "videoskasvols",
                                       new AjaxOptions
                                       {
                                           OnSuccess = "OnSuccess",
                                           OnFailure = "OnFailure",
                                           OnBegin = "onLoginBegin",
                                           LoadingElementId = "progress"
                                       }))
        {@Html.AntiForgeryToken()



        <div class="form-horizontal">


            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            @Html.HiddenFor(model => model.videoskasvolid)


            <div class="form-group m-form__group row">
                <label class="col-md-12 col-sm-12 col-form-label m--align-center" for="title">@Resources.Resource.String4 ar <img alt="en" src="/assets/img/ar.png" /></label>
                <div class="col-sm-12">
                    <input class="form-control text-box single-line" id="titlear" name="titlear" type="text" value="@valueart">
                    <span class="field-validation-valid text-danger" data-valmsg-for="titlear" data-valmsg-replace="true"></span>
                </div>
            </div>
            <div class="form-group m-form__group row">
                <label class="col-md-12 col-sm-12 col-form-label m--align-center" for="title">OgDescription ar <img alt="en" src="/assets/img/ar.png" /></label>
                <div class="col-sm-12">
                    <input class="form-control text-box single-line" id="desar" name="desar" type="text" value="@valueart1">
                    <span class="field-validation-valid text-danger" data-valmsg-for="titlear" data-valmsg-replace="true"></span>
                </div>
            </div>

            <div class="form-group m-form__group row">
                <label class="col-md-12 col-sm-12 col-form-label m--align-center" for="360Degreear">@Resources.Resource.video ar <img alt="en" src="/assets/img/ar.png" /></label>
                <div class="col-sm-12">
                    <input class="form-control text-box single-line" id="videoar" name="videoar" type="text" value="@valuear">
                    <span class="field-validation-valid text-danger" data-valmsg-for="360Degreear" data-valmsg-replace="true"></span>
                </div>
            </div>
            <div class="form-group m-form__group row">
                <label class="col-md-12 col-sm-12 col-form-label m--align-center" for="title">@Resources.Resource.String4 en <img alt="en" src="/assets/img/en.png" /></label>
                <div class="col-sm-12">
                    <input class="form-control text-box single-line" id="titleen" name="titleen" type="text" value="@valueent">
                    <span class="field-validation-valid text-danger" data-valmsg-for="titleen" data-valmsg-replace="true"></span>
                </div>
            </div>
            <div class="form-group m-form__group row">
                <label class="col-md-12 col-sm-12 col-form-label m--align-center" for="title">OgDescription en <img alt="en" src="/assets/img/en.png" /></label>
                <div class="col-sm-12">
                    <input class="form-control text-box single-line" id="desen" name="desen" type="text" value="@valueent1">
                    <span class="field-validation-valid text-danger" data-valmsg-for="titlear" data-valmsg-replace="true"></span>
                </div>
            </div>
            <div class="form-group m-form__group row">
                <label class="col-md-12 col-sm-12 col-form-label m--align-center" for="360Degreeen">@Resources.Resource.video en <img alt="en" src="/assets/img/en.png" /></label>
                <div class="col-sm-12">
                    <input class="form-control text-box single-line" id="videoen" name="videoen" type="text" value="@valueen">
                    <span class="field-validation-valid text-danger" data-valmsg-for="360Degreeen" data-valmsg-replace="true"></span>
                </div>
            </div>
            @*<div class="form-group m-form__group row">
                <label class="col-md-12 col-sm-12 col-form-label m--align-center" for="title">@Resources.Resource.String4 tr <img alt="en" src="/assets/img/tr.png" /></label>
                <div class="col-sm-12">
                    <input class="form-control text-box single-line" id="titletr" name="titletr" type="text" value="@valuetrt">
                    <span class="field-validation-valid text-danger" data-valmsg-for="titletr" data-valmsg-replace="true"></span>
                </div>
            </div>
            <div class="form-group m-form__group row">
                <label class="col-md-12 col-sm-12 col-form-label m--align-center" for="title">OgDescription tr <img alt="en" src="/assets/img/tr.png" /></label>
                <div class="col-sm-12">
                    <input class="form-control text-box single-line" id="destr" name="destr" type="text" value="@valuetrt1">
                    <span class="field-validation-valid text-danger" data-valmsg-for="titlear" data-valmsg-replace="true"></span>
                </div>
            </div>
            <div class="form-group m-form__group row">
                <label class="col-md-12 col-sm-12 col-form-label m--align-center" for="360Degreetr">@Resources.Resource.video tr <img alt="en" src="/assets/img/tr.png" /></label>
                <div class="col-sm-12">
                    <input class="form-control text-box single-line" id="videotr" name="videotr" type="text" value="@valuetr">
                    <span class="field-validation-valid text-danger" data-valmsg-for="360Degreetr" data-valmsg-replace="true"></span>
                </div>
            </div>*@
            <div class="form-group m-form__group row">
                @Resources.Resource.String68
                <div class="col-sm-12">

                    <select class="form-control" name="videocategoryid" required>
                        <option selected value="@Model.videocategoryid">@value1 </option>
                        @foreach (var item in ViewBag.videocategory)
                        {



                            string value = t.getvalue(ViewBag.lang, item.title);



                            <option value="@item.videocategoryid">@value</option>
                        }
                    </select>
                </div>
            </div>

            <br /><br />

            <div class="form-group">
                <div class=" col-md-12" style="text-align:center">
                    <input type="submit" value="@Resources.Resource.String9" class="btn btn-primary" />
                </div>
            </div>
        </div>
    }


        <div id="status">@ViewBag.mes</div>
        <div class=" col-md-12" style="text-align:center">
            @Html.ActionLink("Back to List", "Index")
        </div>
    </div>
</div>



<link rel="stylesheet" type="text/css" href="~/smart/css/smart-forms.css">
<link rel="stylesheet" type="text/css" href="~/smart/css/smart-addons.css">


@section Scripts {

    <script type="text/javascript">
            function OnSuccess() {
                location.href = '/videos/Index';
            }
            function OnFailure() {
                alert("Programmer will know this error ");
                $('#progress').hide();
            }

    </script>
    <script type="text/javascript">

            jQuery(document).ready(function ($) {

                $('#form0').validate();


            });

    </script>
    <script type="text/javascript">
            $("#form0").on("submit", function (event) {



                if ($('#form0').valid()) {
                    $('#progress').show();
                }
            });

    </script>

    <script type="text/javascript" src="~/smart/js/jquery.validate.min.js"></script>
    <script type="text/javascript" src="~/smart/js/additional-methods.min.js"></script>

}
