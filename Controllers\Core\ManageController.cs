using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using var enReader = await enCommand.ExecuteReaderAsync();
using MySql.Data.MySqlClient;
using EF Core
                var posts = await _db.posts.ToListAsync();
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Text.RegularExpressions;
using var reader = await command.ExecuteReaderAsync();
using (var image = SixLabors.ImageSharp.Image.Load(normalImagePath))
                                    {
                                        // Save original size image
                                        await using (var webpFileStream = System.IO.File.Create(webpImagePath))
                                        {
                                            await image.SaveAsync(webpFileStream, new WebpEncoder { Quality = 80 });
using var mediaReader = await mediaCommand.ExecuteReaderAsync();
using SixLabors.ImageSharp.Formats.Webp;
using (var enConnection = new MySqlConnection(mysqlConnection))
                    {
                        await enConnection.OpenAsync();
using var connection = new MySqlConnection(mysqlConnection);
using var mediaConnection = new MySqlConnection(mysqlConnection);
using Microsoft.AspNetCore.Identity;
using SixLabors.ImageSharp.Processing;
using kasvol.Models;
using Microsoft.AspNetCore.Http;
using (var trConnection = new MySqlConnection(mysqlConnection))
                        {
                            await trConnection.OpenAsync();
using var command = new MySqlCommand("SELECT * FROM `posts` where `lang`='ar'", connection);
using SixLabors.ImageSharp;
using (var webpFileStream1 = System.IO.File.Create(webpImagePath1))
                                        {
                                            await image.SaveAsync(webpFileStream1, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using modern connection management
                string mysqlConnection = _configuration.GetConnectionString("constr1");
using Microsoft.Data.SqlClient;
using var trReader = await trCommand.ExecuteReaderAsync();

namespace kasvol.Controllers.Core
{
    [Authorize]
    public class ManageController : BaseController
    {
        private readonly ILogger<ManageController> _logger;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;

        public ManageController(
            KasvolDbContext db,
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            IConfiguration configuration,
            kasvolservices kasvolServices,
            ILogger<ManageController> logger,
            IWebHostEnvironment webHostEnvironment,
            IHttpContextAccessor httpContextAccessor)
            : base(db, configuration, kasvolServices, logger)
        {
            _logger = logger;
            _webHostEnvironment = webHostEnvironment;
            _userManager = userManager;
            _signInManager = signInManager;
        }

        [HttpPost]
        public async Task<IActionResult> EditAlt()
        {
            try
            {
                // Delete existing posts using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using var enReader = await enCommand.ExecuteReaderAsync();
using MySql.Data.MySqlClient;
using EF Core
                var posts = await _db.posts.ToListAsync();
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Text.RegularExpressions;
using var reader = await command.ExecuteReaderAsync();
using (var image = SixLabors.ImageSharp.Image.Load(normalImagePath))
                                    {
                                        // Save original size image
                                        await using (var webpFileStream = System.IO.File.Create(webpImagePath))
                                        {
                                            await image.SaveAsync(webpFileStream, new WebpEncoder { Quality = 80 });
using var mediaReader = await mediaCommand.ExecuteReaderAsync();
using SixLabors.ImageSharp.Formats.Webp;
using (var enConnection = new MySqlConnection(mysqlConnection))
                    {
                        await enConnection.OpenAsync();
using var connection = new MySqlConnection(mysqlConnection);
using var mediaConnection = new MySqlConnection(mysqlConnection);
using Microsoft.AspNetCore.Identity;
using SixLabors.ImageSharp.Processing;
using kasvol.Models;
using Microsoft.AspNetCore.Http;
using (var trConnection = new MySqlConnection(mysqlConnection))
                        {
                            await trConnection.OpenAsync();
using var command = new MySqlCommand("SELECT * FROM `posts` where `lang`='ar'", connection);
using SixLabors.ImageSharp;
using (var webpFileStream1 = System.IO.File.Create(webpImagePath1))
                                        {
                                            await image.SaveAsync(webpFileStream1, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using modern connection management
                string mysqlConnection = _configuration.GetConnectionString("constr1");
using Microsoft.Data.SqlClient;
using var trReader = await trCommand.ExecuteReaderAsync();

_db.posts.RemoveRange(posts);
                await _db.SaveChangesAsync();

                // Import posts from MySQL using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using var enReader = await enCommand.ExecuteReaderAsync();
using MySql.Data.MySqlClient;
using EF Core
                var posts = await _db.posts.ToListAsync();
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Text.RegularExpressions;
using var reader = await command.ExecuteReaderAsync();
using (var image = SixLabors.ImageSharp.Image.Load(normalImagePath))
                                    {
                                        // Save original size image
                                        await using (var webpFileStream = System.IO.File.Create(webpImagePath))
                                        {
                                            await image.SaveAsync(webpFileStream, new WebpEncoder { Quality = 80 });
using var mediaReader = await mediaCommand.ExecuteReaderAsync();
using SixLabors.ImageSharp.Formats.Webp;
using (var enConnection = new MySqlConnection(mysqlConnection))
                    {
                        await enConnection.OpenAsync();
using var connection = new MySqlConnection(mysqlConnection);
using var mediaConnection = new MySqlConnection(mysqlConnection);
using Microsoft.AspNetCore.Identity;
using SixLabors.ImageSharp.Processing;
using kasvol.Models;
using Microsoft.AspNetCore.Http;
using (var trConnection = new MySqlConnection(mysqlConnection))
                        {
                            await trConnection.OpenAsync();
using var command = new MySqlCommand("SELECT * FROM `posts` where `lang`='ar'", connection);
using SixLabors.ImageSharp;
using (var webpFileStream1 = System.IO.File.Create(webpImagePath1))
                                        {
                                            await image.SaveAsync(webpFileStream1, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using modern connection management
                string mysqlConnection = _configuration.GetConnectionString("constr1");
using Microsoft.Data.SqlClient;
using var trReader = await trCommand.ExecuteReaderAsync();

await using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using var enReader = await enCommand.ExecuteReaderAsync();
using MySql.Data.MySqlClient;
using EF Core
                var posts = await _db.posts.ToListAsync();
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Text.RegularExpressions;
using var reader = await command.ExecuteReaderAsync();
using (var image = SixLabors.ImageSharp.Image.Load(normalImagePath))
                                    {
                                        // Save original size image
                                        await using (var webpFileStream = System.IO.File.Create(webpImagePath))
                                        {
                                            await image.SaveAsync(webpFileStream, new WebpEncoder { Quality = 80 });
using var mediaReader = await mediaCommand.ExecuteReaderAsync();
using SixLabors.ImageSharp.Formats.Webp;
using (var enConnection = new MySqlConnection(mysqlConnection))
                    {
                        await enConnection.OpenAsync();
using var connection = new MySqlConnection(mysqlConnection);
using var mediaConnection = new MySqlConnection(mysqlConnection);
using Microsoft.AspNetCore.Identity;
using SixLabors.ImageSharp.Processing;
using kasvol.Models;
using Microsoft.AspNetCore.Http;
using (var trConnection = new MySqlConnection(mysqlConnection))
                        {
                            await trConnection.OpenAsync();
using var command = new MySqlCommand("SELECT * FROM `posts` where `lang`='ar'", connection);
using SixLabors.ImageSharp;
using (var webpFileStream1 = System.IO.File.Create(webpImagePath1))
                                        {
                                            await image.SaveAsync(webpFileStream1, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using modern connection management
                string mysqlConnection = _configuration.GetConnectionString("constr1");
using Microsoft.Data.SqlClient;
using var trReader = await trCommand.ExecuteReaderAsync();

await connection.OpenAsync();
                
                // Get Arabic posts
                await using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using var enReader = await enCommand.ExecuteReaderAsync();
using MySql.Data.MySqlClient;
using EF Core
                var posts = await _db.posts.ToListAsync();
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Text.RegularExpressions;
using var reader = await command.ExecuteReaderAsync();
using (var image = SixLabors.ImageSharp.Image.Load(normalImagePath))
                                    {
                                        // Save original size image
                                        await using (var webpFileStream = System.IO.File.Create(webpImagePath))
                                        {
                                            await image.SaveAsync(webpFileStream, new WebpEncoder { Quality = 80 });
using var mediaReader = await mediaCommand.ExecuteReaderAsync();
using SixLabors.ImageSharp.Formats.Webp;
using (var enConnection = new MySqlConnection(mysqlConnection))
                    {
                        await enConnection.OpenAsync();
using var connection = new MySqlConnection(mysqlConnection);
using var mediaConnection = new MySqlConnection(mysqlConnection);
using Microsoft.AspNetCore.Identity;
using SixLabors.ImageSharp.Processing;
using kasvol.Models;
using Microsoft.AspNetCore.Http;
using (var trConnection = new MySqlConnection(mysqlConnection))
                        {
                            await trConnection.OpenAsync();
using var command = new MySqlCommand("SELECT * FROM `posts` where `lang`='ar'", connection);
using SixLabors.ImageSharp;
using (var webpFileStream1 = System.IO.File.Create(webpImagePath1))
                                        {
                                            await image.SaveAsync(webpFileStream1, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using modern connection management
                string mysqlConnection = _configuration.GetConnectionString("constr1");
using Microsoft.Data.SqlClient;
using var trReader = await trCommand.ExecuteReaderAsync();

await using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using var enReader = await enCommand.ExecuteReaderAsync();
using MySql.Data.MySqlClient;
using EF Core
                var posts = await _db.posts.ToListAsync();
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Text.RegularExpressions;
using var reader = await command.ExecuteReaderAsync();
using (var image = SixLabors.ImageSharp.Image.Load(normalImagePath))
                                    {
                                        // Save original size image
                                        await using (var webpFileStream = System.IO.File.Create(webpImagePath))
                                        {
                                            await image.SaveAsync(webpFileStream, new WebpEncoder { Quality = 80 });
using var mediaReader = await mediaCommand.ExecuteReaderAsync();
using SixLabors.ImageSharp.Formats.Webp;
using (var enConnection = new MySqlConnection(mysqlConnection))
                    {
                        await enConnection.OpenAsync();
using var connection = new MySqlConnection(mysqlConnection);
using var mediaConnection = new MySqlConnection(mysqlConnection);
using Microsoft.AspNetCore.Identity;
using SixLabors.ImageSharp.Processing;
using kasvol.Models;
using Microsoft.AspNetCore.Http;
using (var trConnection = new MySqlConnection(mysqlConnection))
                        {
                            await trConnection.OpenAsync();
using var command = new MySqlCommand("SELECT * FROM `posts` where `lang`='ar'", connection);
using SixLabors.ImageSharp;
using (var webpFileStream1 = System.IO.File.Create(webpImagePath1))
                                        {
                                            await image.SaveAsync(webpFileStream1, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using modern connection management
                string mysqlConnection = _configuration.GetConnectionString("constr1");
using Microsoft.Data.SqlClient;
using var trReader = await trCommand.ExecuteReaderAsync();

int i = 0;

                while (await reader.ReadAsync())
                {
                    int id = reader.GetInt32(0);
                    string slug = reader.IsDBNull(5) ? string.Empty : reader.GetString(5);
                    string oldTitle = reader.IsDBNull(1) ? string.Empty : reader.GetString(1).Replace(" ", "-");
                    string guid = Guid.NewGuid().ToString();
                    i++;

                    // Create Arabic post
                    var arabicPost = new posts
                    {
                        arrange = i,
                        lang = reader.IsDBNull(12) ? string.Empty : reader.GetString(12),
                        guid = guid,
                        title = reader.IsDBNull(1) ? string.Empty : reader.GetString(1),
                        ogtitle = reader.IsDBNull(1) ? string.Empty : reader.GetString(1),
                        content = reader.IsDBNull(2) ? string.Empty : reader.GetString(2).Replace("https://kasvol.com/wp-content/", "/wp-content/"),
                        ogdescription = StripHTML(reader.IsDBNull(2) ? string.Empty : reader.GetString(2).Replace("https://kasvol.com/wp-content/", "/wp-content/")).Substring(0, 165),
                        keywords = reader.IsDBNull(1) ? string.Empty : reader.GetString(1),
                        slogan = slug,
                        oldslogan = oldTitle,
                        auther = "kasvol",
                        postcatid = 1,
                        publish = true,
                        allowcomments = reader.GetBoolean(15),
                        allowwhatsapp = reader.GetBoolean(16)
                    };

                    _db.posts.Add(arabicPost);
                    await _db.SaveChangesAsync();

                    // Import English translation
                    await using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using var enReader = await enCommand.ExecuteReaderAsync();
using MySql.Data.MySqlClient;
using EF Core
                var posts = await _db.posts.ToListAsync();
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Text.RegularExpressions;
using var reader = await command.ExecuteReaderAsync();
using (var image = SixLabors.ImageSharp.Image.Load(normalImagePath))
                                    {
                                        // Save original size image
                                        await using (var webpFileStream = System.IO.File.Create(webpImagePath))
                                        {
                                            await image.SaveAsync(webpFileStream, new WebpEncoder { Quality = 80 });
using var mediaReader = await mediaCommand.ExecuteReaderAsync();
using SixLabors.ImageSharp.Formats.Webp;
using (var enConnection = new MySqlConnection(mysqlConnection))
                    {
                        await enConnection.OpenAsync();
using var connection = new MySqlConnection(mysqlConnection);
using var mediaConnection = new MySqlConnection(mysqlConnection);
using Microsoft.AspNetCore.Identity;
using SixLabors.ImageSharp.Processing;
using kasvol.Models;
using Microsoft.AspNetCore.Http;
using (var trConnection = new MySqlConnection(mysqlConnection))
                        {
                            await trConnection.OpenAsync();
using var command = new MySqlCommand("SELECT * FROM `posts` where `lang`='ar'", connection);
using SixLabors.ImageSharp;
using (var webpFileStream1 = System.IO.File.Create(webpImagePath1))
                                        {
                                            await image.SaveAsync(webpFileStream1, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using modern connection management
                string mysqlConnection = _configuration.GetConnectionString("constr1");
using Microsoft.Data.SqlClient;
using var trReader = await trCommand.ExecuteReaderAsync();

var enCommand = new MySqlCommand($"SELECT * FROM `posts` where `lang`='en' and `slug`=@slug", enConnection);
                        enCommand.Parameters.AddWithValue("@slug", slug);

                        await using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using var enReader = await enCommand.ExecuteReaderAsync();
using MySql.Data.MySqlClient;
using EF Core
                var posts = await _db.posts.ToListAsync();
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Text.RegularExpressions;
using var reader = await command.ExecuteReaderAsync();
using (var image = SixLabors.ImageSharp.Image.Load(normalImagePath))
                                    {
                                        // Save original size image
                                        await using (var webpFileStream = System.IO.File.Create(webpImagePath))
                                        {
                                            await image.SaveAsync(webpFileStream, new WebpEncoder { Quality = 80 });
using var mediaReader = await mediaCommand.ExecuteReaderAsync();
using SixLabors.ImageSharp.Formats.Webp;
using (var enConnection = new MySqlConnection(mysqlConnection))
                    {
                        await enConnection.OpenAsync();
using var connection = new MySqlConnection(mysqlConnection);
using var mediaConnection = new MySqlConnection(mysqlConnection);
using Microsoft.AspNetCore.Identity;
using SixLabors.ImageSharp.Processing;
using kasvol.Models;
using Microsoft.AspNetCore.Http;
using (var trConnection = new MySqlConnection(mysqlConnection))
                        {
                            await trConnection.OpenAsync();
using var command = new MySqlCommand("SELECT * FROM `posts` where `lang`='ar'", connection);
using SixLabors.ImageSharp;
using (var webpFileStream1 = System.IO.File.Create(webpImagePath1))
                                        {
                                            await image.SaveAsync(webpFileStream1, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using modern connection management
                string mysqlConnection = _configuration.GetConnectionString("constr1");
using Microsoft.Data.SqlClient;
using var trReader = await trCommand.ExecuteReaderAsync();

if (await enReader.ReadAsync())
                        {
                            var englishPost = new posts
                            {
                                arrange = i,
                                lang = enReader.IsDBNull(12) ? string.Empty : enReader.GetString(12),
                                guid = guid,
                                title = enReader.IsDBNull(1) ? string.Empty : enReader.GetString(1),
                                ogtitle = enReader.IsDBNull(1) ? string.Empty : enReader.GetString(1),
                                content = enReader.IsDBNull(2) ? string.Empty : enReader.GetString(2).Replace("https://kasvol.com/wp-content/", "/wp-content/"),
                                ogdescription = StripHTML(enReader.IsDBNull(2) ? string.Empty : enReader.GetString(2).Replace("https://kasvol.com/wp-content/", "/wp-content/")).Substring(0, 165),
                                keywords = enReader.IsDBNull(1) ? string.Empty : enReader.GetString(1),
                                slogan = slug,
                                oldslogan = oldTitle,
                                auther = "kasvol",
                                postcatid = 1,
                                publish = true,
                                allowcomments = enReader.GetBoolean(15),
                                allowwhatsapp = enReader.GetBoolean(16)
                            };

                            _db.posts.Add(englishPost);
                            await _db.SaveChangesAsync();
                        }
                    }

                    // Import Turkish translation
                    try
                    {
                        await using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using var enReader = await enCommand.ExecuteReaderAsync();
using MySql.Data.MySqlClient;
using EF Core
                var posts = await _db.posts.ToListAsync();
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Text.RegularExpressions;
using var reader = await command.ExecuteReaderAsync();
using (var image = SixLabors.ImageSharp.Image.Load(normalImagePath))
                                    {
                                        // Save original size image
                                        await using (var webpFileStream = System.IO.File.Create(webpImagePath))
                                        {
                                            await image.SaveAsync(webpFileStream, new WebpEncoder { Quality = 80 });
using var mediaReader = await mediaCommand.ExecuteReaderAsync();
using SixLabors.ImageSharp.Formats.Webp;
using (var enConnection = new MySqlConnection(mysqlConnection))
                    {
                        await enConnection.OpenAsync();
using var connection = new MySqlConnection(mysqlConnection);
using var mediaConnection = new MySqlConnection(mysqlConnection);
using Microsoft.AspNetCore.Identity;
using SixLabors.ImageSharp.Processing;
using kasvol.Models;
using Microsoft.AspNetCore.Http;
using (var trConnection = new MySqlConnection(mysqlConnection))
                        {
                            await trConnection.OpenAsync();
using var command = new MySqlCommand("SELECT * FROM `posts` where `lang`='ar'", connection);
using SixLabors.ImageSharp;
using (var webpFileStream1 = System.IO.File.Create(webpImagePath1))
                                        {
                                            await image.SaveAsync(webpFileStream1, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using modern connection management
                string mysqlConnection = _configuration.GetConnectionString("constr1");
using Microsoft.Data.SqlClient;
using var trReader = await trCommand.ExecuteReaderAsync();

var trCommand = new MySqlCommand($"SELECT * FROM `posts` where `lang`='tr' and `slug`=@slug", trConnection);
                            trCommand.Parameters.AddWithValue("@slug", slug);

                            await using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using var enReader = await enCommand.ExecuteReaderAsync();
using MySql.Data.MySqlClient;
using EF Core
                var posts = await _db.posts.ToListAsync();
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Text.RegularExpressions;
using var reader = await command.ExecuteReaderAsync();
using (var image = SixLabors.ImageSharp.Image.Load(normalImagePath))
                                    {
                                        // Save original size image
                                        await using (var webpFileStream = System.IO.File.Create(webpImagePath))
                                        {
                                            await image.SaveAsync(webpFileStream, new WebpEncoder { Quality = 80 });
using var mediaReader = await mediaCommand.ExecuteReaderAsync();
using SixLabors.ImageSharp.Formats.Webp;
using (var enConnection = new MySqlConnection(mysqlConnection))
                    {
                        await enConnection.OpenAsync();
using var connection = new MySqlConnection(mysqlConnection);
using var mediaConnection = new MySqlConnection(mysqlConnection);
using Microsoft.AspNetCore.Identity;
using SixLabors.ImageSharp.Processing;
using kasvol.Models;
using Microsoft.AspNetCore.Http;
using (var trConnection = new MySqlConnection(mysqlConnection))
                        {
                            await trConnection.OpenAsync();
using var command = new MySqlCommand("SELECT * FROM `posts` where `lang`='ar'", connection);
using SixLabors.ImageSharp;
using (var webpFileStream1 = System.IO.File.Create(webpImagePath1))
                                        {
                                            await image.SaveAsync(webpFileStream1, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using modern connection management
                string mysqlConnection = _configuration.GetConnectionString("constr1");
using Microsoft.Data.SqlClient;
using var trReader = await trCommand.ExecuteReaderAsync();

if (await trReader.ReadAsync())
                            {
                                var turkishPost = new posts
                                {
                                    arrange = i,
                                    lang = trReader.IsDBNull(12) ? string.Empty : trReader.GetString(12),
                                    guid = guid,
                                    title = trReader.IsDBNull(1) ? string.Empty : trReader.GetString(1),
                                    ogtitle = trReader.IsDBNull(1) ? string.Empty : trReader.GetString(1),
                                    content = trReader.IsDBNull(2) ? string.Empty : trReader.GetString(2).Replace("https://kasvol.com/wp-content/", "/wp-content/"),
                                    ogdescription = StripHTML(trReader.IsDBNull(2) ? string.Empty : trReader.GetString(2).Replace("https://kasvol.com/wp-content/", "/wp-content/")).Substring(0, 165),
                                    keywords = trReader.IsDBNull(1) ? string.Empty : trReader.GetString(1),
                                    slogan = slug,
                                    oldslogan = oldTitle,
                                    auther = "kasvol",
                                    postcatid = 1,
                                    publish = true,
                                    allowcomments = trReader.GetBoolean(15),
                                    allowwhatsapp = trReader.GetBoolean(16)
                                };

                                _db.posts.Add(turkishPost);
                                await _db.SaveChangesAsync();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Error processing Turkish translation: {ex.Message}");
                    }

                    // Import media with improved error handling
                    try
                    {
                        await using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using var enReader = await enCommand.ExecuteReaderAsync();
using MySql.Data.MySqlClient;
using EF Core
                var posts = await _db.posts.ToListAsync();
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Text.RegularExpressions;
using var reader = await command.ExecuteReaderAsync();
using (var image = SixLabors.ImageSharp.Image.Load(normalImagePath))
                                    {
                                        // Save original size image
                                        await using (var webpFileStream = System.IO.File.Create(webpImagePath))
                                        {
                                            await image.SaveAsync(webpFileStream, new WebpEncoder { Quality = 80 });
using var mediaReader = await mediaCommand.ExecuteReaderAsync();
using SixLabors.ImageSharp.Formats.Webp;
using (var enConnection = new MySqlConnection(mysqlConnection))
                    {
                        await enConnection.OpenAsync();
using var connection = new MySqlConnection(mysqlConnection);
using var mediaConnection = new MySqlConnection(mysqlConnection);
using Microsoft.AspNetCore.Identity;
using SixLabors.ImageSharp.Processing;
using kasvol.Models;
using Microsoft.AspNetCore.Http;
using (var trConnection = new MySqlConnection(mysqlConnection))
                        {
                            await trConnection.OpenAsync();
using var command = new MySqlCommand("SELECT * FROM `posts` where `lang`='ar'", connection);
using SixLabors.ImageSharp;
using (var webpFileStream1 = System.IO.File.Create(webpImagePath1))
                                        {
                                            await image.SaveAsync(webpFileStream1, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using modern connection management
                string mysqlConnection = _configuration.GetConnectionString("constr1");
using Microsoft.Data.SqlClient;
using var trReader = await trCommand.ExecuteReaderAsync();

await mediaConnection.OpenAsync();
                        var mediaCommand = new MySqlCommand("SELECT id,name,file_name FROM `media` where `collection_name`='post' and `model_id`=@id", mediaConnection);
                        mediaCommand.Parameters.AddWithValue("@id", id);

                        await using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using var enReader = await enCommand.ExecuteReaderAsync();
using MySql.Data.MySqlClient;
using EF Core
                var posts = await _db.posts.ToListAsync();
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Text.RegularExpressions;
using var reader = await command.ExecuteReaderAsync();
using (var image = SixLabors.ImageSharp.Image.Load(normalImagePath))
                                    {
                                        // Save original size image
                                        await using (var webpFileStream = System.IO.File.Create(webpImagePath))
                                        {
                                            await image.SaveAsync(webpFileStream, new WebpEncoder { Quality = 80 });
using var mediaReader = await mediaCommand.ExecuteReaderAsync();
using SixLabors.ImageSharp.Formats.Webp;
using (var enConnection = new MySqlConnection(mysqlConnection))
                    {
                        await enConnection.OpenAsync();
using var connection = new MySqlConnection(mysqlConnection);
using var mediaConnection = new MySqlConnection(mysqlConnection);
using Microsoft.AspNetCore.Identity;
using SixLabors.ImageSharp.Processing;
using kasvol.Models;
using Microsoft.AspNetCore.Http;
using (var trConnection = new MySqlConnection(mysqlConnection))
                        {
                            await trConnection.OpenAsync();
using var command = new MySqlCommand("SELECT * FROM `posts` where `lang`='ar'", connection);
using SixLabors.ImageSharp;
using (var webpFileStream1 = System.IO.File.Create(webpImagePath1))
                                        {
                                            await image.SaveAsync(webpFileStream1, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using modern connection management
                string mysqlConnection = _configuration.GetConnectionString("constr1");
using Microsoft.Data.SqlClient;
using var trReader = await trCommand.ExecuteReaderAsync();

while (await mediaReader.ReadAsync())
                        {
                            try
                            {
                                short mediaId = mediaReader.GetInt16(0);
                                var fileName = mediaReader.IsDBNull(2) ? string.Empty : mediaReader.GetString(2).Replace("jpeg", "jpg");
                                var mediaName = mediaReader.IsDBNull(1) ? string.Empty : mediaReader.GetString(1);

                                if (string.IsNullOrEmpty(fileName) || string.IsNullOrEmpty(mediaName))
                                {
                                    continue; // Skip invalid media entries
                                }

                                var blogPath = Path.Combine(_webHostEnvironment.WebRootPath, "kasvolfactory", "vi", "blog", id.ToString());
                                var storagePath = Path.Combine(_webHostEnvironment.WebRootPath, "storage", mediaId.ToString());

                                var normalImagePath = Path.Combine(storagePath, fileName);
                                var webpFileName = $"{mediaName}.webp";
                                var webpFileName1 = $"{mediaName}1.webp";
                                var webpImagePath = Path.Combine(blogPath, webpFileName);
                                var webpImagePath1 = Path.Combine(blogPath, webpFileName1);

                                Directory.CreateDirectory(blogPath);

                                if (!fileName.EndsWith(".webp", StringComparison.OrdinalIgnoreCase) && System.IO.File.Exists(normalImagePath))
                                {
                                    using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using var enReader = await enCommand.ExecuteReaderAsync();
using MySql.Data.MySqlClient;
using EF Core
                var posts = await _db.posts.ToListAsync();
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Text.RegularExpressions;
using var reader = await command.ExecuteReaderAsync();
using (var image = SixLabors.ImageSharp.Image.Load(normalImagePath))
                                    {
                                        // Save original size image
                                        await using (var webpFileStream = System.IO.File.Create(webpImagePath))
                                        {
                                            await image.SaveAsync(webpFileStream, new WebpEncoder { Quality = 80 });
using var mediaReader = await mediaCommand.ExecuteReaderAsync();
using SixLabors.ImageSharp.Formats.Webp;
using (var enConnection = new MySqlConnection(mysqlConnection))
                    {
                        await enConnection.OpenAsync();
using var connection = new MySqlConnection(mysqlConnection);
using var mediaConnection = new MySqlConnection(mysqlConnection);
using Microsoft.AspNetCore.Identity;
using SixLabors.ImageSharp.Processing;
using kasvol.Models;
using Microsoft.AspNetCore.Http;
using (var trConnection = new MySqlConnection(mysqlConnection))
                        {
                            await trConnection.OpenAsync();
using var command = new MySqlCommand("SELECT * FROM `posts` where `lang`='ar'", connection);
using SixLabors.ImageSharp;
using (var webpFileStream1 = System.IO.File.Create(webpImagePath1))
                                        {
                                            await image.SaveAsync(webpFileStream1, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using modern connection management
                string mysqlConnection = _configuration.GetConnectionString("constr1");
using Microsoft.Data.SqlClient;
using var trReader = await trCommand.ExecuteReaderAsync();

}

                                        // Save resized image
                                        image.Mutate(x => x.Resize(new SixLabors.ImageSharp.Size(573, 521)));
                                        await using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using var enReader = await enCommand.ExecuteReaderAsync();
using MySql.Data.MySqlClient;
using EF Core
                var posts = await _db.posts.ToListAsync();
using System.IO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Text.RegularExpressions;
using var reader = await command.ExecuteReaderAsync();
using (var image = SixLabors.ImageSharp.Image.Load(normalImagePath))
                                    {
                                        // Save original size image
                                        await using (var webpFileStream = System.IO.File.Create(webpImagePath))
                                        {
                                            await image.SaveAsync(webpFileStream, new WebpEncoder { Quality = 80 });
using var mediaReader = await mediaCommand.ExecuteReaderAsync();
using SixLabors.ImageSharp.Formats.Webp;
using (var enConnection = new MySqlConnection(mysqlConnection))
                    {
                        await enConnection.OpenAsync();
using var connection = new MySqlConnection(mysqlConnection);
using var mediaConnection = new MySqlConnection(mysqlConnection);
using Microsoft.AspNetCore.Identity;
using SixLabors.ImageSharp.Processing;
using kasvol.Models;
using Microsoft.AspNetCore.Http;
using (var trConnection = new MySqlConnection(mysqlConnection))
                        {
                            await trConnection.OpenAsync();
using var command = new MySqlCommand("SELECT * FROM `posts` where `lang`='ar'", connection);
using SixLabors.ImageSharp;
using (var webpFileStream1 = System.IO.File.Create(webpImagePath1))
                                        {
                                            await image.SaveAsync(webpFileStream1, new WebpEncoder { Quality = 80 });
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using modern connection management
                string mysqlConnection = _configuration.GetConnectionString("constr1");
using Microsoft.Data.SqlClient;
using var trReader = await trCommand.ExecuteReaderAsync();

}
                                    }
                                }

                                var mediaEntry = new Models.Media
                                {
                                    arrange = 1,
                                    lang = "1", // Assuming lang '1' is for the original image
                                    guid = guid,
                                    type = "blog",
                                    name = Path.Combine("kasvolfactory", "vi", "blog", id.ToString(), webpFileName),
                                    filename = webpFileName
                                };
                                _db.medias.Add(mediaEntry);
                                await _db.SaveChangesAsync();

                                var mediaEntry1 = new Models.Media
                                {
                                    arrange = 2,
                                    lang = "2", // Assuming lang '2' is for the resized image
                                    guid = guid,
                                    type = "blog",
                                    name = Path.Combine("kasvolfactory", "vi", "blog", id.ToString(), webpFileName1),
                                    filename = webpFileName1
                                };
                                _db.medias.Add(mediaEntry1);
                                await _db.SaveChangesAsync();
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "Error processing media {MediaId}: {ErrorMessage}", "mediaId", ex.Message);
                                continue;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error accessing media database: {ErrorMessage}", ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in EditAlt: {ex.Message}");
                return Json(new { param1 = 500, param2 = "An error occurred" });
            }

            return Json(new { param1 = 200, param2 = "Done!!!" });
        }

        private string StripHTML(string input)
        {
            return Regex.Replace(input, "<.*?>", string.Empty);
        }
    }
}

