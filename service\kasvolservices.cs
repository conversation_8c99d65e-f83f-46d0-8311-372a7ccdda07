using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using kasvol.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;

namespace kasvol.service
{
    public class kasvolservices
    {
        private readonly KasvolDbContext _db;
        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public kasvolservices(KasvolDbContext db, IConfiguration configuration, IHttpContextAccessor httpContextAccessor)
        {
            _db = db;
            _configuration = configuration;
            _httpContextAccessor = httpContextAccessor;
        }
        public static bool IsArabicOrTurkish(string input)
        {
            foreach (char c in input)
            {
                UnicodeCategory category = CharUnicodeInfo.GetUnicodeCategory(c);
                if (category == UnicodeCategory.OtherLetter)
                {
                    if (c >= 0x0600 && c <= 0x06FF) // Arabic
                        return true;
                    if ((c >= 0x0100 && c <= 0x024F) || (c >= 0x1E00 && c <= 0x1EFF)) // Extended Latin (includes Turkish characters)

                        return true;
                }
            }
            return false;
        }
        public  string EncodeSpaces(string input)
        {
            return input.Replace(" ", "%20");
        }
        public  string EncodeUrlIfNeeded(string url)
        {
            if (IsArabicOrTurkish(url)) { 
                return HttpUtility.UrlEncode(url);
                 }
            return EncodeSpaces(url);
        }
        public bool ReCaptchaPassed(string gRecaptchaResponse)
        {
            HttpClient httpClient = new HttpClient();
            var kk = "6Ldf2NIiAAAAAAlb9EKi2_XbpDzRkBzLj8NNA14r";
            var res = httpClient.GetAsync($"https://www.google.com/recaptcha/api/siteverify?secret=" + kk + "&response=" + gRecaptchaResponse + "").Result;

            if (res.StatusCode != HttpStatusCode.OK)
                return false;

            string JSONres = res.Content.ReadAsStringAsync().Result;
            dynamic JSONdata = JObject.Parse(JSONres);

            if (JSONdata.success != "true")
                return false;

            return true;
        }
        public string setvalue(string lang, string value)
        {
            string value1 = FriendlyURLTitle(value);
            var obj = new langs();
            if (lang == "ar")
            {
                obj = new langs
                {

                    ar = value1,
                    en = "",
                    tr = ""
                };
            }
            else if (lang == "en")
            {
                obj = new langs
                {

                    ar = "",
                    en = value1,
                    tr = ""
                };
            }
            else
            {
                obj = new langs
                {

                    ar = "",
                    en = "",
                    tr = value1
                };


            }
            string result = JsonConvert.SerializeObject(obj);

            return result;
        }
        public string FriendlyURLTitle(string incomingText)
        {

            if (incomingText != null)
            {
                incomingText = incomingText.Replace("ş", "s");
                incomingText = incomingText.Replace("Ş", "s");
                incomingText = incomingText.Replace("İ", "i");
                incomingText = incomingText.Replace("I", "i");
                incomingText = incomingText.Replace("ı", "i");
                incomingText = incomingText.Replace("ö", "o");
                incomingText = incomingText.Replace("Ö", "o");
                incomingText = incomingText.Replace("ü", "u");
                incomingText = incomingText.Replace("Ü", "u");
                incomingText = incomingText.Replace("Ç", "c");
                incomingText = incomingText.Replace("ç", "c");
                incomingText = incomingText.Replace("ğ", "g");
                incomingText = incomingText.Replace("Ğ", "g");

                incomingText = incomingText.Replace("---", "-");
                incomingText = incomingText.Replace("?", "");
                incomingText = incomingText.Replace(":", " ");








                string encodedUrl = incomingText;
                // & ile " " yer değiştirme
                encodedUrl = Regex.Replace(encodedUrl, @"\&+", "and");
                // " " karakterlerini silme

                // geçersiz karakterleri sil

                // tekrar edenleri sil
                encodedUrl = Regex.Replace(encodedUrl, @"-+", "-");

                return encodedUrl;
            }
            else
            {
                return "";
            }
        }
        public string updatevalue(string lang, string value, string oldvalueljson)
        {
            if (oldvalueljson != null)
            {
                if (oldvalueljson.Contains("#"))
                {
                    StringBuilder sb = new StringBuilder(oldvalueljson);
                    sb.Replace("{#ar#:#", "{\"ar\":\"");
                    sb.Replace("#,#en#:#", "\",\"en\":\"");
                    sb.Replace("#}", "\"}");
                    sb.Replace("#", "\"");
                    oldvalueljson = sb.ToString();
                }
                string value1 = FriendlyURLTitle(value);

                JObject obj = JObject.Parse(oldvalueljson);
                var val = obj;

                if (lang == "ar")
                {
                    val["ar"] = value1;
                }
                else if (lang == "en")
                {
                    val["en"] = value1;
                }
                else
                {
                    val["tr"] = value1;

                }
                string result = JsonConvert.SerializeObject(obj);
                return result;
            }
            else
            {
                string result = setvalue(lang, value);
                return result;
            }
        }
        public string getvalue(string lang, string oldvalueljson)
        {
            if (oldvalueljson != null)
            {
                if (oldvalueljson.Contains("#"))
                {
                    StringBuilder sb = new StringBuilder(oldvalueljson);
                    sb.Replace("{#ar#:#", "{\"ar\":\"");
                    sb.Replace("#,#en#:#", "\",\"en\":\"");
                    sb.Replace("#}", "\"}");
                    sb.Replace(",", "@");
                    sb.Replace("\\#", "\'}");
                    sb.Replace("color: \"", "color: #");
                    oldvalueljson = sb.ToString();
                }

                JObject obj = JObject.Parse(oldvalueljson);
                var val = obj;
                string result = "";
                if (lang == "ar")
                {
                    result = val["ar"].ToString();
                }
                else if (lang == "en")
                {
                    result = val["en"].ToString();
                }
                else
                {
                    result = val["tr"].ToString();

                }
                string lang2 = "";
                if (lang == "ar") { lang2 = "en"; }
                else
                {
                    lang2 = "ar";
                }
                string value = "";
                if (result != "")
                {
                    value = result;
                }
                else { value = val[lang2].ToString(); }

                return value;
            }
            else
            {

                return "";
            }

        }
        public string getimage(string guid)
        {
            string lang = "";

            lang = (from rol in db.medias
                    where rol.guid == guid

                    select rol.name).FirstOrDefault();



            return lang;
        }
        public double tax()
        {
            string lang = "";

            lang = (from rol in db.settings
                    where rol.settingsID == 2

                    select rol.value).FirstOrDefault();

            double val = Convert.ToDouble(lang) / 100;

            return val;
        }
        public int maxdis()
        {
            string lang = "";

            lang = (from rol in db.settings
                    where rol.settingsID == 4

                    select rol.value).FirstOrDefault();

           

            return Convert.ToInt32(lang);
        }
        public string getimagewithid(string guid,string id)
        {
            string lang = "";

            lang = (from rol in db.medias
                    where rol.guid == guid && rol.lang==id

                    select rol.name).SingleOrDefault();



            return lang;
        }
        public string getpdfwithid(string guid, string id)
        {
            string lang = "";

            lang = (from rol in db.documents
                    where rol.guid == guid && rol.lang == id

                    select rol.link).SingleOrDefault();



            return lang;
        }
        public string getimageawardshome(string guid)
        {
            string lang = "";
            string type = db.awards.Where(a => a.showinhome).Select(a => a.guid).SingleOrDefault();
            lang = (from rol in db.medias
                    where rol.type == guid&&rol.guid==type

                    select rol.name).SingleOrDefault();



            return lang;
        }
        public string getimagewithtype(string guid, string id)
        {
            string lang = "";

            lang = (from rol in db.medias
                    where rol.guid == guid && rol.type == id

                    select rol.name).SingleOrDefault();



            return lang;
        }
        public int getimagewithcount(string guid)
        {
            int lang = 0;

            lang =  db.galleries.Where(a=>a.guid == guid            ).Count();



            return lang;
        }
        public int noblog(int guid)
        {
            int lang = 0;

            lang = db.posts.Where(a => a.postcatid == guid).Count();



            return lang;
        }
        public int noproduct(int guid)
        {
            int lang = 0;

            lang = db.products.Where(a => a.ProductID == guid).Count();



            return lang;
        }
        public string getdoc(string guid)
        {
            string lang = "";

            lang = (from rol in db.documents
                    where rol.guid == guid

                    select rol.title).SingleOrDefault();



            return lang;
        }
        public string getvideo(string guid,string lang1)
        {
            string lang = "";

            lang = (from rol in db.videos
                    where rol.guid == guid&&rol.lang==lang1

                    select rol.youtube).SingleOrDefault();



            return lang;
        }
        public string envisi(string guid)
        {
            int lang = 0;

            lang = (from rol in db.posts
                    where rol.guid == guid && rol.lang == "en"

                    select rol.numofvisit).SingleOrDefault();



            return lang.ToString();
        }
        public string trvisi(string guid)
        {
            int lang = 0;

            lang = (from rol in db.posts
                    where rol.guid == guid && rol.lang == "tr"

                    select rol.numofvisit).SingleOrDefault();



            return lang.ToString();
        }
        public string getg360Degree(string guid, string lang1)
        {
            string lang = "";

            lang = (from rol in db.g360s
                    where rol.guid == guid && rol.lang == lang1

                    select rol.link).SingleOrDefault();



            return lang;
        }
        public double getlat(string guid)
        {
            double lang = 0;

            lang = (from rol in db.projects
                    where rol.guid == guid && rol.lang == "ar"

                    select rol.Latitude).SingleOrDefault();



            return lang;
        }
        public double getlong(string guid)
        {
            double lang = 0;

            lang = (from rol in db.projects
                    where rol.guid == guid && rol.lang == "ar"

                    select rol.Longitude).SingleOrDefault();



            return lang;
        }
        public string getvideoimg(string guid)
        {
            string lang = "";

            lang = (from rol in db.videos
                    where rol.guid == guid && rol.lang == "ar"

                    select rol.coverimagelink).SingleOrDefault();



            return lang;
        }
        public string getg360Degreeimg(string guid)
        {
            string lang = "";

            lang = (from rol in db.g360s
                    where rol.guid == guid && rol.lang == "ar"

                    select rol.coverimagelink).SingleOrDefault();



            return  lang;
        }
        public bool isactive(string username)
        {
            bool lang = true;
            if (db.agencies.Where(a => a.username == username).Any())
            {
                lang = (from rol in db.agencies
                        where rol.username == username

                        select rol.isactive).SingleOrDefault();
            }
            else
            {
                lang = (from rol in db.employees
                        where rol.username == username

                        select rol.isactive).SingleOrDefault();

            }
            return lang;
        }
        public string namebyid(string username)
        {
            string lang;

            lang = (from rol in db.employees
                    where rol.aspuserid == username

                    select rol.name).SingleOrDefault();



            return lang;
        }
        public string agentname(int username)
        {
            string lang;

            lang = (from rol in db.agencies
                    where rol.agencyid == username

                    select rol.companyname).SingleOrDefault();



            return lang;
        }
        public string agentid(string guid)
        {
            int lang;

            lang = (from rol in db.agencies
                    where rol.guid == guid

                    select rol.agencyid).SingleOrDefault();



            return lang.ToString();
        }
        public string agentrole(string guid)
        {
            int lang;

            lang = (from rol in db.agencies
                    where rol.guid == guid

                    select rol.agencyrolesid).SingleOrDefault();



            return lang.ToString();
        }
        public string agentrolebyid(int guid)
        {
            int lang;

            lang = (from rol in db.agencies
                    where rol.agencyid == guid

                    select rol.agencyrolesid).SingleOrDefault();



            return lang.ToString();
        }
        public int countofdetails(string ordercode)
        {
            int lang;

            lang = db.orderDetails.Where(a=>a.ordercode== ordercode).Count();



            return lang;
        }
        public string  name(string username)
        {
            string lang ;

            lang = (from rol in db.employees
                    where rol.username == username

                    select rol.name).SingleOrDefault();



            return lang;
        }
        public string work(string username)
        {
            string lang;

            lang = (from rol in db.employees
                    where rol.username == username

                    select rol.name).SingleOrDefault();



            return lang;
        }
        public string getsliderimage(string guid,string type)
        {
            string lang = "";

            lang = (from rol in db.medias
                    where rol.guid == guid&&rol.lang==type

                    select rol.name).SingleOrDefault();



            return lang;
        }
        public string postcategory(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.postcats
                         where rol.postcatid == code
                         select rol.title
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string kasvolslong( string lang)
        {
            string idasp = "";
            string value = "";
           



                idasp = (from rol in db.sliders
                         where rol.slidersid == 1
                         select rol.title
                    ).Single();
                value = getvalue(lang, idasp);




            

            return value;
        }
        public string pagetitle(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.pages
                         where rol.pagesid == code
                         select rol.title
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string pageogtitle(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.pages
                         where rol.pagesid == code
                         select rol.ogtitle
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string pageogdes(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.pages
                         where rol.pagesid == code
                         select rol.ogdescription
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string getblock(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.blocks
                         where rol.blocksid == code
                         select rol.title
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string getblockc(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.blocks
                         where rol.blocksid == code
                         select rol.content
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string pagekeyword(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.pages
                         where rol.pagesid == code
                         select rol.keywords
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string project_categorietitle(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.project_Categories
                         where rol.project_categoriesid == code
                         select rol.title
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string project_categorieogtitle(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.project_Categories
                         where rol.project_categoriesid == code
                         select rol.ogtitle
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string project_categorieogdes(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.project_Categories
                         where rol.project_categoriesid == code
                         select rol.ogdescription
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string project_categoriekeyword(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.project_Categories
                         where rol.project_categoriesid == code
                         select rol.keywords
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string vidcattitle(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.videocategories
                         where rol.videocategoryid == code
                         select rol.title
                    ).SingleOrDefault();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string vidcatogtitle(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.videocategories
                         where rol.videocategoryid == code
                         select rol.ogtitle
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string vidcatogdes(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.videocategories
                         where rol.videocategoryid == code
                         select rol.ogdescription
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string vidcatkeyword(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.videocategories
                         where rol.videocategoryid == code
                         select rol.keywords
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string postcatogtitle(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.postcats
                        where rol.postcatid == code
                        select rol.ogtitle
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string postcatogdes(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp =( from rol in db.postcats
                        where rol.postcatid == code
                        select rol.ogdescription
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string postcatkeyword(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.postcats
                         where rol.postcatid == code
                         select rol.keywords
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string block(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.blocks
                         where rol.blocksid == code
                         select rol.content
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string projectcategory(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.project_Categories
                         where rol.project_categoriesid == code
                         select rol.title
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string productcategory(int? code, string lang)
        {
            string idasp = "";
            string value = "";
            if (code != null)
            {



                idasp = (from rol in db.categories
                         where rol.CategoryID == code
                         select rol.CategoryName
                    ).Single();
                value = getvalue(lang, idasp);




            }

            return value;
        }
        public string posttitle(string code, string lang)
        {
            string idasp = "";
          
            if (code != null)
            {



                idasp = (from rol in db.posts
                         where rol.guid == code&&rol.lang==lang
                         select rol.title
                    ).SingleOrDefault();
            




            }

            return idasp;
        }
        public string product(string code, string lang)
        {
            string idasp = "";

            if (code != "")
            {



                idasp = (from rol in db.products
                         where rol.guid == code && rol.lang == lang
                         select rol.ProductName
                    ).SingleOrDefault();
                if (idasp == "")
                {
                    idasp = (from rol in db.products
                             where rol.guid == code && rol.lang == "ar"
                             select rol.ProductName
                   ).SingleOrDefault();
                }




            }

            return idasp;
        }
        public string project(string code, string lang)
        {
            string idasp = "";
          
            if (code != "")
            {



                idasp = (from rol in db.projects
                         where rol.guid == code&&rol.lang==lang
                         select rol.title
                    ).SingleOrDefault();
                if (idasp == "")
                {
                    idasp = (from rol in db.projects
                             where rol.guid == code && rol.lang == "ar"
                             select rol.title
                   ).SingleOrDefault();
                }




            }

            return idasp;
        }
        public string awardtitle(string code, string lang)
        {
            string idasp = "";

            if (code != "")
            {



                idasp = (from rol in db.awards
                         where rol.guid == code 
                         select rol.title
                    ).SingleOrDefault();
            




            }
            idasp = getvalue(lang, idasp);
            return idasp;
        }
        public string projectvid(string code, string lang)
        {
            string idasp = "";

            if (code != "")
            {



                idasp = (from rol in db.projects
                         where rol.guid == code && rol.lang == lang
                         select rol.vediolink
                    ).SingleOrDefault();
                if (idasp == "")
                {
                    idasp = (from rol in db.projects
                             where rol.guid == code && rol.lang == "ar"
                             select rol.vediolink
                   ).SingleOrDefault();
                }




            }

            return idasp;
        }
        public string project360(string code, string lang)
        {
            string idasp = "";

            if (code != "")
            {



                idasp = (from rol in db.projects
                         where rol.guid == code && rol.lang == lang
                         select rol.g360link
                    ).SingleOrDefault();
                if (idasp == "")
                {
                    idasp = (from rol in db.projects
                             where rol.guid == code && rol.lang == "ar"
                             select rol.g360link
                   ).SingleOrDefault();
                }




            }

            return idasp;
        }
        public string g360(string code, string lang)
        {
            string idasp = "";

            if (code != null)
            {



                idasp = (from rol in db.g360s
                         where rol.guid == code && rol.lang == lang
                         select rol.link
                    ).SingleOrDefault();
               




            }

            return idasp;
        }
        public string g360t(string code, string lang)
        {
            string idasp = "";

            if (code != null)
            {



                idasp = (from rol in db.g360s
                         where rol.guid == code && rol.lang == lang
                         select rol.title
                    ).SingleOrDefault();





            }

            return idasp;
        }
        public string vid(string code, string lang)
        {
            string idasp = "";

            if (code != null)
            {



                idasp = (from rol in db.videos
                         where rol.guid == code && rol.lang == lang
                         select rol.youtube
                    ).SingleOrDefault();





            }

            return idasp;
        }
        public string vida(string code, string lang)
        {
            string idasp = "";

            if (code != null)
            {



                idasp = (from rol in db.videoskasvols
                         where rol.guid == code && rol.lang == lang
                         select rol.youtube
                    ).SingleOrDefault();





            }

            return idasp;
        }
        public string vidt(string code, string lang)
        {
            string idasp = "";

            if (code != null)
            {



                idasp = (from rol in db.videos
                         where rol.guid == code && rol.lang == lang
                         select rol.title
                    ).SingleOrDefault();





            }

            return idasp;
        }

        public string vidtt(string code, string lang)
        {
            string idasp = "";

            if (code != null)
            {



                idasp = (from rol in db.videoskasvols
                         where rol.guid == code && rol.lang == lang
                         select rol.title
                    ).SingleOrDefault();





            }

            return idasp;
        }
        public string vidtt1(string code, string lang)
        {
            string idasp = "";

            if (code != null)
            {



                idasp = (from rol in db.videoskasvols
                         where rol.guid == code && rol.lang == lang
                         select rol.ogdescription
                    ).SingleOrDefault();





            }

            return idasp;
        }
        public string post(string code, string lang)
        {
            string idasp = "";

            if (code != "")
            {



                idasp = (from rol in db.posts
                         where rol.guid == code && rol.lang == lang
                         select rol.title
                    ).SingleOrDefault();
                if (idasp == "")
                {
                    idasp = (from rol in db.posts
                             where rol.guid == code && rol.lang == "ar"
                             select rol.title
                   ).SingleOrDefault();
                }





            }

            return idasp;
        }
        public void pagec(string code)
        {

            SqlConnection conn;
            SqlCommand cmd101;
            //connect to the db
            conn = new SqlConnection(_configuration.GetConnectionString("kasvol"));

            //the sql command to increment hits by 1
            cmd101 = new SqlCommand("UPDATE pages SET numofvisit = numofvisit+1 WHERE pagesid=@id", conn);
            cmd101.CommandType = CommandType.Text;

            //update where Name is 'Default' which corresponds to this page
            cmd101.Parameters.AddWithValue("@id", code);

            using (conn)
            {
                //open the connection
                conn.Open();
                //send the query
                cmd101.ExecuteNonQuery();
            }





        }
        public void procat(string code)
        {

            SqlConnection conn;
            SqlCommand cmd101;
            //connect to the db
            conn = new SqlConnection(_configuration.GetConnectionString("kasvol"));

            //the sql command to increment hits by 1
            cmd101 = new SqlCommand("UPDATE project_categories SET numofvisit = numofvisit+1 WHERE project_categoriesid=@id", conn);
            cmd101.CommandType = CommandType.Text;

            //update where Name is 'Default' which corresponds to this page
            cmd101.Parameters.AddWithValue("@id", code);

            using (conn)
            {
                //open the connection
                conn.Open();
                //send the query
                cmd101.ExecuteNonQuery();
            }





        }
        public void postcats(string code)
        {

            SqlConnection conn;
            SqlCommand cmd101;
            //connect to the db
            conn = new SqlConnection(_configuration.GetConnectionString("kasvol"));

            //the sql command to increment hits by 1
            cmd101 = new SqlCommand("UPDATE postcats SET numofvisit = numofvisit+1 WHERE postcatid=@id", conn);
            cmd101.CommandType = CommandType.Text;

            //update where Name is 'Default' which corresponds to this page
            cmd101.Parameters.AddWithValue("@id", code);

            using (conn)
            {
                //open the connection
                conn.Open();
                //send the query
                cmd101.ExecuteNonQuery();
            }





        }
        public void vidcat(string code)
        {

            SqlConnection conn;
            SqlCommand cmd101;
            //connect to the db
            conn = new SqlConnection(_configuration.GetConnectionString("kasvol"));

            //the sql command to increment hits by 1
            cmd101 = new SqlCommand("UPDATE videocategories SET numofvisit = numofvisit+1 WHERE videocategoryid=@id", conn);
            cmd101.CommandType = CommandType.Text;

            //update where Name is 'Default' which corresponds to this page
            cmd101.Parameters.AddWithValue("@id", code);

            using (conn)
            {
                //open the connection
                conn.Open();
                //send the query
                cmd101.ExecuteNonQuery();
            }





        }
        public string keyword(string code)
        {
            string kk = "";
            if (code != null)
            {
                 kk = code.Replace("[{\"value\":\"", "");
                kk = kk.Replace("\"}]", "");
                kk = kk.Replace("}", "");
                kk = kk.Replace("{", "");
                kk = kk.Replace("\"value\":\"", "");
                kk = kk.Replace("\"", "");
            }
            return kk;
        
        
        }
        public List<string> keywordlist(string code)
        {
            List<string> tt = new List<string> ();
            if (code != null)
            {
                string kk = code.Replace("[{\"value\":\"", "");
                kk = kk.Replace("\"}]", "");
                kk = kk.Replace("}", "");
                kk = kk.Replace("{", "");
                kk = kk.Replace("\"value\":\"", "");
                kk = kk.Replace("\"", "");
                tt = kk.Split(',').ToList();
            }
            return tt;


        }
        public List<string> keywordlist3(string code)
        {
            List<string> tt = new List<string>();

            string kk = code.Replace("[{\"value\":\"", "");
            kk = kk.Replace("\"}]", "");
            kk = kk.Replace("}", "");
            kk = kk.Replace("{", "");
            kk = kk.Replace("\"value\":\"", "");
            kk = kk.Replace("\"", "");
            tt = kk.Split(',').ToList();
            if (tt.Count > 2)
            {
                return tt.GetRange(0, 3);
            }
            else
            {
                return tt;
            }


        }

    }
}