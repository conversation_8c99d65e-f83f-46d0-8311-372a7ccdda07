using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Data;
using kasvol.Models;
using System.Net;
using System.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using Microsoft.AspNetCore.Identity;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class agenciesController : BaseController
    {
        private ApplicationSignInManager _signInManager;
        private ApplicationUserManager _userManager;




        public ApplicationUserManager UserManager
        {
            get
            {
                return _userManager ?? HttpContext.GetOwinContext().GetUserManager<ApplicationUserManager>();
            }
            private set
            {
                _userManager = value;
            }
        }
        private kasvoldb db = new kasvoldb();
        private ApplicationDbContext dbasp = new ApplicationDbContext();
        // GET: agencies
        public async Task<ActionResult> Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            return View(await db.agencies.Include(r => r.Agencyroles).Where(a=>a.agencyid>2).ToListAsync());
        }

        // GET: agencies/Details/5
        public async Task<ActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            agency agency = await db.agencies.FindAsync(id);
            if (agency == null)
            {
                return NotFound();
            }
            return View(agency);
        }

        // GET: agencies/Create
        public ActionResult Create()
        {
            ViewBag.agencyrolesid = db.Agencyroles.ToList();
            return View();
        }

        // POST: agencies/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Create([Bind("agencyid,agentid,username,password,deposit,cmark,gmark,comark,key,agentlogo,companyname,companyemail,CompanyPhone,CompanyTaxNumber,where,facebook,twitter,linkedin,instagram,viber,whatsapp,wechat,website,country,city,address,zipcode,issubagent,parentid,MaximumApplicationLimit,isemplyee,fullname,isactive,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage,agencyrolesid")] agency agency)
        {
            agency.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            agency.user  = UserManager.GetUserId(User);
            if (ModelState.IsValid)
            {
                var userasp = new ApplicationUser { UserName = agency.username, Email = agency.companyemail };
                var result = UserManager.Create(userasp, agency.password);
                if (result.Succeeded)
                {
                    string id = "";
                    var idasp = (from rol in dbasp.Users
                                 where rol.Email == agency.companyemail
                                 select rol.Id).Single();
                    id = idasp.ToString();
                    agency.guid = id;
                    db.agencies.Add(agency);
                    await db.SaveChangesAsync();
                    addrole("2afa4683-df12-4d45-8723-de5824905224", id);
                    return RedirectToAction("Index");
                }
            }
            ViewBag.agencyrolesid = db.Agencyroles.ToList();
            return View(agency);
        }
        public void addrole(string RoleId, string UserId)
        {
            string connectionString = WebConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString;
            SqlConnection con = new SqlConnection(connectionString);
            con.Open();
            SqlCommand cmd = new SqlCommand("insert into AspNetUserRoles (UserId,RoleId) Values(@UserId,@RoleId)", con);

            cmd.Parameters.AddWithValue("@RoleId", RoleId);
            cmd.Parameters.AddWithValue("@UserId", UserId);





            cmd.ExecuteNonQuery();
            con.Close();
        }

        // GET: agencies/Edit/5
        public async Task<ActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            agency agency = await db.agencies.FindAsync(id);
            if (agency == null)
            {
                return NotFound();
            }
            ViewBag.agencyrolesid = db.Agencyroles.ToList();
            return View(agency);
        }

        // POST: agencies/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Edit([Bind("agencyid,agentid,username,password,deposit,cmark,gmark,comark,key,agentlogo,companyname,companyemail,CompanyPhone,CompanyTaxNumber,where,facebook,twitter,linkedin,instagram,viber,whatsapp,wechat,website,country,city,address,zipcode,issubagent,parentid,MaximumApplicationLimit,isemplyee,fullname,isactive,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage,agencyrolesid")] agency agency)
        {
            if (ModelState.IsValid)
            {
                var user = UserManager.FindById(agency.guid);


       
                if (agency.password != "")
                {
                    UserManager.RemovePassword(agency.guid);

                    IdentityResult result1 = UserManager.AddPassword(agency.guid, agency.password);
                }
                db.Entry(agency).State = EntityState.Modified;
                await db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            ViewBag.agencyrolesid = db.Agencyroles.ToList();
            return View(agency);
        }

        // GET: agencies/Delete/5
        public async Task<ActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            agency agency = await db.agencies.FindAsync(id);
            if (agency == null)
            {
                return NotFound();
            }
            ViewBag.agencyrolesid = db.Agencyroles.ToList();
            return View(agency);
        }

        // POST: agencies/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> DeleteConfirmed(int id)
        {
            agency agency = await db.agencies.FindAsync(id);
            db.agencies.Remove(agency);
            await db.SaveChangesAsync();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}


