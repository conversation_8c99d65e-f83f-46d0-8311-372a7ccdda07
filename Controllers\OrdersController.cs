using the lang
                                Text = _kasvolServices.getvalue(lang, r.regionname)
                            }).ToListAsync();
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Microsoft.Extensions.Configuration;
using the lang
                               Text = _kasvolServices.getvalue(lang, c.cityname)
                           }).ToListAsync();
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;

namespace kasvol.Controllers
{
    [Authorize(Roles = "SuperAdmin,Sales Manager")]
    public class OrdersController : BaseController
    {
        private readonly kasvoldb _db;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public OrdersController(kasvoldb db, IConfiguration configuration, kasvolservices kasvolServices,
            UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor)
            : base(db, configuration, kasvolServices)
        {
            _db = db;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
        }

        // Action to display products in the modal
        public async Task<IActionResult> GetProducts()
        {
            var products = await _db.products
                .Where(a => a.lang == "ar")
                .AsNoTracking()
                .Select(p => new {
                    p.Id,
                    p.Name,
                    p.UnitPrice,
                    p.length,
                    p.width,
                    p.height,
                    p.Description
                })
                .ToListAsync();
            return PartialView("_ProductSelectionPartial", products);
        }
        public class ProductOrderModel
        {
            public int Id { get; set; }
            public double? NewPrice { get; set; }
            public int Quantity { get; set; }
            public double? Trilla { get; set; }
            public double? Loading { get; set; }
        }
        // Action to add selected products to the order
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddToOrder(string products, string ordercode, bool pwithvt, bool pwithtrella, bool pwithtrellavt, bool pwithlo, bool pwithlovt, bool istotal, string sentence, double? totaltrilla = 0, double? totalloading = 0)
        {
            var productList = JsonConvert.DeserializeObject<List<ProductOrderModel>>(products);

            if (productList == null || !productList.Any())
            {
                // Handle the case where no products are selected
                TempData["Message"] = Resources.Resource.String204;
                return RedirectToAction("addproducts"); // Redirect to a summary or appropriate page
            }

            // Create an order and add selected products
            var order = await _db.orders.FirstAsync(a => a.kasvolcode == ordercode);
            if (istotal)
            {
                var agentedit = await _db.orders
                    .FirstOrDefaultAsync(d => d.OrderId == order.OrderId);
                    
                if (agentedit != null)
                {
                    agentedit.shoppingvt = pwithtrellavt;
                    agentedit.droppingvt = pwithlovt;
                    agentedit.withvt = pwithvt;
                    agentedit.isjoint = istotal;
                    agentedit.shopping = totaltrilla;
                    agentedit.dropping = totalloading;
                    agentedit.isshopping = pwithtrella;
                    agentedit.isdropping = pwithlo;
                    agentedit.sentence = sentence;
                    agentedit.isapproved = true;
                    await _db.SaveChangesAsync();
                }
            }
            else 
            {
                var agentedit = await _db.orders
                    .FirstOrDefaultAsync(d => d.OrderId == order.OrderId);
                    
                if (agentedit != null)
                {
                    agentedit.shoppingvt = pwithtrellavt;
                    agentedit.sentence = sentence;
                    agentedit.droppingvt = pwithlovt;
                    agentedit.withvt = pwithvt;
                    agentedit.isshopping = pwithtrella;
                    agentedit.isdropping = pwithlo;
                    agentedit.isapproved = true;
                    await _db.SaveChangesAsync();
                }
            }

            foreach (var productOrder in productList)
            {
                var product = await _db.products.FindAsync(productOrder.Id);
                var orderDetail = new OrderDetail
                {
                    ProductId = productOrder.Id,
                    UnitPrice = product.UnitPrice,
                    Quantity = productOrder.Quantity,
                    newUnitPrice = productOrder.NewPrice,
                    shopping = productOrder.Trilla,
                    dropping = productOrder.Loading,
                    OrderId = order.OrderId,
                    ordercode = ordercode,
                    user = _userManager.GetUserId(User),
                    IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString()
                };
                
                _db.orderDetails.Add(orderDetail);
                await _db.SaveChangesAsync();
            }
          

            TempData["Message"] = "Done...!";
            return RedirectToAction("Index"); // Redirect to the order summary page
        }
        // GET: Orders
        public async Task<IActionResult> Index(string word, int? take)
        {
            int pageSize = 50;
            if (take.HasValue)
            {
                pageSize = take.Value;
            }
            
            // Get language from cookie
            string currentLanguage = Request.Cookies["CurrentLanguage"];
            if (!string.IsNullOrEmpty(currentLanguage))
            {
                if (currentLanguage == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (currentLanguage == "en-En")
                {
                    ViewBag.ar = "English.json";
                }
                else if (currentLanguage == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }
            }
            
            var query = _db.orders.AsQueryable();
            
            if (!string.IsNullOrEmpty(word))
            {
                query = query.Where(a => a.CompanyName.Contains(word) || 
                                         a.Phone.Contains(word) || 
                                         a.Email.Contains(word));
            }
            
            query = query.OrderByDescending(a => a.OrderId);
            
            if (pageSize > 0)
            {
                return View(await query.Take(pageSize).ToListAsync());
            }
            else
            {
                return View(await query.ToListAsync());
            }
        }
        public async Task<IActionResult> ordersapp(string word, int? take)
        {
            int pageSize = take ?? 50;
            
            // Get language from cookie
            string currentLanguage = Request.Cookies["CurrentLanguage"];
            if (!string.IsNullOrEmpty(currentLanguage))
            {
                ViewBag.ar = currentLanguage switch
                {
                    "ar-AE" => "Arabic.json",
                    "en-En" => "English.json",
                    "tr-tr" => "Turkish.json",
                    _ => "English.json"
                };
            }
            
            var query = _db.orders
                .Where(a => a.isneedapproved && !a.isapproved)
                .AsNoTracking();
            
            if (!string.IsNullOrEmpty(word))
            {
                query = query.Where(a => a.CompanyName.Contains(word) || 
                                       a.Phone.Contains(word) || 
                                       a.Email.Contains(word));
            }
            
            query = query.OrderByDescending(a => a.OrderId);
            
            var orders = pageSize > 0 
                ? await query.Take(pageSize).ToListAsync()
                : await query.ToListAsync();
                
            return View(orders);
        }
        public async Task<IActionResult> sales(string word, int? take)
        {
            int pageSize = take ?? 50;
            
            // Get language from cookie
            string currentLanguage = Request.Cookies["CurrentLanguage"];
            if (!string.IsNullOrEmpty(currentLanguage))
            {
                ViewBag.ar = currentLanguage switch
                {
                    "ar-AE" => "Arabic.json",
                    "en-En" => "English.json",
                    "tr-tr" => "Turkish.json",
                    _ => "English.json"
                };
            }
            
            var query = _db.orders
                .Where(a => !a.isneedapproved && a.isapproved && !a.issold)
                .AsNoTracking();
            
            if (!string.IsNullOrEmpty(word))
            {
                query = query.Where(a => a.CompanyName.Contains(word) || 
                                       a.Phone.Contains(word) || 
                                       a.Email.Contains(word));
            }
            
            query = query.OrderByDescending(a => a.OrderId);
            
            var orders = pageSize > 0 
                ? await query.Take(pageSize).ToListAsync()
                : await query.ToListAsync();
                
            return View(orders);
        }
        public async Task<IActionResult> detect(string word, int? take)
        {
            int pageSize = take ?? 50;
            
            // Get language from cookie
            string currentLanguage = Request.Cookies["CurrentLanguage"];
            if (!string.IsNullOrEmpty(currentLanguage))
            {
                ViewBag.ar = currentLanguage switch
                {
                    "ar-AE" => "Arabic.json",
                    "en-En" => "English.json",
                    "tr-tr" => "Turkish.json",
                    _ => "English.json"
                };
            }
            
            DateTime oneWeekAgo = DateTime.Now.AddDays(-7);

            // Filter orders created within the last week and include order details
            var recentOrders = await _db.orders
                .Where(a => a.datecreated >= oneWeekAgo)
                .AsNoTracking()
                .ToListAsync();
         
            var ordersExceedingDiscount = new List<Order>();
            int maxDiscount = _kasvolServices.maxdis();
            
            foreach (var order in recentOrders)
            {
                // Fetch the order details for the current order
                var orderDetails = await _db.orderDetails
                    .Where(a => a.ordercode == order.kasvolcode)
                    .AsNoTracking()
                    .ToListAsync();

                var hasExceedingDiscount = orderDetails.Any(detail =>
                {
                    var effectivePrice = detail.newUnitPrice ?? detail.UnitPrice;
                    var discountPercentage = (decimal)(((detail.UnitPrice - effectivePrice) / detail.UnitPrice) * 100);
                    return discountPercentage > maxDiscount;
                });

                if (hasExceedingDiscount)
                {
                    ordersExceedingDiscount.Add(order);
                }
            }

            // Filter and order the results
            var filteredOrders = ordersExceedingDiscount.AsQueryable();
            
            if (!string.IsNullOrEmpty(word))
            {
                filteredOrders = filteredOrders.Where(a => 
                    a.CompanyName.Contains(word) || 
                    a.Phone.Contains(word) || 
                    a.Email.Contains(word));
            }
            
            var result = filteredOrders
                .OrderByDescending(a => a.OrderId)
                .Take(pageSize > 0 ? pageSize : int.MaxValue)
                .ToList();
                
            return View(result);
        }
        public async Task<IActionResult> DuplicateOrder(int Id)
        {
            // Retrieve the original order by its ID
            var originalOrder = await _db.orders.AsNoTracking().FirstOrDefaultAsync(o => o.OrderId == Id);

            if (originalOrder == null)
            {
                return NotFound();
            }

            // Create a new order object with copied properties
            var duplicatedOrder = new Order
            {
                FirstName = originalOrder.FirstName,
                LastName = originalOrder.LastName,
                CompanyName = originalOrder.CompanyName,
                Phone = originalOrder.Phone,
                Email = originalOrder.Email,
                address = originalOrder.address,
                datecreated = DateTime.Now,
                user = _userManager.GetUserId(User),
                IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString(),
                kasvolcode = Guid.NewGuid().ToString("N"),
                isapproved = false,
                issold = false,
                HasBeenShipped = false
            };

            await _db.orders.AddAsync(duplicatedOrder);
            await _db.SaveChangesAsync();

            TempData["Message"] = "Order duplicated successfully";
            return RedirectToAction(nameof(Index));
        }
        // GET: Orders/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            var order = await _db.orders
                .AsNoTracking()
                .FirstOrDefaultAsync(o => o.OrderId == id);
            if (order == null)
            {
                return NotFound();
            }
            return View(order);
        }

        // GET: Orders/Create
        public async Task<IActionResult> Create()
        {
            var viewModel = new OrderCreateViewModel
            {
                Countries = await _db.Countries.AsNoTracking().ToListAsync(),
                Regions = await _db.regions.AsNoTracking().ToListAsync(),
                Cities = await _db.cities.AsNoTracking().ToListAsync(),
                Agencies = await _db.agencies.Where(a => a.agencyid > 2).AsNoTracking().ToListAsync()
            };
            return View(viewModel);
        }
        public async Task<IActionResult> addproducts(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return BadRequest();
            }
            ViewBag.id = id;
            var products = await _db.products
                .Where(a => a.lang == "ar")
                .AsNoTracking()
                .ToListAsync();
            return View(products);
        }
        public async Task<IActionResult> offerprice(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return BadRequest();
            }

            var order = await _db.orders
                .AsNoTracking()
                .FirstOrDefaultAsync(a => a.kasvolcode == id);

            if (order == null)
            {
                return NotFound();
            }

            var plist = await _db.orderDetails
                .Include(od => od.Product)
                .Where(a => a.ordercode == id)
                .AsNoTracking()
                .ToListAsync();
            foreach (var item in plist)
            {
                if (item.newUnitPrice == null)
                {
                    item.newUnitPrice = item.UnitPrice;
                }
            }
            double? totaling = 0;
            if (order.isjoint)
            {
                foreach (var item in plist) {
                    double? tprice = item.newUnitPrice;
                    item.ogimage = "(" + item.Product.length + "X" + item.Product.width + "X" + item.Product.height + ")";
                    item.ogdescription = _kasvolServices.getimage(item.Product.guid);
                    if (order.withvt) {
                        tprice = Math.Round((double)(item.newUnitPrice + (item.newUnitPrice * _kasvolServices.tax())), 2);
                    }
                    if (order.shopping != 0)
                    {
                        tprice = tprice + (Math.Round((double)(order.shopping / item.Quantity), 2));
                    }
                    if (order.dropping != 0)
                    {
                        tprice = tprice + (Math.Round((double)(order.dropping / item.Quantity), 2));
                    }
                    item.newUnitPrice = tprice;
                    item.UnitPrice = Math.Round((double)(tprice * item.Quantity));
                    totaling += item.UnitPrice;
                }
            }
            else {
                foreach (var item in plist)
                {
                    double? tprice = item.newUnitPrice;
                    item.ogimage = "(" + item.Product.length + "X" + item.Product.width + "X" + item.Product.height + ")";
                    item.ogdescription = _kasvolServices.getimage(item.Product.guid);
                    if (order.withvt)
                    {
                        tprice = Math.Round((double)(item.newUnitPrice + (item.newUnitPrice * _kasvolServices.tax())), 2);
                    }
                    if (order.isshopping)
                    {
                        tprice = tprice + (Math.Round((double)(item.shopping / item.Quantity), 2));
                        if (order.shoppingvt)
                        {
                            tprice = tprice + Math.Round((double)((item.shopping * _kasvolServices.tax()) / item.Quantity), 2);
                        }
                    }
                    if (order.isdropping)
                    {
                        tprice = tprice + (Math.Round((double)(item.dropping / item.Quantity), 2));
                        if (order.droppingvt)
                        {
                            tprice = tprice + Math.Round((double)((item.shopping * _kasvolServices.tax()) / item.Quantity), 2);
                        }
                    }
                    item.newUnitPrice = tprice;
                    item.UnitPrice = Math.Round((double)(tprice * item.Quantity));
                    totaling += item.UnitPrice;
                }
            }
            ViewBag.total = totaling;
            ViewBag.plist = plist;
            return View(order);
        }
        [HttpPost]
        public async Task<IActionResult> cancelorder(string id)
        {
            var order = await _db.orders.FirstAsync(a => a.kasvolcode == id);
            
            order.HasBeenShipped = true;
            order.datemodified = DateTime.Now;
            order.modifiedIP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();
            
            await _db.SaveChangesAsync();
            
            // Perform your logic here (e.g., saving data, processing, etc.)
            bool success = true; // Example condition for success (you can replace with actual logic)

            if (success)
            {
                return Json(new { success = true, message = "Action performed successfully!" });
            }
            else
            {
                return Json(new { success = false, message = "Something went wrong, please try again!" });
            }
        }
        [HttpPost]
        public async Task<IActionResult> appearorder(string id)
        {
            var order = await _db.orders.FirstAsync(a => a.kasvolcode == id);
            
            order.isapproved = true;
            order.userapproved = _userManager.GetUserId(User);
            order.dateapproved = DateTime.Now;
            
            await _db.SaveChangesAsync();
            
            // Perform your logic here (e.g., saving data, processing, etc.)
            bool success = true; // Example condition for success (you can replace with actual logic)

            if (success)
            {
                return Json(new { success = true, message = "Action performed successfully!" });
            }
            else
            {
                return Json(new { success = false, message = "Something went wrong, please try again!" });
            }
        }
        [HttpPost]
        public async Task<IActionResult> saleorder(string id)
        {
            var order = await _db.orders.FirstAsync(a => a.kasvolcode == id);
            
            order.issold = true;
            order.datemodified = DateTime.Now;
            order.modifiedIP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();
            
            await _db.SaveChangesAsync();
            
            // Perform your logic here (e.g., saving data, processing, etc.)
            bool success = true; // Example condition for success (you can replace with actual logic)

            if (success)
            {
                return Json(new { success = true, message = "Action performed successfully!" });
            }
            else
            {
                return Json(new { success = false, message = "Something went wrong, please try again!" });
            }
        }
        // POST: Orders/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("OrderId,kasvolcode,userid,FirstName,LastName,CompanyName,Phone,Email,Total,PaymentTransactionId,HasBeenShipped,businessyear,address,issold,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage,countryid,regionid,cityid,agentid")] Order order)
        {
            order.IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();
            order.user = _userManager.GetUserId(User);
            order.datecreated = DateTime.Now;
            
            if (ModelState.IsValid)
            {
                _db.orders.Add(order);
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }

            // If we got this far, something failed, redisplay form
            ViewBag.countryid = await _db.Countries.ToListAsync();
            ViewBag.regionid = await _db.regions.ToListAsync();
            ViewBag.cityid = await _db.cities.ToListAsync();
            ViewBag.agentid = await _db.agencies.Where(a => a.agencyid > 2).ToListAsync();
            return View(order);
        }

        // GET: Orders/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            Order order = await _db.orders.FindAsync(id);
            if (order == null)
            {
                return NotFound();
            }
            return View(order);
        }
        
        [HttpGet]
        public async Task<IActionResult> GetRegions(int countryId, string lang)
        {
            var regions = await _db.regions
                           .Where(r => r.countryid == countryId)
                           .Select(r => new SelectListItem
                            {
                                Value = r.regionid.ToString(),
                                // Fetch the translated region name using the lang
                                Text = _kasvolServices.getvalue(lang, r.regionname)
                            }).ToListAsync();
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Microsoft.Extensions.Configuration;
using the lang
                               Text = _kasvolServices.getvalue(lang, c.cityname)
                           }).ToListAsync();
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;

return Json(regions);
        }

        // Action to get cities by region ID, and use lang for translation
        [HttpGet]
        public async Task<IActionResult> GetCities(int regionId, string lang)
        {
            var cities = await _db.cities
                           .Where(c => c.regionid == regionId)
                           .Select(c => new SelectListItem
                           {
                               Value = c.cityid.ToString(),
                               // Fetch the translated city name using the lang
                                Text = _kasvolServices.getvalue(lang, r.regionname)
                            }).ToListAsync();
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Microsoft.Extensions.Configuration;
using the lang
                               Text = _kasvolServices.getvalue(lang, c.cityname)
                           }).ToListAsync();
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using kasvol.service;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;

return Json(cities);
        }
        // POST: Orders/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit([Bind("OrderId,kasvolcode,userid,FirstName,LastName,CompanyName,Phone,Email,Total,PaymentTransactionId,HasBeenShipped,businessyear,address,issold,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] Order order)
        {
            order.modifiedIP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();
            order.datemodified = DateTime.Now;
            
            if (ModelState.IsValid)
            {
                _db.Entry(order).State = EntityState.Modified;
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            return View(order);
        }

        // GET: Orders/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            Order order = await _db.orders.FindAsync(id);
            if (order == null)
            {
                return NotFound();
            }
            return View(order);
        }

        // POST: Orders/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            Order order = await _db.orders.FindAsync(id);
            if (order != null)
            {
                _db.orders.Remove(order);
                await _db.SaveChangesAsync();
            }
            return RedirectToAction("Index");
        }

    }
}

