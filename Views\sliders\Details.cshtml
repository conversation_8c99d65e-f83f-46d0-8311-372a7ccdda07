@model kasvol.Models.Sliders

@{
    ViewBag.Title = @Resources.Resource.String105;
    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}


<div class="row">
    <div class="col-md-12" style="text-align:center">

        <h1 style="color:#ff0000">
            @Resources.Resource.String105
        </h1>


        <div class="m-portlet" style="margin:0 auto">
            <div class="m-portlet__head">
                <div class="m-portlet__head-caption">




                </div>
            </div>
            <div class="m-portlet__body">

                <!--begin::Section-->
                <div class="m-section">
                    <dl class="dl-horizontal">
                        <dt>
                            @Html.Label(@Resources.Resource.String4)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.title)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.subtitle)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.subtitle)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.btntitle)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.btntitle)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.link)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.link)
                        </dd>

                        <dt>
                            @Html.Label(@Resources.Resource.String83)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.arrange)
                        </dd>

                        <dt>
                            @Html.Label(@Resources.Resource.String330)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.datecreated)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.datemodified)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.datemodified)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.IP)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.IP)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.modifiedIP)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.modifiedIP)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.user)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.user)
                        </dd>

                        <dt>
                            @Html.Label(@Resources.Resource.String5)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.year)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.guid)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.guid)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.lang)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.lang)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.ogtitle)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.ogtitle)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.ogdescription)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.ogdescription)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.keywords)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.keywords)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.ogimage)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.ogimage)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.twimage)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.twimage)
                        </dd>

                    </dl>
                </div>
                <p>
                    @Html.ActionLink("Edit", "Edit", new { id = Model.slidersid }) |
                    @Html.ActionLink("Back to List", "Index")
                </p>
