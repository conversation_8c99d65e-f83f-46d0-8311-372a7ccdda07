using System;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;

namespace kasvol.Models
{
    public class Posts
    {
        [Key]
        public int Id { get; set; }
        public int arrange { get; set; }
        public string lang { get; set; }
        public string guid { get; set; }
        public string title { get; set; }
        public string ogtitle { get; set; }
        public string content { get; set; }
        public string ogdescription { get; set; }
        public string keywords { get; set; }
        public string slogan { get; set; }
        public string oldslogan { get; set; }
        public string oldslogan1 { get; set; }
        public int numofvisit { get; set; } = 0;
        public string auther { get; set; }
        public bool publish { get; set; }
        public bool allowcomments { get; set; }
        public bool allowwhatsapp { get; set; }
        public int postcatid { get; set; }
        public virtual postcat Postcat { get; set; }
    }
}
