using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace kasvol.Models
{
    public class Order : Baseclass
    {
        public int OrderId { get; set; }
        public string kasvolcode { get; set; } = Guid.NewGuid().ToString().Replace("-", string.Empty).Substring(0, 7);
       

        public string userid { get; set; }

        [StringLength(160)]
        public string FirstName { get; set; }

        
        [StringLength(160)]
        public string LastName { get; set; }
        [Required]
        public string CompanyName { get; set; }
        [Required]

      
        public string Phone { get; set; }

        

        [DataType(DataType.EmailAddress)]
        public string Email { get; set; } 

        [ScaffoldColumn(false)]
        public decimal Total { get; set; } = 0;

        [ScaffoldColumn(false)]
        public string PaymentTransactionId { get; set; }

        [ScaffoldColumn(false)]
        public bool HasBeenShipped { get; set; }
        public double? shopping { get; set; }
        public double? dropping { get; set; }
        public bool isshopping { get; set; } = false;
        public bool isdropping { get; set; } = false;
        public bool isjoint { get; set; } = false;
        public bool withvt { get; set; } = false;
        public bool shoppingvt { get; set; } = false;
        public bool droppingvt { get; set; } = false;
        public List<OrderDetail> OrderDetails { get; set; }

        public string businessyear { get; set; } = DateTime.Now.Year.ToString("yyyy");
        public string countryid { get; set; }
        public string regionid { get; set; }
        [Required]
        public string cityid { get; set; }
        public string agentid { get; set; }
        public string address { get; set; }
        public string sentence { get; set; }
        public bool issold { get; set; } = false;
        public bool isapproved { get; set; } = false;
        public string userapproved { get; set; }
        public DateTime dateapproved { get; set; } = DateTime.Now;
        public bool isneedapproved { get; set; } = false;



    }

}