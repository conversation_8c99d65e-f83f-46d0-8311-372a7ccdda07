using System.Threading.Tasks;
using System;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    public class ReviewlistsController : BaseController
    {
        public ReviewlistsController(KasvolDbContext db) : base(db)
        {
        }

        // GET: Reviewlists
        public async Task<ActionResult> Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            var reviewlists = _db.Reviewlists.Include(r => r.Product);
            return View(await reviewlists.ToListAsync());
        }

        // GET: Reviewlists/Details/5
        public async Task<ActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            Reviewlist reviewlist = await _db.Reviewlists.FindAsync(id);
            if (reviewlist == null)
            {
                return NotFound();
            }
            return View(reviewlist);
        }

        // GET: Reviewlists/Create
        public ActionResult Create()
        {
            ViewBag.ProductId = new SelectList(_db.Products, "ProductID", "ProductName");
            return View();
        }

        // POST: Reviewlists/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Create([Bind("reviewlistid,businessprofileid,rateruserid,rating,Productrating,usercommentforproduct,usercommentforstore,ProductId,ordercode,Type,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] Reviewlist reviewlist)
        {
            if (ModelState.IsValid)
            {
                _db.Reviewlists.Add(reviewlist);
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }

            ViewBag.ProductId = new SelectList(_db.Products, "ProductID", "ProductName", reviewlist.ProductId);
            return View(reviewlist);
        }

        // GET: Reviewlists/Edit/5
        public async Task<ActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            Reviewlist reviewlist = await _db.Reviewlists.FindAsync(id);
            if (reviewlist == null)
            {
                return NotFound();
            }
            ViewBag.ProductId = new SelectList(_db.Products, "ProductID", "ProductName", reviewlist.ProductId);
            return View(reviewlist);
        }

        // POST: Reviewlists/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Edit([Bind("reviewlistid,businessprofileid,rateruserid,rating,Productrating,usercommentforproduct,usercommentforstore,ProductId,ordercode,Type,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] Reviewlist reviewlist)
        {
            if (ModelState.IsValid)
            {
                _db.Entry(reviewlist).State = EntityState.Modified;
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            ViewBag.ProductId = new SelectList(_db.Products, "ProductID", "ProductName", reviewlist.ProductId);
            return View(reviewlist);
        }

        // GET: Reviewlists/Delete/5
        public async Task<ActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            Reviewlist reviewlist = await _db.Reviewlists.FindAsync(id);
            if (reviewlist == null)
            {
                return NotFound();
            }
            return View(reviewlist);
        }

        // POST: Reviewlists/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> DeleteConfirmed(int id)
        {
            Reviewlist reviewlist = await _db.Reviewlists.FindAsync(id);
            _db.Reviewlists.Remove(reviewlist);
            await _db.SaveChangesAsync();
            return RedirectToAction("Index");
        }


    }
}


