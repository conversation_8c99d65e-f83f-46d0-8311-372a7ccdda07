@model kasvol.Models.Projects
@{

    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}
@using kasvol.service;
@{kasvolservices t = new kasvolservices(); }

@{


    string value = t.project(Model.guid, ViewBag.lang);

    string valuear = t.projectvid(Model.guid.ToString(), "ar");
    string valueen = t.projectvid(Model.guid.ToString(), "en");
    string valuetr = t.projectvid(Model.guid.ToString(), "tr");

}
<div id="progress" class="modal1">
    <div class="throbber">
        <div class="curtain">
        </div>
        <div class="curtain-content">
            <div>

                <div id="preloader">
                    <div id="loader"></div>
                </div>
            </div>
        </div>
    </div></div>
    <div class="card card-custom">
        <div class="card-header flex-wrap py-5">
            <div class="card-videoar">
                <h3 class="card-label">
                    @Resources.Resource.String58


                </h3>

            </div>


        </div>
        <div class="card-body" style="margin-bottom:125px;text-align:center">


       



            @using (Ajax.BeginForm("updatevideo", "projects",
                                           new AjaxOptions
                                           {
                                               OnSuccess = "OnSuccess",
                                               OnFailure = "OnFailure",
                                               OnBegin = "onLoginBegin",
                                               LoadingElementId = "progress"
                                           }))
            {@Html.AntiForgeryToken()



            <div class="form-horizontal">


                @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                @Html.HiddenFor(model => model.projectsid)
                <div class="form-group">
                    <label class="col-sm-12 control-label no-padding-right" for="form-field-1"> Project ID </label>

                    <div class="col-sm-12">


                        @Html.DisplayFor(model => model.projectsid)
                        <h3>@value</h3>

                    </div>
                </div>

                <div class="form-group m-form__group row">
                    <div class="col-md-12 " style="text-align:center">
                        <div class="checkbox checkbox-success">
                            <label style="margin:0 auto">
                                <input class="check-box" data-val="true" data-val-required="The publish field is required." id="publish" name="publish" type="checkbox" value="true"><input name="publish" type="hidden" value="false">
                                <span class="field-validation-valid text-danger" data-valmsg-for="publish" data-valmsg-replace="true"></span>
                                <span class="lbl">In Vedio Page</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-group m-form__group row">
                    <label class="col-md-12 col-sm-12 col-form-label m--align-center" for="videoar">videoar <img alt="en" src="/assets/img/ar.png" /></label>
                    <div class="col-sm-12">
                        <input class="form-control text-box single-line" id="videoar" name="videoar" type="text" value="@valuear">
                        <span class="field-validation-valid text-danger" data-valmsg-for="videoar" data-valmsg-replace="true"></span>
                    </div>
                </div>
                <div class="form-group m-form__group row">
                    <label class="col-md-12 col-sm-12 col-form-label m--align-center" for="videoen">videoen <img alt="en" src="/assets/img/en.png" /></label>
                    <div class="col-sm-12">
                        <input class="form-control text-box single-line" id="videoen" name="videoen" type="text" value="@valueen">
                        <span class="field-validation-valid text-danger" data-valmsg-for="videoen" data-valmsg-replace="true"></span>
                    </div>
                </div>

                @*<div class="form-group m-form__group row">
                    <label class="col-md-12 col-sm-12 col-form-label m--align-center" for="videotr">videotr <img alt="en" src="/assets/img/tr.png" /></label>
                    <div class="col-sm-12">
                        <input class="form-control text-box single-line" id="videotr" name="videotr" type="text" value="@valuetr">
                        <span class="field-validation-valid text-danger" data-valmsg-for="videotr" data-valmsg-replace="true"></span>
                    </div>
                </div>*@


                <br /><br />

                <div class="form-group">
                    <div class=" col-md-12" style="text-align:center">
                        <input type="submit"  value="@Resources.Resource.String9" class="btn btn-primary" />
                    </div>
                </div>
            </div>
        }


            <div id="status">@ViewBag.mes</div>
            <div class=" col-md-12" style="text-align:center">
                @Html.ActionLink("Back to List", "Index")
            </div>
        </div>
    </div>



    <link rel="stylesheet" type="text/css" href="~/smart/css/smart-forms.css">
    <link rel="stylesheet" type="text/css" href="~/smart/css/smart-addons.css">


    @section Scripts {

        <script type="text/javascript">
            function OnSuccess() {
                location.href = '/projects/Index';
            }
            function OnFailure() {
                alert("Programmer will know this error ");
                $('#progress').hide();
            }

        </script>
        <script type="text/javascript">

            jQuery(document).ready(function ($) {

                $('#form0').validate();


            });

        </script>
        <script type="text/javascript">
            $("#form0").on("submit", function (event) {



                if ($('#form0').valid()) {
                    $('#progress').show();
                }
            });

        </script>

        <script type="text/javascript" src="~/smart/js/jquery.validate.min.js"></script>
        <script type="text/javascript" src="~/smart/js/additional-methods.min.js"></script>

    }
