using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using kasvol.service;

namespace kasvol.Controllers.Core
{
    [Authorize]
    public class citiesController : BaseController
    {
        private readonly kasvoldb _db;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public citiesController(kasvoldb db, IConfiguration configuration, kasvolservices kasvolServices,
            UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor)
            : base(db, configuration, kasvolServices)
        {
            _db = db;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
        }

        // GET: cities
        public async Task<IActionResult> Index()
        {
            // Get language from cookie
            string currentLanguage = Request.Cookies["CurrentLanguage"];
            if (!string.IsNullOrEmpty(currentLanguage))
            {
                ViewBag.ar = currentLanguage switch
                {
                    "ar-AE" => "Arabic.json",
                    "en-En" => "English.json",
                    "tr-tr" => "Turkish.json",
                    _ => "English.json"
                };
            }
            
            var cities = _db.cities.Include(c => c.Region);
            return View(await cities.ToListAsync());
        }

        // GET: cities/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            var city = await _db.cities.FindAsync(id);
            if (city == null)
            {
                return NotFound();
            }
            
            return View(city);
        }

        // GET: cities/Create
        public async Task<IActionResult> Create()
        {
            ViewBag.regionid = await _db.regions.ToListAsync();
            return View();
        }

        // POST: cities/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(city city)
        {
            city.IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            city.user = _userManager.GetUserId(User);
            city.cityname = _kasvolServices.setvalue("ar", city.cityname);
            
            if (ModelState.IsValid)
            {
                await _db.cities.AddAsync(city);
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            
            ViewBag.regionid = await _db.regions.ToListAsync();
            return View(city);
        }

        // GET: cities/Edit/5
        public async Task<IActionResult> Edit(int? id, string lang)
        {
            if (!string.IsNullOrEmpty(lang))
            {
                ViewBag.lang = lang;
            }
            
            if (id == null)
            {
                return BadRequest();
            }
            
            var city = await _db.cities.FindAsync(id);
            if (city == null)
            {
                return NotFound();
            }
            
            ViewBag.regionid = await _db.regions.ToListAsync();
            return View(city);
        }

        // POST: cities/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(city city)
        {
            city.modifiedIP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            city.datemodified = DateTime.Now;
            
            if (ModelState.IsValid)
            {
                _db.Entry(city).State = EntityState.Modified;
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            
            ViewBag.regionid = await _db.regions.ToListAsync();
            return View(city);
        }

        // GET: cities/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            var city = await _db.cities.FindAsync(id);
            if (city == null)
            {
                return NotFound();
            }
            
            return View(city);
        }

        // POST: cities/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var city = await _db.cities.FindAsync(id);
            if (city != null)
            {
                _db.cities.Remove(city);
                await _db.SaveChangesAsync();
            }
            
            return RedirectToAction("Index");
        }
    }
}
