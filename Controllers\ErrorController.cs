using System;
using kasvol.services;
using kasvol.Models;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;

Microsoft.AspNetCore.Http;
namespace kasvol.Controllers
{
    public class ErrorController : BaseController
    {
        private readonly KasvolDbContext _db;
        
        public ErrorController(KasvolDbContext db) : base()
        {
            _db = db;
        }
       
       
        public ActionResult PageError()
        {
            string url = HttpContext.Request.GetDisplayUrl();
            var uri = new Uri(url);
            var domain = GetDomainPart(url);
            var result = "https://" + domain + uri.PathAndQuery;
            string aspxerrorpath = uri.PathAndQuery;
            aspxerrorpath = aspxerrorpath.Replace("/feed/", "");
            aspxerrorpath = aspxerrorpath.Replace("/feed", "");
            string lang = "ar";
            lang = ViewBag.lang;
            if (aspxerrorpath != null)
            {
                var topcourse1 = _db.posts.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) && a.lang == "ar").SingleOrDefault();

                if (topcourse1 != null)
                {
                    ViewBag.url = "https://kasvol.com/post/" + aspxerrorpath.Replace("/", "");
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301

                    Response.StatusCode = 301;
                    Response.AddHeader("Location", "https://kasvol.com/post/" + aspxerrorpath.Replace("/", "")); // Add the new URL to the Location header
                    Response.End(); // End the response



                }
                var topcourse = _db.Projects.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) && a.lang == "ar").SingleOrDefault();

                if (topcourse != null)
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/project/" + aspxerrorpath.Replace("/", "");
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response

                }
                if (aspxerrorpath.Contains("????-???"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/contact-us";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                //if (aspxerrorpath.Contains("kasvol-gallery"))
                //{
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //    Response.StatusCode = 301;
                //    ViewBag.url = "https://kasvol.com/projects";
                //    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //    Response.End(); // End the response
                //}
                if (aspxerrorpath.Contains("kasvol-videos/contact-us")|| aspxerrorpath.Contains("project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/contact-us";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("en/kasvol-videos/contact-us") || aspxerrorpath.Contains("en/project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/en/contact-us";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/blog/page"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/blog";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                //if (aspxerrorpath.Contains("/en/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/en/projects/", "");
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    if (aspxerrorpath.Trim() != "")
                //    {

                //        Response.StatusCode = 301;
                //        ViewBag.url = "https://kasvol.com/en/project/" + aspxerrorpath.Replace("/", "");
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //    else
                //    {
                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://kasvol.com/en/projects";
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //}
                //else if (aspxerrorpath.Contains("/tr/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/tr/projects/", "");
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    if (aspxerrorpath.Trim() != "")
                //    {
                //        Response.StatusCode = 301;

                //        ViewBag.url = "https://kasvol.com/tr/project/" + aspxerrorpath.Replace("/", "");
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //    else
                //    {
                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://kasvol.com/tr/projects";
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //}
                //else if (aspxerrorpath.Contains("/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/projects/", "");
                //    if (aspxerrorpath.Trim() != "")
                //    {
                //        Response.Clear(); // Clear the response to remove any previous content
                //        Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://kasvol.com/project/" + aspxerrorpath.Replace("/", "");
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End();
                //    }

                //}

                //if (aspxerrorpath.Contains("com/about") && !aspxerrorpath.Contains("com/about-us") && !aspxerrorpath.Contains("en/about-us"))
                //{
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    Response.StatusCode = 301;
                //    ViewBag.st = "1";
                //    ViewBag.url = "https://kasvol.com/about-us";
                //    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //    Response.End(); // End the response
                //}
                if (aspxerrorpath.Contains("/post/test-post"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/blog";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }

                if (aspxerrorpath.Contains("/wp-admin/post.php?post=4749&action=edit"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/blog";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/en/en/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/en/en/", "");
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/en/" + aspxerrorpath;
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/tr/tr/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/tr/tr/", "");
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/tr/" + aspxerrorpath;
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }


                if (aspxerrorpath == "/tag/kasvol/" || aspxerrorpath == "/category/image/" || aspxerrorpath == "/projects/contact-us" || aspxerrorpath == "/blog/contact-us" || aspxerrorpath == "/Hometr/addrequests" || aspxerrorpath == "/project-category/contact-us" || aspxerrorpath == "/category/contact-us")
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;
                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/our-profile";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/projects/istanbul-apartment-core" || aspxerrorpath == "/projects/lux-apartment" || aspxerrorpath == "/projects/faster-mobile-servis" || aspxerrorpath == "/projects/fast-food-chiki-wiki" || aspxerrorpath == "/projects/villa-roneela" || aspxerrorpath == "/projects/heavy-classic" || aspxerrorpath == "/projects/baking-colony-restaurant" || aspxerrorpath == "/projects/new-apartment" || aspxerrorpath == "/projects/elit-life" || aspxerrorpath == "/projects/dubai-appartment" || aspxerrorpath == "/projects/istanbul-appartment" || aspxerrorpath == "/projects/stand-abo-shaker" || aspxerrorpath == "/projects/istanbul-marter-apartment" || aspxerrorpath == "/projects/jakamen" || aspxerrorpath == "/projects/kuwait-apartment" || aspxerrorpath == "/projects/villa-qatar" || aspxerrorpath == "/projects/berrly-beauty-salon" || aspxerrorpath == "/projects/coffee-over-82" || aspxerrorpath == "/projects/coffee-3dots")
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/project/" + aspxerrorpath.Replace("/projects/", "");
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/category/???????/"|| aspxerrorpath == "/category/???????"||aspxerrorpath.Contains("category/???????"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/category/factorys-and-interior-design";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
            }
            Response.TrySkipIisCustomErrors = true;
            return View();
        }
        [Route("404")]
        public ActionResult Page404(string aspxerrorpath)
        {
            
            string lang = "ar";
            lang = ViewBag.lang;
            if (aspxerrorpath != null)
            {
                aspxerrorpath = aspxerrorpath.Replace("/feed/", "");
                aspxerrorpath = aspxerrorpath.Replace("/feed", "");
                var topcourse1 = db.posts.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) && a.lang =="ar").SingleOrDefault();

                if (topcourse1 != null)
                {
                    ViewBag.url = "https://kasvol.com/post/" + aspxerrorpath.Replace("/", "");
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301

                    Response.StatusCode = 301;
                    Response.AddHeader("Location", "https://kasvol.com/post/" + aspxerrorpath.Replace("/", "")); // Add the new URL to the Location header
                    Response.End(); // End the response



                }
                var topcourse = _db.Projects.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) && a.lang == "ar").SingleOrDefault();

                if (topcourse != null)
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/project/" + aspxerrorpath.Replace("/", "");
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response

                }
                if (aspxerrorpath.Contains("????-???"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/contact-us";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                //if (aspxerrorpath.Contains("kasvol-gallery"))
                //{
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //    Response.StatusCode = 301;
                //    ViewBag.url = "https://kasvol.com/projects";
                //    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //    Response.End(); // End the response
                //}
                if (aspxerrorpath.Contains("kasvol-videos/contact-us") || aspxerrorpath.Contains("project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/contact-us";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("en/kasvol-videos/contact-us") || aspxerrorpath.Contains("en/project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/en/contact-us";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/blog/page"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/blog";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/en/projects/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/en/projects/", "");
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    if (aspxerrorpath.Trim() != "")
                    {

                        Response.StatusCode = 301;
                        ViewBag.url = "https://kasvol.com/en/project/" + aspxerrorpath.Replace("/", "");
                        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                        Response.End(); // End the response
                    }
                    else
                    {
                        Response.StatusCode = 301;
                        ViewBag.st = "1";
                        ViewBag.url = "https://kasvol.com/en/projects";
                        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                        Response.End(); // End the response
                    }
                }
                else if (aspxerrorpath.Contains("/tr/projects/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/tr/projects/", "");
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    if (aspxerrorpath.Trim() != "")
                    {
                        Response.StatusCode = 301;

                        ViewBag.url = "https://kasvol.com/tr/project/" + aspxerrorpath.Replace("/", "");
                        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                        Response.End(); // End the response
                    }
                    else
                    {
                        Response.StatusCode = 301;
                        ViewBag.st = "1";
                        ViewBag.url = "https://kasvol.com/tr/projects";
                        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                        Response.End(); // End the response
                    }
                }
                //else if (aspxerrorpath.Contains("/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/projects/", "");
                //    if (aspxerrorpath.Trim() != "")
                //    {
                //        Response.Clear(); // Clear the response to remove any previous content
                //        Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://kasvol.com/project/" + aspxerrorpath.Replace("/", "");
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End();
                //    }

                //}

                if (aspxerrorpath.Contains("/about/"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;
                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/about-us";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
         
                if (aspxerrorpath.Contains("/post/test-post"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/blog";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }

                if (aspxerrorpath.Contains("/wp-admin/post.php?post=4749&action=edit"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/blog";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/en/en/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/en/en/", "");
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/en/" + aspxerrorpath;
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/tr/tr/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/tr/tr/", "");
                 



                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/tr/" + aspxerrorpath;
                
                }


                if (aspxerrorpath == "/tag/kasvol/" || aspxerrorpath == "/category/image/" || aspxerrorpath == "/projects/contact-us" || aspxerrorpath == "/blog/contact-us" || aspxerrorpath == "/Hometr/addrequests" || aspxerrorpath == "/project-category/contact-us"|| aspxerrorpath == "/category/contact-us")
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;
                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/our-profile";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/projects/istanbul-apartment-core" || aspxerrorpath == "/projects/lux-apartment" || aspxerrorpath == "/projects/faster-mobile-servis" || aspxerrorpath == "/projects/fast-food-chiki-wiki" || aspxerrorpath == "/projects/villa-roneela" || aspxerrorpath == "/projects/heavy-classic" || aspxerrorpath == "/projects/baking-colony-restaurant" || aspxerrorpath == "/projects/new-apartment" || aspxerrorpath == "/projects/elit-life" || aspxerrorpath == "/projects/dubai-appartment" || aspxerrorpath == "/projects/istanbul-appartment" || aspxerrorpath == "/projects/stand-abo-shaker" || aspxerrorpath == "/projects/istanbul-marter-apartment" || aspxerrorpath == "/projects/jakamen" || aspxerrorpath == "/projects/kuwait-apartment" || aspxerrorpath == "/projects/villa-qatar" || aspxerrorpath == "/projects/berrly-beauty-salon" || aspxerrorpath == "/projects/coffee-over-82" || aspxerrorpath == "/projects/coffee-3dots")
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/project/" + aspxerrorpath.Replace("/projects/", "");
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/category/???????/"|| aspxerrorpath == "/category/???????" || aspxerrorpath.Contains("category/???????"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/category/factorys-and-interior-design";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
            }

       
          //Response.StatusCode = 404;
            ViewBag.type = "404";
            Response.TrySkipIisCustomErrors = true;
            return View("PageError");
        }
        string GetDomainPart(string url)
        {
            var doubleSlashesIndex = url.IndexOf("://");
            var start = doubleSlashesIndex != -1 ? doubleSlashesIndex + "://".Length : 0;
            var end = url.IndexOf("/", start);
            if (end == -1)
                end = url.Length;

            string trimmed = url.Substring(start, end - start);
            if (trimmed.StartsWith("www."))
                trimmed = trimmed.Substring("www.".Length);
            return trimmed;
        }
        public ActionResult Page403()
        {
            string url = HttpContext.Request.GetDisplayUrl();
            var uri = new Uri(url);
            var domain = GetDomainPart(url);
            var result = "https://" + domain + uri.PathAndQuery;
            string aspxerrorpath = uri.PathAndQuery;
            aspxerrorpath = aspxerrorpath.Replace("/feed/", "");
            aspxerrorpath = aspxerrorpath.Replace("/feed", "");

            string lang = "ar";
            lang = ViewBag.lang;
            if (aspxerrorpath != null)
            {
                var topcourse1 = _db.posts.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) ).SingleOrDefault();

                if (topcourse1 != null)
                {
                    ViewBag.url = "https://kasvol.com/post/" + aspxerrorpath.Replace("/", "");
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301

                    Response.StatusCode = 301;
                    Response.AddHeader("Location", "https://kasvol.com/post/" + aspxerrorpath.Replace("/", "")); // Add the new URL to the Location header
                    Response.End(); // End the response



                }
                var topcourse = _db.Projects.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) ).SingleOrDefault();

                if (topcourse != null)
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/project/" + aspxerrorpath.Replace("/", "");
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response

                }
                if (aspxerrorpath.Contains("????-???"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/contact-us";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                //if (aspxerrorpath.Contains("kasvol-gallery"))
                //{
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //    Response.StatusCode = 301;
                //    ViewBag.url = "https://kasvol.com/projects";
                //    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //    Response.End(); // End the response
                //}
                if (aspxerrorpath.Contains("kasvol-videos/contact-us") || aspxerrorpath.Contains("project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/contact-us";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("en/kasvol-videos/contact-us") || aspxerrorpath.Contains("en/project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/en/contact-us";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/blog/page"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/blog";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                //if (aspxerrorpath.Contains("/en/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/en/projects/", "");
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    if (aspxerrorpath.Trim() != "")
                //    {

                //        Response.StatusCode = 301;
                //        ViewBag.url = "https://kasvol.com/en/project/" + aspxerrorpath.Replace("/", "");
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //    else
                //    {
                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://kasvol.com/en/projects";
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //}
                //else if (aspxerrorpath.Contains("/tr/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/tr/projects/", "");
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    if (aspxerrorpath.Trim() != "")
                //    {
                //        Response.StatusCode = 301;

                //        ViewBag.url = "https://kasvol.com/tr/project/" + aspxerrorpath.Replace("/", "");
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //    else
                //    {
                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://kasvol.com/tr/projects";
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //}
                //else if (aspxerrorpath.Contains("/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/projects/", "");
                //    if (aspxerrorpath.Trim() != "")
                //    {
                //        Response.Clear(); // Clear the response to remove any previous content
                //        Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://kasvol.com/project/" + aspxerrorpath.Replace("/", "");
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End();
                //    }

                //}

                if (aspxerrorpath.Contains("com/about"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;
                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/about-us";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/post/test-post"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/blog";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }

                if (aspxerrorpath.Contains("/wp-admin/post.php?post=4749&action=edit"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/blog";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/en/en/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/en/en/", "");
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/en/" + aspxerrorpath;
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/tr/tr/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/tr/tr/", "");
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/tr/" + aspxerrorpath;
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }


                if (aspxerrorpath == "/tag/kasvol/" || aspxerrorpath == "/category/image/" || aspxerrorpath == "/projects/contact-us" || aspxerrorpath == "/blog/contact-us" || aspxerrorpath == "/Hometr/addrequests" || aspxerrorpath == "/project-category/contact-us" || aspxerrorpath == "/category/contact-us")
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;
                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/our-profile";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/projects/istanbul-apartment-core" || aspxerrorpath == "/projects/lux-apartment" || aspxerrorpath == "/projects/faster-mobile-servis" || aspxerrorpath == "/projects/fast-food-chiki-wiki" || aspxerrorpath == "/projects/villa-roneela" || aspxerrorpath == "/projects/heavy-classic" || aspxerrorpath == "/projects/baking-colony-restaurant" || aspxerrorpath == "/projects/new-apartment" || aspxerrorpath == "/projects/elit-life" || aspxerrorpath == "/projects/dubai-appartment" || aspxerrorpath == "/projects/istanbul-appartment" || aspxerrorpath == "/projects/stand-abo-shaker" || aspxerrorpath == "/projects/istanbul-marter-apartment" || aspxerrorpath == "/projects/jakamen" || aspxerrorpath == "/projects/kuwait-apartment" || aspxerrorpath == "/projects/villa-qatar" || aspxerrorpath == "/projects/berrly-beauty-salon" || aspxerrorpath == "/projects/coffee-over-82" || aspxerrorpath == "/projects/coffee-3dots")
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/project/" + aspxerrorpath.Replace("/projects/", "");
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/category/???????/"|| aspxerrorpath == "/category/???????" || aspxerrorpath.Contains("category/???????"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/category/factorys-and-interior-design";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
            }


            Response.StatusCode = 403;
            Response.TrySkipIisCustomErrors = true;
            return View("PageError");
        }
        [Route("500")]
        public ActionResult Page500()
        {
            string url = HttpContext.Request.GetDisplayUrl();
            var uri = new Uri(url);
            var domain = GetDomainPart(url);
            var result = "https://" + domain + uri.PathAndQuery;
            string aspxerrorpath = uri.PathAndQuery;
            aspxerrorpath = aspxerrorpath.Replace("/feed/", "");
            aspxerrorpath = aspxerrorpath.Replace("/feed", "");

            string lang = "ar";
            lang = ViewBag.lang;
            if (aspxerrorpath != null)
            {
                var topcourse1 = _db.posts.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) ).SingleOrDefault();

                if (topcourse1 != null)
                {
                    ViewBag.url = "https://kasvol.com/post/" + aspxerrorpath.Replace("/", "");
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301

                    Response.StatusCode = 301;
                    Response.AddHeader("Location", "https://kasvol.com/post/" + aspxerrorpath.Replace("/", "")); // Add the new URL to the Location header
                    Response.End(); // End the response



                }
                var topcourse = _db.Projects.Where(a => (a.slogan == aspxerrorpath.Replace("/", "") || a.oldslogan == aspxerrorpath.Replace("/", "") || a.oldslogan1 == aspxerrorpath.Replace("/", "")) ).SingleOrDefault();

                if (topcourse != null)
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/project/" + aspxerrorpath.Replace("/", "");
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response

                }
                if (aspxerrorpath.Contains("????-???"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/contact-us";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                //if (aspxerrorpath.Contains("kasvol-gallery"))
                //{
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //    Response.StatusCode = 301;
                //    ViewBag.url = "https://kasvol.com/projects";
                //    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //    Response.End(); // End the response
                //}
                if (aspxerrorpath.Contains("kasvol-videos/contact-us") || aspxerrorpath.Contains("project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/contact-us";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("en/kasvol-videos/contact-us") || aspxerrorpath.Contains("en/project-category/contact-us"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/en/contact-us";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/blog/page"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/blog";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                //if (aspxerrorpath.Contains("/en/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/en/projects/", "");
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    if (aspxerrorpath.Trim() != "")
                //    {

                //        Response.StatusCode = 301;
                //        ViewBag.url = "https://kasvol.com/en/project/" + aspxerrorpath.Replace("/", "");
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //    else
                //    {
                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://kasvol.com/en/projects";
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //}
                //else if (aspxerrorpath.Contains("/tr/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/tr/projects/", "");
                //    ViewBag.st = "1";
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    if (aspxerrorpath.Trim() != "")
                //    {
                //        Response.StatusCode = 301;

                //        ViewBag.url = "https://kasvol.com/tr/project/" + aspxerrorpath.Replace("/", "");
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //    else
                //    {
                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://kasvol.com/tr/projects";
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End(); // End the response
                //    }
                //}
                //else if (aspxerrorpath.Contains("/projects/"))
                //{
                //    aspxerrorpath = aspxerrorpath.Replace("/projects/", "");
                //    if (aspxerrorpath.Trim() != "")
                //    {
                //        Response.Clear(); // Clear the response to remove any previous content
                //        Response.Status = "301 Moved Permanently"; // Set the status code to 301


                //        Response.StatusCode = 301;
                //        ViewBag.st = "1";
                //        ViewBag.url = "https://kasvol.com/project/" + aspxerrorpath.Replace("/", "");
                //        HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //        Response.End();
                //    }

                //}

                //if (aspxerrorpath.Contains("com/about") && !aspxerrorpath.Contains("com/about-us") && !aspxerrorpath.Contains("en/about-us"))
                //{
                //    Response.Clear(); // Clear the response to remove any previous content
                //    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                //    Response.StatusCode = 301;
                //    ViewBag.st = "1";
                //    ViewBag.url = "https://kasvol.com/about-us";
                //    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                //    Response.End(); // End the response
                //}
                if (aspxerrorpath.Contains("/post/test-post"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/blog";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }

                if (aspxerrorpath.Contains("/wp-admin/post.php?post=4749&action=edit"))
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/blog";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/en/en/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/en/en/", "");
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/en/" + aspxerrorpath;
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath.Contains("/tr/tr/"))
                {
                    aspxerrorpath = aspxerrorpath.Replace("/tr/tr/", "");
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;

                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/tr/" + aspxerrorpath;
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }


                if (aspxerrorpath == "/tag/kasvol/" || aspxerrorpath == "/category/image/" || aspxerrorpath == "/projects/contact-us" || aspxerrorpath == "/blog/contact-us" || aspxerrorpath == "/Hometr/addrequests" || aspxerrorpath == "/project-category/contact-us" || aspxerrorpath == "/category/contact-us")
                {
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301
                    Response.StatusCode = 301;
                    ViewBag.st = "1";
                    ViewBag.url = "https://kasvol.com/our-profile";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/projects/istanbul-apartment-core" || aspxerrorpath == "/projects/lux-apartment" || aspxerrorpath == "/projects/faster-mobile-servis" || aspxerrorpath == "/projects/fast-food-chiki-wiki" || aspxerrorpath == "/projects/villa-roneela" || aspxerrorpath == "/projects/heavy-classic" || aspxerrorpath == "/projects/baking-colony-restaurant" || aspxerrorpath == "/projects/new-apartment" || aspxerrorpath == "/projects/elit-life" || aspxerrorpath == "/projects/dubai-appartment" || aspxerrorpath == "/projects/istanbul-appartment" || aspxerrorpath == "/projects/stand-abo-shaker" || aspxerrorpath == "/projects/istanbul-marter-apartment" || aspxerrorpath == "/projects/jakamen" || aspxerrorpath == "/projects/kuwait-apartment" || aspxerrorpath == "/projects/villa-qatar" || aspxerrorpath == "/projects/berrly-beauty-salon" || aspxerrorpath == "/projects/coffee-over-82" || aspxerrorpath == "/projects/coffee-3dots")
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/project/" + aspxerrorpath.Replace("/projects/", "");
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
                if (aspxerrorpath == "/category/???????/"|| aspxerrorpath == "/category/???????" || aspxerrorpath.Contains("category/???????"))
                {
                    ViewBag.st = "1";
                    Response.Clear(); // Clear the response to remove any previous content
                    Response.Status = "301 Moved Permanently"; // Set the status code to 301


                    Response.StatusCode = 301;
                    ViewBag.url = "https://kasvol.com/category/factorys-and-interior-design";
                    HttpContext.Response.Headers.Add("Location", ViewBag.url); // Add the new URL to the Location header
                    Response.End(); // End the response
                }
            }
            Response.StatusCode = 500;
            ViewBag.type = "500";
            Response.TrySkipIisCustomErrors = true;
            return View("PageError");
        }
    }
}

