using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;

namespace kasvol.Controllers.Core
{
    [Authorize(Roles = "SuperAdmin,Sales Manager")]
    public class OrdersController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public OrdersController(KasvolDbContext db, IConfiguration configuration, kasvolservices kasvolServices, 
            UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor)
            : base(db, configuration, kasvolServices, httpContextAccessor)
        {
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
        }

        // Action to display products in the modal
        public ActionResult GetProducts()
        {
            var products = _db.Products.Where(a => a.lang == "ar").ToList();
            return View("_ProductSelectionPartial", products);
        }

        public class ProductOrderModel
        {
            public int Id { get; set; }
            public double? NewPrice { get; set; }
            public int Quantity { get; set; }
            public double? Trilla { get; set; }
            public double? Loading { get; set; }
        }

        // Action to add selected products to the order
        [HttpPost]
        public async Task<ActionResult> AddToOrder(string products, string ordercode, bool pwithvt, bool pwithtrella, 
            bool pwithtrellavt, bool pwithlo, bool pwithlovt, bool istotal, string sentence, 
            double? totaltrilla = 0, double? totalloading = 0)
        {
            var productList = JsonConvert.DeserializeObject<List<ProductOrderModel>>(products);

            if (productList == null || !productList.Any())
            {
                // Handle the case where no products are selected
                TempData["Message"] = Resources.Resource.String204;
                return RedirectToAction("addproducts"); // Redirect to a summary or appropriate page
            }

            // Create an order and add selected products
            var order = await _db.Orders.FirstAsync(a => a.kasvolcode == ordercode);
            
            if (istotal)
            {
                order.shoppingvt = pwithtrellavt;
                order.droppingvt = pwithlovt;
                order.withvt = pwithvt;
                order.isjoint = istotal;
                order.shopping = totaltrilla;
                order.dropping = totalloading;
                order.isshopping = pwithtrella;
                order.isdropping = pwithlo;
                order.sentence = sentence;
                await _db.SaveChangesAsync();
            }
            else
            {
                order.shoppingvt = pwithtrellavt;
                order.sentence = sentence;
                order.droppingvt = pwithlovt;
                order.withvt = pwithvt;
                order.isshopping = pwithtrella;
                order.isdropping = pwithlo;
                await _db.SaveChangesAsync();
            }

            foreach (var productOrder in productList)
            {
                var product = await _db.Products.FindAsync(productOrder.Id);
                OrderDetail orderDetail = new OrderDetail
                {
                    ProductId = productOrder.Id,
                    UnitPrice = product.UnitPrice,
                    Quantity = productOrder.Quantity,
                    newUnitPrice = productOrder.NewPrice,
                    shopping = productOrder.Trilla,
                    dropping = productOrder.Loading,
                    OrderId = order.OrderId,
                    ordercode = ordercode,
                    user = _userManager.GetUserId(User),
                    IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString()
                };
                _db.OrderDetails.Add(orderDetail);
            }
            await _db.SaveChangesAsync();

            TempData["Message"] = "Done...!";
            return RedirectToAction("Index"); // Redirect to the order summary page
        }

        // GET: Orders
        public async Task<ActionResult> Index(string word, int? take)
        {
            int pageSize = 50;
            if (take.HasValue)
            {
                pageSize = take.Value;
            }

            var query = _db.Orders.AsQueryable();

            if (!string.IsNullOrEmpty(word))
            {
                query = query.Where(a => a.CompanyName.Contains(word) || 
                                         a.Phone.Contains(word) || 
                                         a.Email.Contains(word));
            }

            if (pageSize > 0)
            {
                return View(await query.OrderByDescending(a => a.OrderId).Take(pageSize).ToListAsync());
            }
            else
            {
                return View(await query.OrderByDescending(a => a.OrderId).ToListAsync());
            }
        }

        // GET: Orders/Details/5
        public async Task<ActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }

            Order order = await _db.Orders.FindAsync(id);
            if (order == null)
            {
                return NotFound();
            }

            return View(order);
        }

        // GET: Orders/Create
        public ActionResult Create()
        {
            ViewBag.countryid = _db.Countries.ToList();
            ViewBag.regionid = _db.Regions.ToList();
            ViewBag.cityid = _db.Cities.ToList();
            return View();
        }

        // POST: Orders/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Create([Bind("OrderId,kasvolcode,userid,FirstName,LastName,CompanyName,Phone,Email,Total,PaymentTransactionId,HasBeenShipped,businessyear,address,issold,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage,countryid,regionid,cityid,agentid")] Order order)
        {
            order.IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();
            order.user = _userManager.GetUserId(User);

            if (ModelState.IsValid)
            {
                _db.Orders.Add(order);
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }

            return View(order);
        }

        // GET: Orders/Edit/5
        public async Task<ActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }

            Order order = await _db.Orders.FindAsync(id);
            if (order == null)
            {
                return NotFound();
            }

            ViewBag.countryid = _db.Countries.ToList();
            ViewBag.regionid = _db.regions.ToList();
            ViewBag.cityid = _db.cities.ToList();
            return View(order);
        }

        // POST: Orders/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Edit([Bind("OrderId,kasvolcode,userid,FirstName,LastName,CompanyName,Phone,Email,Total,PaymentTransactionId,HasBeenShipped,businessyear,address,issold,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage,countryid,regionid,cityid,agentid")] Order order)
        {
            order.modifiedIP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();

            if (ModelState.IsValid)
            {
                _db.Entry(order).State = EntityState.Modified;
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }

            return View(order);
        }

        // GET: Orders/Delete/5
        public async Task<ActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }

            Order order = await _db.orders.FindAsync(id);
            if (order == null)
            {
                return NotFound();
            }

            return View(order);
        }

        // POST: Orders/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> DeleteConfirmed(int id)
        {
            Order order = await _db.orders.FindAsync(id);
            _db.orders.Remove(order);
            await _db.SaveChangesAsync();
            return RedirectToAction("Index");
        }

        [HttpGet]
        public async Task<JsonResult> GetRegions(int countryId, string lang)
        {
            var regions = await _db.regions
                            .Where(r => r.countryid == countryId)
                            .Select(r => new { Value = r.regionid.ToString(), Text = _kasvolServices.getvalue(lang, r.regionname) })
                            .ToListAsync();

            return Json(regions);
        }

        [HttpGet]
        public async Task<JsonResult> GetCities(int regionId, string lang)
        {
            var cities = await _db.cities
                           .Where(c => c.regionid == regionId)
                           .Select(c => new { Value = c.cityid.ToString(), Text = _kasvolServices.getvalue(lang, c.cityname) })
                           .ToListAsync();

            return Json(cities);
        }

        public async Task<ActionResult> addproducts(string id)
        {
            ViewBag.id = id;
            var products = await _db.products.Where(a => a.lang == "ar").ToListAsync();
            return View(products);
        }

        [HttpPost]
        public async Task<JsonResult> cancelorder(string id)
        {
            Order order = await _db.orders.FirstAsync(a => a.kasvolcode == id);
            order.HasBeenShipped = true;
            await _db.SaveChangesAsync();
            
            return Json(new { success = true, message = "Action performed successfully!" });
        }

        public async Task<ActionResult> offerprice(string id)
        {
            Order order = await _db.orders.FirstAsync(a => a.kasvolcode == id);
            List<OrderDetail> plist = await _db.orderDetails.Where(a => a.ordercode == id).ToListAsync();
            
            foreach (var item in plist)
            {
                if (item.newUnitPrice == null)
                {
                    item.newUnitPrice = item.UnitPrice;
                }
            }
            
            double? totaling = 0;
            if (order.isjoint)
            {
                foreach (var item in plist)
                {
                    double? tprice = item.newUnitPrice;
                    item.ogimage = "(" + item.Product.length + "X" + item.Product.width + "X" + item.Product.height + ")";
                    item.ogdescription = _kasvolServices.getimage(item.Product.guid);
                    
                    if (order.withvt)
                    {
                        tprice = Math.Round((double)(item.newUnitPrice + (item.newUnitPrice * _kasvolServices.tax())), 2);
                    }
                    
                    if (order.shopping != 0)
                    {
                        tprice = tprice + (Math.Round((double)(order.shopping / item.Quantity), 2));
                    }
                    
                    if (order.dropping != 0)
                    {
                        tprice = tprice + (Math.Round((double)(order.dropping / item.Quantity), 2));
                    }
                    
                    item.newUnitPrice = tprice;
                    item.UnitPrice = Math.Round((double)(tprice * item.Quantity));
                    totaling += item.UnitPrice;
                }
            }
            else
            {
                foreach (var item in plist)
                {
                    double? tprice = item.newUnitPrice;
                    item.ogimage = "(" + item.Product.length + "X" + item.Product.width + "X" + item.Product.height + ")";
                    item.ogdescription = _kasvolServices.getimage(item.Product.guid);
                    
                    if (order.withvt)
                    {
                        tprice = Math.Round((double)(item.newUnitPrice + (item.newUnitPrice * _kasvolServices.tax())), 2);
                    }
                    
                    if (order.isshopping)
                    {
                        tprice = tprice + (Math.Round((double)(item.shopping / item.Quantity), 2));
                        if (order.shoppingvt)
                        {
                            tprice = tprice + Math.Round((double)((item.shopping * _kasvolServices.tax()) / item.Quantity), 2);
                        }
                    }
                    
                    if (order.isdropping)
                    {
                        tprice = tprice + (Math.Round((double)(item.dropping / item.Quantity), 2));
                        if (order.droppingvt)
                        {
                            tprice = tprice + Math.Round((double)((item.shopping * _kasvolServices.tax()) / item.Quantity), 2);
                        }
                    }
                    
                    item.newUnitPrice = tprice;
                    item.UnitPrice = Math.Round((double)(tprice * item.Quantity));
                    totaling += item.UnitPrice;
                }
            }
            
            ViewBag.total = totaling;
            ViewBag.plist = plist;
            return View(order);
        }
    }
}
