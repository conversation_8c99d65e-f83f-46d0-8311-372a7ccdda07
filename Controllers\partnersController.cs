using Microsoft.AspNetCore.Authorization;
using System;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class partnersController : Controller
    {
        private readonly KasvolDbContext _db;

        public partnersController(KasvolDbContext context)
        {
            _db = context;
        }

        // GET: partners
        public ActionResult Index()
        {
            return View(_db.Partners.ToList());
        }

        // GET: partners/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            partners partners = _db.Partners.Find(id);
            if (partners == null)
            {
                return NotFound();
            }
            return View(partners);
        }

        // GET: partners/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: partners/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("partnersid,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] partners partners)
        {
            if (ModelState.IsValid)
            {
                _db.Partners.Add(partners);
                _db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(partners);
        }

        // GET: partners/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            partners partners = _db.Partners.Find(id);
            if (partners == null)
            {
                return NotFound();
            }
            return View(partners);
        }

        // POST: partners/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("partnersid,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] partners partners)
        {
            if (ModelState.IsValid)
            {
                _db.Entry(partners).State = EntityState.Modified;
                _db.SaveChanges();
                return RedirectToAction("Index");
            }
            return View(partners);
        }

        // GET: partners/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            partners partners = _db.Partners.Find(id);
            if (partners == null)
            {
                return NotFound();
            }
            return View(partners);
        }

        // POST: partners/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            partners partners = _db.Partners.Find(id);
            _db.Partners.Remove(partners);
            _db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}


