using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Linq;

namespace kasvol.Filters
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false, Inherited = true)]
    public class ValidateDomainAttribute : ActionFilterAttribute
    {
        private readonly string[] _allowedDomains;

        public ValidateDomainAttribute(params string[] allowedDomains)
        {
            _allowedDomains = allowedDomains ?? throw new ArgumentNullException(nameof(allowedDomains));
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            var request = context.HttpContext.Request;
            var origin = request.Headers["Origin"].FirstOrDefault();
            var referer = request.Headers["Referer"].FirstOrDefault();

            // Check if the request comes from an allowed domain
            bool isValidDomain = false;

            if (!string.IsNullOrEmpty(origin))
            {
                isValidDomain = _allowedDomains.Any(domain => origin.StartsWith(domain, StringComparison.OrdinalIgnoreCase));
            }
            else if (!string.IsNullOrEmpty(referer))
            {
                isValidDomain = _allowedDomains.Any(domain => referer.StartsWith(domain, StringComparison.OrdinalIgnoreCase));
            }
            else
            {
                // If no origin or referer, allow the request (for direct API calls)
                isValidDomain = true;
            }

            if (!isValidDomain)
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            base.OnActionExecuting(context);
        }
    }
}