@model kasvol.Models.faqs


@{
    ViewBag.Title = Resources.Resource.String119;
    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}
@using kasvol.service;
@{kasvolservices t = new kasvolservices(); }
<div id="progress" class="modal1">
    <div class="throbber">
        <div class="curtain">
        </div>
        <div class="curtain-content">
            <div>

                <div id="preloader">
                    <div id="loader"></div>
                </div>
            </div>
        </div>
    </div>
</div><div class="card card-custom">
    <div class="card-header flex-wrap py-5">
        <div class="card-title">
            <h3 class="card-label">
                @Resources.Resource.String119
                @if (ViewBag.id != null)
                {
                    <span style="text-align: center; color: #ff0000">@Resources.Resource.String96  @t.posttitle(ViewBag.id, ViewBag.lang)</span>}

            </h3>
        </div>


    </div>
    <div class="card-body" style="margin-bottom:125px">
        <h2 class="m--font-danger m--align-center"></h2>
        <div class="row">
            <div class="col-md-12">

                @using (Ajax.BeginForm("Create", "faqs",
                                               new AjaxOptions
                                               {
                                                   OnSuccess = "OnSuccess",
                                                   OnFailure = "OnFailure",
                                                   OnBegin = "onLoginBegin",
                                                   LoadingElementId = "progress"
                                               }))
                {@Html.AntiForgeryToken()

                <div class="form-horizontal">
                    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                    <div class="form-group m-form__group row m-form__group row ">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String123 </label>


                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.question, new { htmlAttributes = new { @class = "form-control required" } })
                            @Html.ValidationMessageFor(model => model.question, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row m-form__group row ">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String124 </label>


                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.answer, new { htmlAttributes = new { @class = "form-control required" } })
                            @Html.ValidationMessageFor(model => model.answer, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group m-form__group row m-form__group row ">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String123 EN </label>


                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.oldvalue, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.oldvalue, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row m-form__group row ">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String124 EN</label>


                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.oldvalue1, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.oldvalue1, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group m-form__group row m-form__group row ">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String123 TR</label>


                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.oldvalue2, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.oldvalue2, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row m-form__group row ">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String124 TR</label>


                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.oldvalue3, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.oldvalue3, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group m-form__group row m-form__group row ">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String83 </label>


                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.arrange, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.arrange, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row m-form__group row d-none">
                        @Html.LabelFor(model => model.datecreated, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.datecreated, new { htmlAttributes = new { @class = "form-control", @Value = System.DateTime.Now.ToString("yyyy-MM-dd hh:mm") } })
                            @Html.ValidationMessageFor(model => model.datecreated, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row m-form__group row d-none">
                        @Html.LabelFor(model => model.datemodified, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.datemodified, new { htmlAttributes = new { @class = "form-control", @Value = System.DateTime.Now.ToString("yyyy-MM-dd hh:mm") } })
                            @Html.ValidationMessageFor(model => model.datemodified, "", new { @class = "text-danger" })
                        </div>
                    </div>


                    <div class="form-group m-form__group row m-form__group row d-none">
                        @Html.LabelFor(model => model.IP, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.IP, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.IP, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row m-form__group row d-none">
                        @Html.LabelFor(model => model.modifiedIP, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.modifiedIP, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.modifiedIP, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row m-form__group row d-none">
                        @Html.LabelFor(model => model.user, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.user, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.user, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row m-form__group row d-none">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String5 </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.year, new { htmlAttributes = new { @class = "form-control", @Value = System.DateTime.Now.Year.ToString() } })
                            @Html.ValidationMessageFor(model => model.year, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row m-form__group row d-none">
                        @Html.LabelFor(model => model.guid, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.guid, new { htmlAttributes = new { @class = "form-control", @Value = Guid.NewGuid().ToString() } })
                            @Html.ValidationMessageFor(model => model.guid, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    @if (ViewBag.id != null)
                    {

                        <div class="form-group m-form__group row m-form__group row d-none">
                            @Html.LabelFor(model => model.ogtitle, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.ogtitle, new { htmlAttributes = new { @class = "form-control", @Value = ViewBag.id } })
                                @Html.ValidationMessageFor(model => model.ogtitle, "", new { @class = "text-danger" })
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="form-group m-form__group row">
                            <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String96 </label>


                            <div class="col-sm-12">
                                <select class="form-control" name="ogtitle">
                                    <option selected disabled value="">@Resources.Resource.String15  </option>
                                   

                                    @foreach (var item in ViewBag.postcatid)
                                    {




                                        <option value="@item.guid">@item.title</option>
                                    }
                                </select>
                            </div>
                        </div>
                    }
                    <div class="form-group m-form__group row m-form__group row d-none">
                        @Html.LabelFor(model => model.ogdescription, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.ogdescription, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.ogdescription, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row m-form__group row d-none">
                        @Html.LabelFor(model => model.keywords, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.keywords, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.keywords, "", new { @class = "text-danger" })
                        </div>
                    </div>




                    <div class="form-group m-form__group row m-form__group">
                        <div class=" col-md-12" style="text-align:center">
                            <input type="submit" value="@Resources.Resource.String2" class="btn btn-info" />
                        </div>
                    </div>
                </div>
            }



                <div class=" col-md-12" style="text-align:center">
                    @Html.ActionLink("Back to List", "Index")
                </div>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" type="text/css" href="~/smart/css/smart-forms.css">
<link rel="stylesheet" type="text/css" href="~/smart/css/smart-addons.css">


@section Scripts {




    <script src="/dashboard/assets/js/pages/crud/forms/widgets/tagify.js"></script>
    <script type="text/javascript">
        var input = document.querySelector('input[name=keywords]');

        // initialize Tagify on the above input node reference
        new Tagify(input)
    </script>
    <script type="text/javascript">
        function OnSuccess() {
            location.href = '/faqs/Index';
        }
        function OnFailure() {
            alert("Programmer will know this error ");
            $('#progress').hide();
        }

    </script>
    <script type="text/javascript">

        jQuery(document).ready(function ($) {

            $('#form0').validate();


        });

    </script>
    <script type="text/javascript">
        $("#form0").on("submit", function (event) {



            if ($('#form0').valid()) {
                $('#progress').show();
            }
        });

    </script>

    <script type="text/javascript" src="~/smart/js/jquery.validate.min.js"></script>
    <script type="text/javascript" src="~/smart/js/additional-methods.min.js"></script>

}
