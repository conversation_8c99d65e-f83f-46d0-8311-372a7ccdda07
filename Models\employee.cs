namespace kasvol.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using NetTopologySuite.Geometries;

  
    public partial class employee:Baseclass
    {
        [Key]
        public int employeeid { get; set; }

        public string name { get; set; }

        public string telno { get; set; }
        [EmailAddress]
        public string email { get; set; }
     
        public string photolink { get; set; }
        public string aspuserid { get; set; }


        public int roleid { get; set; }
        public virtual roles roleses { get; set; }
        [Required]
    
        public string username { get; set; }
        [Required(ErrorMessage = "The password field is required.")]
        [MinLength(6, ErrorMessage = "The password must be at least 6 characters long.")]
        [RegularExpression(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*\W).+$",
             ErrorMessage = "The password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character.")]


        public string password { get; set; }
        public bool isactive { get; set; }


    }
}
