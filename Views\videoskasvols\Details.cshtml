@model kasvol.Models.VideosKasvol

@{
    ViewBag.Title = @Resources.Resource.String115;
    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}
<div class="row">
    <div class="col-md-12" style="text-align:center">

        <h1 style="color:#ff0000">
            @Resources.Resource.String115
        </h1>


        <div class="m-portlet" style="margin:0 auto">
            <div class="m-portlet__head">
                <div class="m-portlet__head-caption">




                </div>
            </div>
            <div class="m-portlet__body">

                <!--begin::Section-->
                <div class="m-section">
                    <dl class="dl-horizontal">
                        <dt>
                            @Html.Label(@Resources.Resource.String4)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.title)
                        </dd>

                        <dt>
                            @Html.DisplayNameFor(model => model.youtube)
                        </dt>

                        <dd>
                            @Html.DisplayFor(model => model.youtube)
                        </dd>

                        <dt>
                            @Html.Label(@Resources.Resource.String330)
                        </dt>

                        <dd>
                            @Model.datecreated.ToString("yyyy/MM/dd HH:mm")
                        </dd>
                        <dt>
                            @Html.Label(@Resources.Resource.String331)
                        </dt>

                        <dd>
                            @Model.datemodified.ToString("yyyy/MM/dd HH:mm")
                        </dd>



                    </dl>
                    <div class="row" style="text-align:center">
                        <div class=" col-md-12" style="text-align:center">


                            @Html.ActionLink(@Resources.Resource.String265, "Index")
                        </div>
                    </div>
                </div>

                <!--end::Section-->
                <div class="m-separator m-separator--dashed"></div>
            </div>
        </div>


    </div>
</div>
<br />
<br />


