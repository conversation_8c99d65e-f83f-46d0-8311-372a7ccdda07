using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

namespace kasvol.Controllers
{
    [Authorize]
    public class referencesController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public referencesController(KasvolDbContext db, IWebHostEnvironment webHostEnvironment, UserManager<ApplicationUser> userManager) : base(db)
        {
            _userManager = userManager;
            _webHostEnvironment = webHostEnvironment;
        }
        [HttpPost]
        public JsonResult editalt(int id, bool alt)
        {
            int dd = Convert.ToInt32(id);
            var agentedit = (from d in _db.References
                             where d.referenceid == dd
                             select d).SingleOrDefault();
            agentedit.showinhome = alt;
            _db.SaveChanges();


            dynamic showMessageString = string.Empty;
            showMessageString = new
            {
                param1 = 200,
                param2 = "Done !!!"
            };
            return Json(showMessageString, JsonRequestBehavior.AllowGet);





        }
        // GET: references
        public ActionResult Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            return View(_db.References.ToList());
        }
        public async Task<ActionResult> uploadv(int? id)
        {
            kasvolservices t = new kasvolservices(); if (id == null)
            {
                return BadRequest();
            }
             Reference page = await _db.References.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            return View(page);
        }
        [HttpPost]
        public async Task<ActionResult> uploadv(int? id, IFormFile ImageFile, IFormFile ImageFile1)
        {
            kasvolservices t = new kasvolservices();
            Reference page = await _db.References.FindAsync(id);

            string path = Server.MapPath("~/kasvolfactory/vi/reference/" + page.referenceid + "/");


            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }


            if (ImageFile != null)
            {
                try
                {
                    string normalImagePath = System.IO.Path.Combine(path, ImageFile.FileName);
                    string webPFileName = System.IO.Path.GetFileNameWithoutExtension(ImageFile.FileName) + ".webp";
                    string webPImagePath = System.IO.Path.Combine(path, webPFileName);
                    // using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                    string ext = System.IO.Path.GetExtension(ImageFile.FileName);
                    if (ext != ".webp")
                    {
                        var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                        using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                    }

                    string yy = t.getimage(page.guid);
                    if (yy != null)
                    {
                        var useredit = (from d in _db.Medias
                                        where d.guid == page.guid
                                        select d).Single();
                        useredit.name = "kasvolfactory/vi/reference/" + id + "/" + webPFileName;
                        useredit.filename = webPFileName;
                        _db.SaveChanges();

                    }
                    else
                    {
                        Media media = new Media();

                        media.arrange = 1;

                        media.guid = page.guid;

                        media.type = "reference";
                        media.name = "kasvolfactory/vi/reference/" + id + "/" + webPFileName;
                        media.filename = webPFileName;
                        _db.Medias.Add(media);
                        _db.SaveChanges();
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine("Overflow. {0}", e.Message);
                }
            }




            return RedirectToAction("Index");
        }
        // GET: clients/Details/5

        // GET: references/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            Reference reference = _db.References.Find(id);
            if (reference == null)
            {
                return NotFound();
            }
            return View(reference);
        }

        // GET: references/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: references/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create( Reference reference)
        {
            kasvolservices t = new kasvolservices();
            reference.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            reference.user = _userManager.GetUserId(User);
            reference.title = t.setvalue(reference.lang, reference.title);
            if (ModelState.IsValid)
            {
                _db.References.Add(reference);
                _db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(reference);
        }

        // GET: references/Edit/5
        public ActionResult Edit(int? id, string lang)
        {
            if (lang != null)
            {
                ViewBag.lang = lang;
            }
            if (id == null)
            {
                return BadRequest();
            }
            Reference reference = _db.References.Find(id);
            if (reference == null)
            {
                return NotFound();
            }
            return View(reference);
        }

        // POST: references/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit( Reference reference)
        {
            kasvolservices t = new kasvolservices();
            reference.title = t.updatevalue(reference.lang, reference.title, reference.oldvalue);

            reference.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();

            if (ModelState.IsValid)
            {
                var users = (from d in _db.References
                             where d.referenceid == reference.referenceid
                             select d).Single();
                users.title = reference.title;
                users.datemodified = DateTime.Now;
                users.modifiedIP = reference.modifiedIP;
                _db.SaveChanges();

                return RedirectToAction("Index");
            }
            return View(reference);
        }

        // GET: references/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            Reference reference = _db.References.Find(id);
            if (reference == null)
            {
                return NotFound();
            }
            return View(reference);
        }

        // POST: references/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            Reference reference = _db.References.Find(id);
            _db.References.Remove(reference);
            _db.SaveChanges();
            return RedirectToAction("Index");
        }


    }
}






