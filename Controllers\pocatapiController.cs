using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using System;
using kasvol.service;
using kasvol.services;
using kasvol.Models;
using kasvol.Filters;
using System.Net;
using System.Net.Http;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [ValidateDomain("https://kasvol.qa", "https://kasvol.ae", "https://kasvol.net.tr", "https://kasvol.net", "https://kasvol.info", "https://turkiyefactory.site", "https://kasvol.netlify.app", "https://kasvolfactorys.web.app")]


   
    public class pocatapiController : ControllerBase
    {
        private readonly KasvolDbContext _db;

        public pocatapiController(KasvolDbContext db)
        {
            _db = db;
        }

        [HttpGet]
        public IActionResult postcatapi(string lang, string pageNumber)
        {
            kasvolservices t = new kasvolservices();
            int defaultPageNumber = 1; // ?????? ??????????
            int pageSize = 18; // ??? ??????? ?? ?? ????

            int requestedPage;
            if (!int.TryParse(pageNumber, out requestedPage))
            {
                requestedPage = defaultPageNumber;
            }
            var initialData = _db.Postcats.Where(a => a.lang == "ar").OrderBy(a => a.postcatid).Skip((requestedPage - 1) * pageSize).Take(pageSize).ToList();
            // ????? ???????? ??? ????? ??????? ??????? (View Models) ???????? LINQ to Objects
            if (lang == "ar")
            {

                var postList = initialData.Select(p => new PViewModel
                {
                    description = t.getvalue(lang, p.ogdescription),
                    title = t.getvalue(lang, p.title),
                    link = "https://kasvol.com/category/" + p.slogan /*t.EncodeUrlIfNeeded(p.slogan)*/,
                    pubDate = p.datemodified,
                    image = "https://kasvol.com/" + t.EncodeSpaces(t.getimagewithid(p.guid, "1")),
                    pdf = t.getpdfwithid(p.guid, "gdrive")
                }).ToList();
                return Ok(postList);
            }
            else
            {

                var postList = initialData.Select(p => new PViewModel
                {
                    description = t.getvalue(lang, p.ogdescription),
                    title = t.getvalue(lang, p.title),
                    link = "https://kasvol.com/en/category/" + p.slogan /*t.EncodeUrlIfNeeded(p.slogan)*/,
                    pubDate = p.datemodified,
                    image = "https://kasvol.com/" + t.EncodeSpaces(t.getimagewithid(p.guid, "1")),
                    pdf = t.getpdfwithid(p.guid, "gdrive")
                }).ToList();
                return Ok(postList);
            }
          
        }
        
        public class PViewModel
        {
            public string title { get; set; }
            public string link { get; set; }
            public string description { get; set; }
            public DateTime pubDate { get; set; }
            public string image { get; set; }
            public string pdf { get; set; }
        }
    }
}

