using Microsoft.AspNetCore.Authorization;
using System;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class bannersController : Controller
    {
        private kasvoldb db = new kasvoldb();

        // GET: banners
        public ActionResult Index()
        {
            return View(db.banners.ToList());
        }

        // GET: banners/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            banners banners = db.banners.Find(id);
            if (banners == null)
            {
                return NotFound();
            }
            return View(banners);
        }

        // GET: banners/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: banners/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("bannersid,title,pageid,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] banners banners)
        {
            if (ModelState.IsValid)
            {
                db.banners.Add(banners);
                db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(banners);
        }

        // GET: banners/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            banners banners = db.banners.Find(id);
            if (banners == null)
            {
                return NotFound();
            }
            return View(banners);
        }

        // POST: banners/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("bannersid,title,pageid,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] banners banners)
        {
            if (ModelState.IsValid)
            {
                db.Entry(banners).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("Index");
            }
            return View(banners);
        }

        // GET: banners/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            banners banners = db.banners.Find(id);
            if (banners == null)
            {
                return NotFound();
            }
            return View(banners);
        }

        // POST: banners/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            banners banners = db.banners.Find(id);
            db.banners.Remove(banners);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}


