using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace kasvol.Models
{
    public class iconblock1 : Baseclass
    {[Key]
        public int iconblockid { get; set; }
        public string imagedes { get; set; }
        public string image { get; set; }
        public string headline { get; set; }
        [DataType(DataType.MultilineText)]        public string headdes { get; set; }
        public string headlineen { get; set; }
        [DataType(DataType.MultilineText)]        public string headdesen { get; set; }
        public string headlinefr { get; set; }
        [DataType(DataType.MultilineText)]        public string headdesfr { get; set; }
        public string headlineru { get; set; }
        [DataType(DataType.MultilineText)]        public string headdesru { get; set; }
        public int iconcontainerid { get; set; }
        public virtual iconcontainer1 Iconcontainer1 { get; set; }
    }
}