using Microsoft.AspNetCore.Authorization;
using System;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class testimonialsController : Controller
    {
        private KasvolDbContext db = new KasvolDbContext();

        // GET: testimonials
        public ActionResult Index()
        {
            return View(db.Testimonials.ToList());
        }

        // GET: testimonials/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            Testimonials testimonials = db.Testimonials.Find(id);
            if (testimonials == null)
            {
                return NotFound();
            }
            return View(testimonials);
        }

        // GET: testimonials/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: testimonials/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("testimonialsid,title,youtube,country,officail,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] Testimonials testimonials)
        {
            if (ModelState.IsValid)
            {
                db.Testimonials.Add(testimonials);
                db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(testimonials);
        }

        // GET: testimonials/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            Testimonials testimonials = db.Testimonials.Find(id);
            if (testimonials == null)
            {
                return NotFound();
            }
            return View(testimonials);
        }

        // POST: testimonials/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("testimonialsid,title,youtube,country,officail,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] Testimonials testimonials)
        {
            if (ModelState.IsValid)
            {
                db.Entry(testimonials).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("Index");
            }
            return View(testimonials);
        }

        // GET: testimonials/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            testimonials testimonials = db.testimonials.Find(id);
            if (testimonials == null)
            {
                return NotFound();
            }
            return View(testimonials);
        }

        // POST: testimonials/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            testimonials testimonials = db.testimonials.Find(id);
            db.Testimonials.Remove(testimonials);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}


