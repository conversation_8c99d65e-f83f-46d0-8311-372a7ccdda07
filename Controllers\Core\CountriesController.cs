using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using System;
using kasvol.service;
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers.Core
{
    [Authorize(Roles = "SuperAdmin")]
    public class CountriesController : BaseController
    {
        private readonly KasvolDbContext _db;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CountriesController(KasvolDbContext db, IConfiguration configuration, kasvolservices kasvolServices,
            UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor)
            : base(db, httpContextAccessor)
        {
            _db = db;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
        }

        // GET: Countries
        public async Task<ActionResult> Index(string word, int? take)
        {
            int pageSize = 50;
            if (take.HasValue)
            {
                pageSize = take.Value;
            }

            var query = _db.Countries.AsQueryable();

            if (!string.IsNullOrEmpty(word))
            {
                query = query.Where(a => a.countryname.Contains(word));
            }

            if (pageSize > 0)
            {
                return View(await query.OrderByDescending(a => a.countryid).Take(pageSize).ToListAsync());
            }
            else
            {
                return View(await query.OrderByDescending(a => a.countryid).ToListAsync());
            }
        }

        // GET: Countries/Details/5
        public async Task<ActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }

            Country country = await _db.Countries.FindAsync(id);
            if (country == null)
            {
                return NotFound();
            }

            return View(country);
        }

        // GET: Countries/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: Countries/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Create([Bind("countryid,countryname,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] Country country)
        {
            country.IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();
            country.user = _userManager.GetUserId(User);
            country.datecreated = DateTime.Now;
            country.guid = Guid.NewGuid().ToString();

            if (ModelState.IsValid)
            {
                _db.Countries.Add(country);
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }

            return View(country);
        }

        // GET: Countries/Edit/5
        public async Task<ActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }

            Country country = await _db.Countries.FindAsync(id);
            if (country == null)
            {
                return NotFound();
            }

            return View(country);
        }

        // POST: Countries/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Edit([Bind("countryid,countryname,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] Country country)
        {
            country.modifiedIP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();
            country.datemodified = DateTime.Now;

            if (ModelState.IsValid)
            {
                _db.Entry(country).State = EntityState.Modified;
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }

            return View(country);
        }

        // GET: Countries/Delete/5
        public async Task<ActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }

            Country country = await _db.Countries.FindAsync(id);
            if (country == null)
            {
                return NotFound();
            }

            return View(country);
        }

        // POST: Countries/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> DeleteConfirmed(int id)
        {
            Country country = await _db.Countries.FindAsync(id);
            _db.Countries.Remove(country);
            await _db.SaveChangesAsync();
            return RedirectToAction("Index");
        }
    }
}
