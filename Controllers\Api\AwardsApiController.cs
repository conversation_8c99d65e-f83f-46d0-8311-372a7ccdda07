using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using kasvol.Models;

namespace kasvol.Controllers.Api
{
    [ApiController]
    [Route("api/[controller]")]
    public class AwardsApiController : ControllerBase
    {
        private readonly kasvoldb _db;
        private readonly ILogger<AwardsApiController> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AwardsApiController(kasvoldb db, ILogger<AwardsApiController> logger, IHttpContextAccessor httpContextAccessor)
        {
            _db = db;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
        }

        // GET: api/Awards
        [HttpGet]
        public async Task<ActionResult<IEnumerable<awards>>> GetAwards()
        {
            try
            {
                var awards = await _db.awards
                    .OrderByDescending(a => a.datemodified ?? a.date)
                    .ToListAsync();
                return Ok(awards);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while fetching awards");
                return StatusCode(500, new { message = "An error occurred while processing your request." });
            }
        }

        // GET: api/Awards/5
        [HttpGet("{id}")]
        public async Task<ActionResult<awards>> GetAward(int id)
        {
            try
            {
                var award = await _db.awards.FindAsync(id);
                if (award == null)
                {
                    return NotFound(new { message = "Award not found" });
                }
                return Ok(award);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while fetching award {Id}", id);
                return StatusCode(500, new { message = "An error occurred while processing your request." });
            }
        }

        // POST: api/Awards
        [HttpPost]
        public async Task<ActionResult<awards>> CreateAward(awards award)
        {
            try
            {
                if (award == null)
                {
                    return BadRequest(new { message = "Invalid award data" });
                }

                award.IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                award.date = DateTime.Now;

                await _db.awards.AddAsync(award);
                await _db.SaveChangesAsync();

                return CreatedAtAction(nameof(GetAward), new { id = award.id }, award);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating award");
                return StatusCode(500, new { message = "An error occurred while creating the award." });
            }
        }

        // PUT: api/Awards/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateAward(int id, awards award)
        {
            try
            {
                if (id != award.id)
                {
                    return BadRequest(new { message = "ID mismatch" });
                }

                var existingAward = await _db.awards.FindAsync(id);
                if (existingAward == null)
                {
                    return NotFound(new { message = "Award not found" });
                }

                award.modifiedIP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                award.datemodified = DateTime.Now;

                _db.Entry(existingAward).CurrentValues.SetValues(award);
                await _db.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating award {Id}", id);
                return StatusCode(500, new { message = "An error occurred while updating the award." });
            }
        }

        // DELETE: api/Awards/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteAward(int id)
        {
            try
            {
                var award = await _db.awards.FindAsync(id);
                if (award == null)
                {
                    return NotFound(new { message = "Award not found" });
                }

                _db.awards.Remove(award);
                await _db.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting award {Id}", id);
                return StatusCode(500, new { message = "An error occurred while deleting the award." });
            }
        }
    }
}