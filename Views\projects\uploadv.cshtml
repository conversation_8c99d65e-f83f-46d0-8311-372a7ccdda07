
@model kasvol.Models.Projects
@{

    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}
@using kasvol.service;
@{kasvolservices t = new kasvolservices(); }

@{


    string value = t.project( Model.guid, ViewBag.lang);



}
<div class="card card-custom">
    <div class="card-header flex-wrap py-5">
        <div class="card-title">
            <h3 class="card-label">
                @Resources.Resource.String19


            </h3>

        </div>


    </div>
    <div class="card-body" style="margin-bottom:125px;text-align:center">


        @using (Html.BeginForm("uploadv", "projects"
, new { enctype = "multipart/form-data" }))
        {@Html.AntiForgeryToken()






        <div class="form-horizontal">


            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            @Html.HiddenFor(model => model.projectsid)
            <div class="form-group">
                <label class="col-sm-12 control-label no-padding-right" for="form-field-1"> Project ID </label>

                <div class="col-sm-12">


                    @Html.DisplayFor(model => model.projectsid)
                    <h3>@value</h3>

                </div>
            </div>


            <div class="form-group" id="user-profile-3">
                <label class="col-sm-12 control-label no-padding-right" for="form-field-1"> projects  1000*556</label>

                <div class="col-sm-12">
                    <input type="file" id="ImageFile" name="ImageFile" />
                </div>
            </div>

            <div class="form-group" id="user-profile-13">
                <label class="col-sm-12 control-label no-padding-right" for="form-field-1"> projects Before  585*600</label>

                <div class="col-sm-12">
                    <input type="file" id="before" name="before" />
                </div>
            </div>

            <div class="form-group" id="user-profile-39">
                <label class="col-sm-12 control-label no-padding-right" for="form-field-1"> projects After  585*600</label>

                <div class="col-sm-12">
                    <input type="file" id="after" name="after" />
                </div>
            </div>

            <div class="progress progress-striped">
                <div class="progress-bar progress-bar-success">0%</div>
            </div>
            <br /><br />

            <div class="form-group">
                <div class=" col-md-12" style="text-align:center">
                    <input type="submit" value="@Resources.Resource.String69" class="btn btn-primary" />
                </div>
            </div>
        </div>
    }


        <div id="status">@ViewBag.mes</div>
        <div class=" col-md-12" style="text-align:center">
            @Html.ActionLink("Back to List", "Index")
        </div>
    </div>
</div>




@section Scripts {


    <script src="/Scripts/jquery.1.7.js"></script>
    <script src="/Scripts/jquery.form.js"></script>

    <script>
                (function () {

                    var bar = $('.progress-bar');
                    var percent = $('.progress-bar');
                    var status = $('#status');

                    $('form').ajaxForm({
                        beforeSend: function () {
                            status.empty();
                            var percentVal = '0%';
                            bar.width(percentVal)
                            percent.html(percentVal);
                        },
                        uploadProgress: function (event, position, total, percentComplete) {
                            var percentVal = percentComplete + '%';
                            bar.width(percentVal)
                            percent.html(percentVal);
                        },
                        success: function () {
                            var percentVal = '100%';
                            bar.width(percentVal)
                            percent.html(percentVal);
                        },
                        complete: function (xhr) {
                            location.href = '/projects/Index';
                        }
                    });

                })();
    </script>



}




