using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await before.CopyToAsync(stream);
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using (kasvoldb db1 = new kasvoldb())
                        //{
                        //    db1.medias.Add(media1);
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await after.CopyToAsync(stream);
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(stream);
using (var context = new kasvoldb())
                        //{
                        //    var useredit = (from d in context.medias
                        //                    where d.guid == page.guid && d.lang == "2"
                        //                    select d).Single();
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

namespace kasvol.Controllers
{
    [Authorize]
    public class ProductsController : BaseController
    {
        private readonly kasvoldb _db;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IWebHostEnvironment _webHostEnvironment;
        
        public ProductsController(kasvoldb db, IConfiguration configuration, kasvolservices kasvolServices,
            UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor, IWebHostEnvironment webHostEnvironment)
            : base(db, configuration, kasvolServices)
        {
            _db = db;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
            _webHostEnvironment = webHostEnvironment;
        }

        [HttpPost]
        public async Task<IActionResult> editalt(int id, int alt)
        {
            var page = await _db.products.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }
            
            int dd = Convert.ToInt32(alt);

            // Update Arabic version
            var agenteditAr = await _db.products
                .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "ar");
                
            if (agenteditAr != null)
            {
                agenteditAr.arrange = dd;
            }
            
            // Update English version
            var agenteditEn = await _db.products
                .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "en");
                
            if (agenteditEn != null)
            {
                agenteditEn.arrange = dd;
            }
            
            // Update Turkish version
            var agenteditTr = await _db.products
                .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "tr");
                
            if (agenteditTr != null)
            {
                agenteditTr.arrange = dd;
            }
            
            await _db.SaveChangesAsync();

            return Json(new
            {
                param1 = 200,
                param2 = "Done !!!"
            });





        }
        [HttpPost]
        public async Task<IActionResult> editalt22(int id, string alt)
        {
            var page = await _db.products.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            // Update Arabic version
            var agenteditAr = await _db.products
                .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "ar");
                
            if (agenteditAr != null)
            {
                //agenteditAr.oldslogan1 = alt;
            }
            
            // Update English version
            var agenteditEn = await _db.products
                .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "en");
                
            if (agenteditEn != null)
            {
                //agenteditEn.oldslogan1 = alt;
            }
            
            // Update Turkish version
            var agenteditTr = await _db.products
                .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "tr");
                
            if (agenteditTr != null)
            {
                //agenteditTr.oldslogan1 = alt;
            }
            
            await _db.SaveChangesAsync();

            return Json(new
            {
                param1 = 200,
                param2 = "Done !!!"
            });
        }
        // GET: products
        public async Task<IActionResult> Index(string word, int? take)
        {
            int pageSize = take ?? 12;
            
            // Get language from cookie
            string currentLanguage = Request.Cookies["CurrentLanguage"];
            if (!string.IsNullOrEmpty(currentLanguage))
            {
                ViewBag.ar = currentLanguage switch
                {
                    "ar-AE" => "Arabic.json",
                    "en-En" => "English.json",
                    "tr-tr" => "Turkish.json",
                    _ => "English.json"
                };
            }
            
            var query = _db.products.AsQueryable();
              
            if (!string.IsNullOrEmpty(word))
            {
                query = query.Where(a => a.ProductName.Contains(word));
            }
            
            query = query.Where(a => a.lang == "ar").OrderByDescending(a => a.ProductID);
            
            if (take.HasValue && take.Value > 0)
            {
                query = query.Take(pageSize);
            }
            
            return View(await query.AsNoTracking().ToListAsync());

        }
        public async Task<IActionResult> uploadv(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            Product page = await _db.products.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            return View(page);
        }
        public async Task<IActionResult> updatevideo(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            Product page = await _db.products.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            return View(page);
        }
        public async Task<IActionResult> update360(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            Product page = await _db.products.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            return View(page);
        }
        public async Task<IActionResult> gallary(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            Product page = await _db.products.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            return View(page);
        }
        [HttpPost]
        public async Task<IActionResult> updatevideo(int? ProductID, string videoar, string videoen, string videotr, bool publish)
        {
            if (ProductID == null)
            {
                return BadRequest();
            }
            
            var page = await _db.products.FindAsync(ProductID);
            if (page == null)
            {
                return NotFound();
            }
            
            if (publish)
            {
                // Process Arabic video
                string yy = _kasvolServices.getvideo(page.guid, "ar");
                if (yy != null)
                {
                    var useredit = await _db.videos
                        .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "ar");
                    
                    if (useredit != null)
                    {
                        useredit.youtube = videoar;
                        useredit.title = await _db.products
                            .Where(a => a.guid == page.guid && a.lang == "ar")
                            .Select(a => a.ProductName)
                            .FirstOrDefaultAsync();
                        useredit.coverimagelink = _kasvolServices.getimagewithtype(page.guid, "product");
                    }
                }
                else
                {
                    videos media = new videos
                    {
                        arrange = 1,
                        lang = "ar",
                        guid = page.guid,
                        title = await _db.products
                            .Where(a => a.guid == page.guid && a.lang == "ar")
                            .Select(a => a.ProductName)
                            .FirstOrDefaultAsync(),
                        coverimagelink = _kasvolServices.getimagewithtype(page.guid, "product"),
                        youtube = videoar
                    };
                    
                    await _db.videos.AddAsync(media);
                }

                // Process English video
                string yy2 = _kasvolServices.getvideo(page.guid, "en");
                if (yy2 != null)
                {
                    var useredit = await _db.videos
                        .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "en");
                    
                    if (useredit != null)
                    {
                        useredit.youtube = videoen;
                        useredit.title = await _db.products
                            .Where(a => a.guid == page.guid && a.lang == "en")
                            .Select(a => a.ProductName)
                            .FirstOrDefaultAsync();
                        useredit.coverimagelink = _kasvolServices.getimagewithtype(page.guid, "product");
                    }
                }
                else
                {
                    videos media = new videos
                    {
                        arrange = 2,
                        lang = "en",
                        guid = page.guid,
                        title = await _db.products
                            .Where(a => a.guid == page.guid && a.lang == "en")
                            .Select(a => a.ProductName)
                            .FirstOrDefaultAsync(),
                        coverimagelink = _kasvolServices.getimagewithtype(page.guid, "product"),
                        youtube = videoen
                    };
                    
                    await _db.videos.AddAsync(media);
                }
                
                // Process Turkish video
                string yy1 = _kasvolServices.getvideo(page.guid, "tr");
                if (yy1 != null)
                {
                    var useredit = await _db.videos
                        .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "tr");
                    
                    if (useredit != null)
                    {
                        useredit.youtube = videotr;
                        useredit.title = await _db.products
                            .Where(a => a.guid == page.guid && a.lang == "tr")
                            .Select(a => a.ProductName)
                            .FirstOrDefaultAsync();
                        useredit.coverimagelink = _kasvolServices.getimagewithtype(page.guid, "product");
                    }
                }
                else
                {
                    videos media = new videos
                    {
                        arrange = 3,
                        lang = "tr",
                        guid = page.guid,
                        title = await _db.products
                            .Where(a => a.guid == page.guid && a.lang == "tr")
                            .Select(a => a.ProductName)
                            .FirstOrDefaultAsync(),
                        coverimagelink = _kasvolServices.getimagewithtype(page.guid, "product"),
                        youtube = videotr
                    };
                    
                    await _db.videos.AddAsync(media);
                }
            }
            else
            {
                // Update product video links directly
                var arProduct = await _db.products
                    .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "ar");
                if (arProduct != null)
                {
                    arProduct.vediolink = videoar;
                }
                
                var enProduct = await _db.products
                    .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "en");
                if (enProduct != null)
                {
                    enProduct.vediolink = videoen;
                }
                
                var trProduct = await _db.products
                    .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "tr");
                if (trProduct != null)
                {
                    trProduct.vediolink = videotr;
                }
            }
            
            await _db.SaveChangesAsync();
            return RedirectToAction("Index");
        }
        [HttpPost]
        public async Task<IActionResult> update360Degree(int? ProductID, string g360Degreear, string g360Degreeen, string g360Degreetr, bool publish)
        {
            if (ProductID == null)
            {
                return BadRequest();
            }
            
            var page = await _db.products.FindAsync(ProductID);
            if (page == null)
            {
                return NotFound();
            }
            
            if (publish)
            {
                // Process Arabic 360 degree
                string yy = _kasvolServices.getg360Degree(page.guid, "ar");
                if (yy != null)
                {
                    var useredit = await _db.g360s
                        .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "ar");
                    
                    if (useredit != null)
                    {
                        useredit.link = g360Degreear;
                        useredit.title = await _db.products
                            .Where(a => a.guid == page.guid && a.lang == "ar")
                            .Select(a => a.ProductName)
                            .FirstOrDefaultAsync();
                        useredit.coverimagelink = _kasvolServices.getimagewithtype(page.guid, "product");
                    }
                }
                else
                {
                    g360 media = new g360
                    {
                        arrange = 1,
                        lang = "ar",
                        guid = page.guid,
                        title = await _db.products
                            .Where(a => a.guid == page.guid && a.lang == "ar")
                            .Select(a => a.ProductName)
                            .FirstOrDefaultAsync(),
                        coverimagelink = _kasvolServices.getimagewithtype(page.guid, "product"),
                        link = g360Degreear
                    };
                    
                    await _db.g360s.AddAsync(media);
                }

                // Process English 360 degree
                string yy2 = _kasvolServices.getg360Degree(page.guid, "en");
                if (yy2 != null)
                {
                    var useredit = await _db.g360s
                        .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "en");
                    
                    if (useredit != null)
                    {
                        useredit.link = g360Degreeen;
                        useredit.title = await _db.products
                            .Where(a => a.guid == page.guid && a.lang == "en")
                            .Select(a => a.ProductName)
                            .FirstOrDefaultAsync();
                        useredit.coverimagelink = _kasvolServices.getimagewithtype(page.guid, "product");
                    }
                }
                else
                {
                    g360 media = new g360
                    {
                        arrange = 2,
                        lang = "en",
                        guid = page.guid,
                        title = await _db.products
                            .Where(a => a.guid == page.guid && a.lang == "en")
                            .Select(a => a.ProductName)
                            .FirstOrDefaultAsync(),
                        coverimagelink = _kasvolServices.getimagewithtype(page.guid, "product"),
                        link = g360Degreeen
                    };
                    
                    await _db.g360s.AddAsync(media);
                }
                
                // Process Turkish 360 degree
                string yy1 = _kasvolServices.getg360Degree(page.guid, "tr");
                if (yy1 != null)
                {
                    var useredit = await _db.g360s
                        .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "tr");
                    
                    if (useredit != null)
                    {
                        useredit.link = g360Degreetr;
                        useredit.title = await _db.products
                            .Where(a => a.guid == page.guid && a.lang == "tr")
                            .Select(a => a.ProductName)
                            .FirstOrDefaultAsync();
                        useredit.coverimagelink = _kasvolServices.getimagewithtype(page.guid, "product");
                    }
                }
                else
                {
                    g360 media = new g360
                    {
                        arrange = 3,
                        lang = "tr",
                        guid = page.guid,
                        title = await _db.products
                            .Where(a => a.guid == page.guid && a.lang == "tr")
                            .Select(a => a.ProductName)
                            .FirstOrDefaultAsync(),
                        coverimagelink = _kasvolServices.getimagewithtype(page.guid, "product"),
                        link = g360Degreetr
                    };
                    
                    await _db.g360s.AddAsync(media);
                }
            }
            else
            {
                // Update product 360 links directly
                var arProduct = await _db.products
                    .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "ar");
                if (arProduct != null)
                {
                    arProduct.g360link = g360Degreear;
                }
                
                var enProduct = await _db.products
                    .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "en");
                if (enProduct != null)
                {
                    enProduct.g360link = g360Degreeen;
                }
                
                var trProduct = await _db.products
                    .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "tr");
                if (trProduct != null)
                {
                    trProduct.g360link = g360Degreetr;
                }
            }
            
            await _db.SaveChangesAsync();
            return RedirectToAction("Index");
        }
        [HttpPost]
        public async Task<IActionResult> uploadv(int? id, IFormFile ImageFile, IFormFile before, IFormFile after)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            Product page = await _db.products.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            string path = Path.Combine(_webHostEnvironment.WebRootPath, "kasvolfactory", "vi", "product", page.ProductID.ToString());
            string pathb = Path.Combine(_webHostEnvironment.WebRootPath, "kasvolfactory", "vi", "product", page.ProductID.ToString(), "before");
            string patha = Path.Combine(_webHostEnvironment.WebRootPath, "kasvolfactory", "vi", "product", page.ProductID.ToString(), "after");

            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }


            if (ImageFile != null)
            {
                Size size = new Size(573, 521);
                try
                {
                    string normalImagePath = System.IO.Path.Combine(path, ImageFile.FileName);
                    string webPFileName = System.IO.Path.GetFileNameWithoutExtension(ImageFile.FileName) + ".webp";
                    //string webPFileName1 = System.IO.Path.GetFileNameWithoutExtension(ImageFile.FileName) + "1.webp";
                    string webPImagePath = System.IO.Path.Combine(path, webPFileName);
                    //string webPImagePath1 = System.IO.Path.Combine(path, webPFileName1);
                    
                    using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await before.CopyToAsync(stream);
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using (kasvoldb db1 = new kasvoldb())
                        //{
                        //    db1.medias.Add(media1);
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await after.CopyToAsync(stream);
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(stream);
using (var context = new kasvoldb())
                        //{
                        //    var useredit = (from d in context.medias
                        //                    where d.guid == page.guid && d.lang == "2"
                        //                    select d).Single();
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                    string ext = System.IO.Path.GetExtension(ImageFile.FileName);
                    if (ext != ".webp")
                    {
                        var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                        //var webPFileStream1 = new FileStream(webPImagePath1, FileMode.Create);
                        using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await before.CopyToAsync(stream);
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using (kasvoldb db1 = new kasvoldb())
                        //{
                        //    db1.medias.Add(media1);
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await after.CopyToAsync(stream);
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(stream);
using (var context = new kasvoldb())
                        //{
                        //    var useredit = (from d in context.medias
                        //                    where d.guid == page.guid && d.lang == "2"
                        //                    select d).Single();
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                        // { image.SaveAsWebp(stream, new WebpEncoder { Quality = 80 }); }
                        //   .Resize(size)
                        //           .Format(new WebPFormat())
                        //           .Quality(80)
                        //           .Save(webPFileStream1);
                    }

                    string yy = t.getimage(page.guid);
                    if (yy != null)
                    {
                        var useredit = await _db.medias
                            .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "1");
                            
                        if (useredit != null)
                        {
                            useredit.name = "kasvolfactory/vi/product/" + id + "/" + webPFileName;
                            useredit.filename = webPFileName;
                            await _db.SaveChangesAsync();
                        }
                        //using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await before.CopyToAsync(stream);
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using (kasvoldb db1 = new kasvoldb())
                        //{
                        //    db1.medias.Add(media1);
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await after.CopyToAsync(stream);
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(stream);
using (var context = new kasvoldb())
                        //{
                        //    var useredit = (from d in context.medias
                        //                    where d.guid == page.guid && d.lang == "2"
                        //                    select d).Single();
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

//    useredit.name = "kasvolfactory/vi/product/" + id + "/" + webPFileName1;
                        //    useredit.filename = webPFileName1;
                        //    context.SaveChanges();
                        //}

                    }
                    else
                    {
                        media media = new media()
                        {
                            arrange = 1,
                            lang = "1",
                            guid = page.guid,
                            type = "product",
                            name = "kasvolfactory/vi/product/" + id + "/" + webPFileName,
                            filename = webPFileName
                        };
                        
                        _db.medias.Add(media);
                        await _db.SaveChangesAsync();
                        //media media1 = new media();

                        //media1.arrange = 2;
                        //media1.lang = "2";
                        //media1.guid = page.guid;

                        //media1.type = "product";
                        //media1.name = "kasvolfactory/vi/product/" + id + "/" + webPFileName1;
                        //media1.filename = webPFileName1;
                        //using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await before.CopyToAsync(stream);
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using (kasvoldb db1 = new kasvoldb())
                        //{
                        //    db1.medias.Add(media1);
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await after.CopyToAsync(stream);
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(stream);
using (var context = new kasvoldb())
                        //{
                        //    var useredit = (from d in context.medias
                        //                    where d.guid == page.guid && d.lang == "2"
                        //                    select d).Single();
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

//    db1.SaveChanges();
                        //}
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine("Overflow. {0}", e.Message);
                }
            }

            if (before != null)
            {
                Size size = new Size(573, 521);
                try
                {
                    string normalImagePath = System.IO.Path.Combine(pathb, before.FileName);
                    string webPFileName = System.IO.Path.GetFileNameWithoutExtension(before.FileName) + ".webp";
                    //string webPFileName1 = System.IO.Path.GetFileNameWithoutExtension(before.FileName) + "1.webp";
                    string webPImagePath = System.IO.Path.Combine(pathb, webPFileName);
                    //string webPImagePath1 = System.IO.Path.Combine(pathb, webPFileName1);
                    
                    using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await before.CopyToAsync(stream);
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using (kasvoldb db1 = new kasvoldb())
                        //{
                        //    db1.medias.Add(media1);
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await after.CopyToAsync(stream);
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(stream);
using (var context = new kasvoldb())
                        //{
                        //    var useredit = (from d in context.medias
                        //                    where d.guid == page.guid && d.lang == "2"
                        //                    select d).Single();
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                    string ext = System.IO.Path.GetExtension(before.FileName);
                    if (ext != ".webp")
                    {
                        var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                        //var webPFileStream1 = new FileStream(webPImagePath1, FileMode.Create);
                        using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await before.CopyToAsync(stream);
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using (kasvoldb db1 = new kasvoldb())
                        //{
                        //    db1.medias.Add(media1);
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await after.CopyToAsync(stream);
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(stream);
using (var context = new kasvoldb())
                        //{
                        //    var useredit = (from d in context.medias
                        //                    where d.guid == page.guid && d.lang == "2"
                        //                    select d).Single();
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                        // { image.SaveAsWebp(stream, new WebpEncoder { Quality = 80 }); }
                        //   .Resize(size)
                        //           .Format(new WebPFormat())
                        //           .Quality(80)
                        //           .Save(webPFileStream1);
                    }

                    string yy = t.getimage(page.guid);
                    if (yy != null)
                    {
                        var mediaRecord = await _db.medias
                            .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "before");
                            
                        if (mediaRecord != null)
                        {
                            mediaRecord.name = "kasvolfactory/vi/product/" + id + "/before/" + webPFileName;
                            mediaRecord.filename = webPFileName;
                            await _db.SaveChangesAsync();
                        }
                        //using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await before.CopyToAsync(stream);
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using (kasvoldb db1 = new kasvoldb())
                        //{
                        //    db1.medias.Add(media1);
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await after.CopyToAsync(stream);
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(stream);
using (var context = new kasvoldb())
                        //{
                        //    var useredit = (from d in context.medias
                        //                    where d.guid == page.guid && d.lang == "2"
                        //                    select d).Single();
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

//    useredit.name = "kasvolfactory/vi/product/" + id + "/" + webPFileName1;
                        //    useredit.filename = webPFileName1;
                        //    context.SaveChanges();
                        //}

                    }
                    else
                    {
                        media media = new media()
                        {
                            arrange = 1,
                            lang = "before",
                            guid = page.guid,
                            type = "product",
                            name = "kasvolfactory/vi/product/" + id + "/before/" + webPFileName,
                            filename = webPFileName
                        };
                        
                        _db.medias.Add(media);
                        await _db.SaveChangesAsync();
                        //media media1 = new media();

                        //media1.arrange = 2;
                        //media1.lang = "2";
                        //media1.guid = page.guid;

                        //media1.type = "product";
                        //media1.name = "kasvolfactory/vi/product/" + id + "/" + webPFileName1;
                        //media1.filename = webPFileName1;
                        //using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await before.CopyToAsync(stream);
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using (kasvoldb db1 = new kasvoldb())
                        //{
                        //    db1.medias.Add(media1);
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await after.CopyToAsync(stream);
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(stream);
using (var context = new kasvoldb())
                        //{
                        //    var useredit = (from d in context.medias
                        //                    where d.guid == page.guid && d.lang == "2"
                        //                    select d).Single();
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

//    db1.SaveChanges();
                        //}
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine("Overflow. {0}", e.Message);
                }
            }

            if (after != null)
            {
                Size size = new Size(573, 521);
                try
                {
                    string normalImagePath = System.IO.Path.Combine(patha, after.FileName);
                    string webPFileName = System.IO.Path.GetFileNameWithoutExtension(after.FileName) + ".webp";
                    //string webPFileName1 = System.IO.Path.GetFileNameWithoutExtension(after.FileName) + "1.webp";
                    string webPImagePath = System.IO.Path.Combine(patha, webPFileName);
                    //string webPImagePath1 = System.IO.Path.Combine(patha, webPFileName1);
                    
                    using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await before.CopyToAsync(stream);
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using (kasvoldb db1 = new kasvoldb())
                        //{
                        //    db1.medias.Add(media1);
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await after.CopyToAsync(stream);
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(stream);
using (var context = new kasvoldb())
                        //{
                        //    var useredit = (from d in context.medias
                        //                    where d.guid == page.guid && d.lang == "2"
                        //                    select d).Single();
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                    string ext = System.IO.Path.GetExtension(after.FileName);
                    if (ext != ".webp")
                    {
                        var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                        //var webPFileStream1 = new FileStream(webPImagePath1, FileMode.Create);
                        using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await before.CopyToAsync(stream);
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using (kasvoldb db1 = new kasvoldb())
                        //{
                        //    db1.medias.Add(media1);
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await after.CopyToAsync(stream);
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(stream);
using (var context = new kasvoldb())
                        //{
                        //    var useredit = (from d in context.medias
                        //                    where d.guid == page.guid && d.lang == "2"
                        //                    select d).Single();
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                        // { image.SaveAsWebp(stream, new WebpEncoder { Quality = 80 }); }
                        //   .Resize(size)
                        //           .Format(new WebPFormat())
                        //           .Quality(80)
                        //           .Save(webPFileStream1);
                    }

                    string yy = t.getimage(page.guid);
                    if (yy != null)
                    {
                        var mediaRecord = await _db.medias
                            .FirstOrDefaultAsync(d => d.guid == page.guid && d.lang == "after");
                            
                        if (mediaRecord != null)
                        {
                            mediaRecord.name = "kasvolfactory/vi/product/" + id + "/after/" + webPFileName;
                            mediaRecord.filename = webPFileName;
                            await _db.SaveChangesAsync();
                        }
                        //using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await before.CopyToAsync(stream);
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using (kasvoldb db1 = new kasvoldb())
                        //{
                        //    db1.medias.Add(media1);
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await after.CopyToAsync(stream);
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(stream);
using (var context = new kasvoldb())
                        //{
                        //    var useredit = (from d in context.medias
                        //                    where d.guid == page.guid && d.lang == "2"
                        //                    select d).Single();
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

//    useredit.name = "kasvolfactory/vi/product/" + id + "/" + webPFileName1;
                        //    useredit.filename = webPFileName1;
                        //    context.SaveChanges();
                        //}

                    }
                    else
                    {
                        media media = new media()
                        {
                            arrange = 1,
                            lang = "after",
                            guid = page.guid,
                            type = "product",
                            name = "kasvolfactory/vi/product/" + id + "/after/" + webPFileName,
                            filename = webPFileName
                        };
                        
                        _db.medias.Add(media);
                        await _db.SaveChangesAsync();
                        //media media1 = new media();

                        //media1.arrange = 2;
                        //media1.lang = "2";
                        //media1.guid = page.guid;

                        //media1.type = "product";
                        //media1.name = "kasvolfactory/vi/product/" + id + "/" + webPFileName1;
                        //media1.filename = webPFileName1;
                        //using Microsoft.EntityFrameworkCore;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await before.CopyToAsync(stream);
using Microsoft.Extensions.Configuration;
using SixLabors.ImageSharp;
using (kasvoldb db1 = new kasvoldb())
                        //{
                        //    db1.medias.Add(media1);
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using kasvol.service;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await after.CopyToAsync(stream);
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SixLabors.ImageSharp.Formats.Webp;
using (var stream = new FileStream(normalImagePath, FileMode.Create))
                    {
                        await ImageFile.CopyToAsync(stream);
using (var context = new kasvoldb())
                        //{
                        //    var useredit = (from d in context.medias
                        //                    where d.guid == page.guid && d.lang == "2"
                        //                    select d).Single();
using System;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

//    db1.SaveChanges();
                        //}
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine("Overflow. {0}", e.Message);
                }
            }
            return RedirectToAction("Index");
        }
        // GET: products/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            Product product = await _db.products.FindAsync(id);
            if (product == null)
            {
                return NotFound();
            }
            
            return View(product);
        }

        // GET: products/Create
        public async Task<IActionResult> Create()
        {
            ViewBag.product_Categories = await _db.categories.ToListAsync();
            return View();
        }

        // POST: products/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Product products)
        {
            var errors = ModelState.Select(x => x.Value.Errors)
                          .Where(y => y.Count > 0)
                          .ToList();
            
            products.IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            products.user = _userManager.GetUserId(User);
            products.slogan = _kasvolServices.FriendlyURLTitle(products.slogan);
            if (ModelState.IsValid)
            {
                await _db.products.AddAsync(products);
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            ViewBag.product_Categories = await _db.categories.ToListAsync();
            return View(products);
        }

        // GET: products/Edit/5
        public async Task<IActionResult> Edit(string id, string lang)
        {
            if (lang != null)
            {
                ViewBag.lang = lang;
            }
            if (id == null)
            {
                return BadRequest();
            }
            bool here = await _db.products.AnyAsync(a => a.guid == id && a.lang == lang);
            int idd = 0;
            if (here)
            {
                idd = await _db.products.Where(a => a.guid == id && a.lang == lang).Select(a => a.ProductID).FirstOrDefaultAsync();
            }
            else
            {
                idd = await _db.products.Where(a => a.guid == id && a.lang == "ar").Select(a => a.ProductID).FirstOrDefaultAsync();
            }
            Product Product = await _db.products.FindAsync(idd);
            if (Product == null)
            {
                return NotFound();
            }
            ViewBag.product_Categories = await _db.categories.ToListAsync();
            return View(Product);
        }

        // POST: products/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(Product products)
        {
            products.slogan = _kasvolServices.FriendlyURLTitle(products.slogan);
            bool here = await _db.products.AnyAsync(a => a.guid == products.guid && a.lang == products.lang);
            products.modifiedIP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();
            if (products.lang == "tr" || products.lang == "en")
            {
                products.ispublish = true;
                products.auther = "kasvol";
            }
            
            if (ModelState.IsValid)
            {
                try
                {
                    if (here)
                    {
                        var users = await _db.products
                            .FirstOrDefaultAsync(d => d.guid == products.guid && d.lang == products.lang);

                        if (users != null)
                        {
                            users.ProductName = products.ProductName;
                            users.ogtitle = products.ogtitle;
                            users.ogdescription = products.ogdescription;
                            users.keywords = products.keywords;
                            users.Description = products.Description;
                            users.shortDescription = products.shortDescription;
                            users.Productnumber = products.Productnumber;
                            users.weight = products.weight;
                            users.height = products.height;
                            users.width = products.width;
                            users.length = products.length;
                            users.countofcontainer = products.countofcontainer;
                            users.ispublish = products.ispublish;
                            users.auther = products.auther;
                            
                            users.datemodified = DateTime.Now;
                            users.modifiedIP = products.modifiedIP;
                            
                            _db.Update(users);
                            await _db.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        products.slogan = await _db.products
                            .Where(a => a.guid == products.guid && a.lang == "ar")
                            .Select(a => a.slogan)
                            .FirstOrDefaultAsync();
                            
                        products.auther = await _db.products
                            .Where(a => a.guid == products.guid && a.lang == "ar")
                            .Select(a => a.auther)
                            .FirstOrDefaultAsync();
                   
                        products.CategoryID = await _db.products
                            .Where(a => a.guid == products.guid && a.lang == "ar")
                            .Select(a => a.CategoryID)
                            .FirstOrDefaultAsync();
                   
                        products.ispublish = await _db.products
                            .Where(a => a.guid == products.guid && a.lang == "ar")
                            .Select(a => a.ispublish)
                            .FirstOrDefaultAsync();
                            
                        products.IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();
                        products.user = _userManager.GetUserId(User);
                        
                        await _db.products.AddAsync(products);
                        await _db.SaveChangesAsync();
                    }
                    return RedirectToAction("Index");
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!await _db.products.AnyAsync(e => e.ProductID == products.ProductID))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }
            
            ViewBag.product_Categories = await _db.categories.ToListAsync();
            return View(products);
        }

        // GET: products/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            Product product = await _db.products.FindAsync(id);
            if (product == null)
            {
                return NotFound();
            }
            
            return View(product);
        }

        // POST: products/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var product = await _db.products.FindAsync(id);
            if (product == null)
            {
                return NotFound();
            }
            
            _db.products.Remove(product);
            await _db.SaveChangesAsync();
            return RedirectToAction("Index");
        }


        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
// Remove extra closing brace here


