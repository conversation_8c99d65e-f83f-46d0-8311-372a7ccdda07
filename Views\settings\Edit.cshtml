@model kasvol.Models.Settings

@{
    ViewBag.Title = @Resources.Resource.String185;
    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}

<div id="progress" class="modal1">
    <div class="throbber">
        <div class="curtain">
        </div>
        <div class="curtain-content">
            <div>

                <div id="preloader">
                    <div id="loader"></div>
                </div>
            </div>
        </div>
    </div>
</div><div class="card card-custom">
    <div class="card-header flex-wrap py-5">
        <div class="card-title">
            <h3 class="card-label">
                @Resources.Resource.String185

            </h3>
        </div>


    </div>
    <div class="card-body" style="margin-bottom:125px">
        <h2 class="m--font-danger m--align-center"></h2>
        <div class="row">
            <div class="col-md-12">
                <div class="col-md-12">

                    @using (Ajax.BeginForm("Edit", "settings",
                                                   new AjaxOptions
                                                   {
                                                       OnSuccess = "OnSuccess",
                                                       OnFailure = "OnFailure",
                                                       OnBegin = "onLoginBegin",
                                                       LoadingElementId = "progress"
                                                   }))
                    {@Html.AntiForgeryToken()


                    <div class="form-horizontal">
                        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                        @Html.HiddenFor(model => model.settingsID)

                        <div class="form-group">
                            @Html.LabelFor(model => model.key, htmlAttributes: new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.key, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.key, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(model => model.value, htmlAttributes: new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.value, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.value, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group d-none">
                            @Html.LabelFor(model => model.arrange, htmlAttributes: new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.arrange, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.arrange, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group m-form__group row m-form__group row d-none">
                            @Html.LabelFor(model => model.datecreated, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.datecreated, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.datecreated, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group m-form__group row m-form__group row d-none">
                            @Html.LabelFor(model => model.datemodified, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.datemodified, new { htmlAttributes = new { @class = "form-control", @Value = System.DateTime.Now.ToString("yyyy-MM-dd hh:mm") } })
                                @Html.ValidationMessageFor(model => model.datemodified, "", new { @class = "text-danger" })
                            </div>
                        </div>


                        <div class="form-group m-form__group row m-form__group row d-none">
                            @Html.LabelFor(model => model.IP, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.IP, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.IP, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group m-form__group row m-form__group row d-none">
                            @Html.LabelFor(model => model.modifiedIP, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.modifiedIP, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.modifiedIP, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group m-form__group row m-form__group row d-none">
                            @Html.LabelFor(model => model.user, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.user, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.user, "", new { @class = "text-danger" })
                            </div>
                        </div>



                        <div class="form-group m-form__group row m-form__group row d-none">
                            @Html.LabelFor(model => model.guid, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.guid, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.guid, "", new { @class = "text-danger" })
                            </div>
                        </div>



                        <div class="form-group m-form__group row m-form__group">
                            <div class=" col-md-12" style="text-align:center">
                                <input type="submit" value="@Resources.Resource.String9" class="btn btn-info" />
                            </div>
                        </div>
                    </div>
                }



                    <div class=" col-md-12" style="text-align:center">
                        @Html.ActionLink("Back to List", "Index")
                    </div>
                </div>
            </div>
        </div>
    </div>

    <link rel="stylesheet" type="text/css" href="~/smart/css/smart-forms.css">
    <link rel="stylesheet" type="text/css" href="~/smart/css/smart-addons.css">


    @section Scripts {

        <script type="text/javascript">
        function OnSuccess() {
            location.href = '/settings/Index';
        }
        function OnFailure() {
            alert("Programmer will know this error ");
            $('#progress').hide();
        }

        </script>
        <script type="text/javascript">

        jQuery(document).ready(function ($) {

            $('#form0').validate();


        });

        </script>
        <script type="text/javascript">
        $("#form0").on("submit", function (event) {



            if ($('#form0').valid()) {
                $('#progress').show();
            }
        });

        </script>

        <script type="text/javascript" src="~/smart/js/jquery.validate.min.js"></script>
        <script type="text/javascript" src="~/smart/js/additional-methods.min.js"></script>

    }
