@model IEnumerable<kasvol.Models.Settings>

@{
    ViewBag.Title = Resources.Resource.String185;
    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}

<div class="card card-custom">
    <div class="card-header flex-wrap py-5">
        <div class="card-title">
            <h3 class="card-label">
               @Resources.Resource.String185

            </h3>
        </div>
        <div class="card-toolbar">
       
        </div>

    </div>
    <div class="card-body" style="margin-bottom:125px">



        <table class="table table-separate table-head-custom table-checkable dataTable no-footer dtr-inline" id="example">
            <thead>
                <tr>
                    <th>
                        ID
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.key)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.value)
                    </th>
                    
                    
                    <th>
                        @Html.DisplayNameFor(model => model.datemodified)
                    </th>
                   
                    <th></th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model)
                {
                <tr>
                    <td>
                        @Html.DisplayFor(modelItem => item.settingsID)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.key)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.value)
                    </td>
                    
                   
                    <td>
                        @item.datemodified.ToString("yyyy-MM-dd HH:mm")
                    </td>
                    
                    <td>
                        @Html.ActionLink("Edit", "Edit", new { id = item.settingsID }) 
                     
                    </td>
                </tr>

                }
            </tbody>
        </table>
    </div>

</div>
<link href="/assets/plugins/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
<link href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css" rel="stylesheet" type="text/css" />
@section Scripts {


    <script src="//cdn.datatables.net/1.10.22/js/jquery.dataTables.min.js" type="text/javascript"></script>
    <script>
        $(document).ready(function () {

            $('#example').dataTable({
                "scrollX": true, "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.20/i18n/@ViewBag.ar"}

            });
        });

    </script>
}
