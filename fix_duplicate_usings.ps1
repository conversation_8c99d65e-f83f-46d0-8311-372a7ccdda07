# PowerShell script to fix duplicate using directives in controller files

$controllersDir = "c:\Users\<USER>\Desktop\new kasvol - Copy (2)\kasvol\kasvol\Controllers"
$coreControllersDir = "c:\Users\<USER>\Desktop\new kasvol - Copy (2)\kasvol\kasvol\Controllers\Core"

# Function to remove duplicate using directives
function Remove-DuplicateUsings {
    param (
        [string]$filePath
    )

    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw
        
        # Get all using directives
        $usingMatches = [regex]::Matches($content, "using [^;]+;")
        $usings = @{}
        
        # Create a hashtable of unique using directives
        foreach ($match in $usingMatches) {
            $usingDirective = $match.Value
            if (-not $usings.ContainsKey($usingDirective)) {
                $usings[$usingDirective] = $true
            }
        }
        
        # Replace all using directives with unique ones
        $uniqueUsings = $usings.Keys -join "`n"
        $newContent = [regex]::Replace($content, "(using [^;]+;\s*)+", "$uniqueUsings`n`n")
        
        # Write the updated content back to the file
        Set-Content -Path $filePath -Value $newContent
        
        Write-Host "Fixed duplicate usings in $filePath"
    }
}

# Process all controller files in the main Controllers directory
Get-ChildItem -Path $controllersDir -Filter "*.cs" | ForEach-Object {
    Remove-DuplicateUsings -filePath $_.FullName
}

# Process all controller files in the Core subdirectory if it exists
if (Test-Path $coreControllersDir) {
    Get-ChildItem -Path $coreControllersDir -Filter "*.cs" | ForEach-Object {
        Remove-DuplicateUsings -filePath $_.FullName
    }
}

Write-Host "Completed fixing duplicate using directives in all controller files."