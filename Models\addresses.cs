using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace kasvol.Models
{
    public class Addresstable : Baseclass
    {[Key]
        public int addressid { get; set; }
        public string countryid { get; set; }
        public string city { get; set; }
        public string address { get; set; }
        public string userid { get; set; }
        public string addressname { get; set; }
      

        public Boolean isprimary { get; set; } = false;
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string roleid { get; set; }
        public bool deleted { get; set; } = false;
        public bool isshipping { get; set; } = false;

    }
}