using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using System.Collections.Generic;
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

namespace kasvol.Controllers
{
    [Authorize]
    public class videosController : BaseController
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly KasvolDbContext _db;

        public videosController(KasvolDbContext db, IConfiguration configuration, kasvolservices kasvolServices, ILogger<BaseController> logger, IWebHostEnvironment webHostEnvironment)
            : base(db, configuration, kasvolServices, logger)
        {
            _db = db;
            _webHostEnvironment = webHostEnvironment;
        }

        // GET: videos
        public ActionResult Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            return View(_db.Videos.Where(a => a.lang == "ar").ToList());
        }
        public async Task<ActionResult> uploadv(int? id)
        {
            kasvolservices t = new kasvolservices(); if (id == null)
            {
                return BadRequest();
            }
            videos page = await _db.Videos.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            return View(page);
        }
        [HttpPost]
        public async Task<ActionResult> uploadv(int? videosid, IFormFile ImageFile)
        {
            kasvolservices t = new kasvolservices();
            videos page = await _db.Videos.FindAsync(videosid);

            string path = Path.Combine(_webHostEnvironment.WebRootPath, "kasvolfactory", "vi", "video", page.videosid.ToString());


            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }


            if (ImageFile != null)
            {
               
                try
                {
                    string normalImagePath = System.IO.Path.Combine(path, ImageFile.FileName);
                    string webPFileName = System.IO.Path.GetFileNameWithoutExtension(ImageFile.FileName) + ".webp";
                  
                    string webPImagePath = System.IO.Path.Combine(path, webPFileName);
             
                    // using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using System.Collections.Generic;
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                    string ext = System.IO.Path.GetExtension(ImageFile.FileName);
                    if (ext != ".webp")
                    {
                        var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                        
                        using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using System.Collections.Generic;
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                      
                    }

                   
                        var usereditAr = (from d in _db.Videos
                                        where d.guid == page.guid && d.lang == "ar"
                                        select d).Single();
                        usereditAr.coverimagelink = "kasvolfactory/vi/video/" + page.videosid + "/" + webPFileName;
                        
                        var usereditTr = (from d in _db.Videos
                                        where d.guid == page.guid && d.lang == "tr"
                                        select d).Single();
                        usereditTr.coverimagelink = "kasvolfactory/vi/video/" + page.videosid + "/" + webPFileName;
                        
                        var usereditEn = (from d in _db.Videos
                                        where d.guid == page.guid && d.lang == "en"
                                        select d).Single();
                        usereditEn.coverimagelink = "kasvolfactory/vi/video/" + page.videosid + "/" + webPFileName;
                        
                        _db.SaveChanges();


                }
                catch (Exception e)
                {
                    Console.WriteLine("Overflow. {0}", e.Message);
                }
            }




            return RedirectToAction("Index");
        }

        // GET: videos/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            videos videos = _db.Videos.Find(id);
            if (videos == null)
            {
                return NotFound();
            }
            return View(videos);
        }

        // GET: videos/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: videos/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(string videoar, string videoen, string videotr, string titlear, string titleen, string titletr)
        {
            kasvolservices t = new kasvolservices();

            string guid = Guid.NewGuid().ToString();

            videos media = new videos();

            media.arrange = 1;
            media.lang = "ar";
            media.guid = guid;
            media.title = titlear;

            media.youtube = videoar;
            _db.Videos.Add(media);
            _db.SaveChanges();


            videos media1 = new videos();

            media1.arrange = 2;
            media1.lang = "en";
            media1.guid = guid;
            media1.title = titleen;

            media1.youtube = videoen;
            _db.Videos.Add(media1);
            _db.SaveChanges();



            videos media2 = new videos();

            media2.arrange = 3;
            media2.lang = "tr";
            media2.guid = guid;
            media2.title = titletr;

            media2.youtube = videotr;
            _db.Videos.Add(media2);
            _db.SaveChanges();




            return RedirectToAction("Index");

        }

        // GET: videos/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
          
                videos videos = db.videos.Find(id);
            if (videos == null)
            {
                return NotFound();
            }
            return View(videos);
        }

        // POST: videos/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(int videosid, string videoar, string videoen, string videotr, string titlear, string titleen, string titletr)
        {
            string guid = db.videos.Where(a => a.videosid == videosid).Select(a => a.guid).Single();
            kasvolservices t = new kasvolservices();

            if (videoar != "")
            {
                using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using System.Collections.Generic;
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

useredit.youtube = videoar;
                    useredit.title = titlear;
                    context.SaveChanges();
                }

            }
             if (videoen != "")
            {


                using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using System.Collections.Generic;
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

useredit.youtube = videoen;
                    useredit.title = titleen;
                    context.SaveChanges();
                }

            }
            if (videotr != "")
            {
                using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using System.Collections.Generic;
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.videos
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

useredit.youtube = videotr;
                    useredit.title = titletr;
                    context.SaveChanges();
                }

            }



            return RedirectToAction("Index");
        }

        // GET: videos/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            videos videos = db.videos.Find(id);
            if (videos == null)
            {
                return NotFound();
            }
            return View(videos);
        }

        // POST: videos/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            videos videos = db.videos.Find(id);
            db.videos.Remove(videos);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}






