using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using kasvol.service;

namespace kasvol.Controllers.Core
{
    [Authorize]
    public class blocksController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public blocksController(KasvolDbContext db, IConfiguration configuration, kasvolservices kasvolServices,
            UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor)
            : base(db, configuration, kasvolServices)
        {
            _db = db;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
        }

        // GET: blocks
        public async Task<IActionResult> Index()
        {
            // Get language from cookie
            string currentLanguage = Request.Cookies["CurrentLanguage"];
            if (!string.IsNullOrEmpty(currentLanguage))
            {
                ViewBag.ar = currentLanguage switch
                {
                    "ar-AE" => "Arabic.json",
                    "en-En" => "English.json",
                    "tr-tr" => "Turkish.json",
                    _ => "English.json"
                };
            }
            
            return View(await _db.Blocks.ToListAsync());
        }

        // GET: blocks/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            var blocks = await _db.Blocks.FindAsync(id);
            if (blocks == null)
            {
                return NotFound();
            }
            
            return View(blocks);
        }

        // GET: blocks/Create
        public async Task<IActionResult> Create()
        {
            ViewBag.pagesid = await _db.Pages.ToListAsync();
            return View();
        }

        // POST: blocks/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(blocks blocks)
        {
            blocks.IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            blocks.user = _userManager.GetUserId(User);
            blocks.title = _kasvolServices.setvalue(blocks.lang, blocks.title);
            
            // Process keywords
            if (!string.IsNullOrEmpty(blocks.keywords))
            {
                string kk = blocks.keywords.Replace("[{\"value\":\"", "");
                kk = kk.Replace("\"}]", "");
                kk = kk.Replace("}", "");
                kk = kk.Replace("{", "");
                kk = kk.Replace("\"value\":\"", "");
                kk = kk.Replace("\"", "");
                kk = kk.Replace(",", "@");
                blocks.keywords = _kasvolServices.setvalue(blocks.lang, kk);
            }
            
            blocks.ogtitle = _kasvolServices.setvalue(blocks.lang, blocks.ogtitle);
            blocks.ogdescription = _kasvolServices.setvalue(blocks.lang, blocks.ogdescription);

            if (ModelState.IsValid)
            {
                await _db.Blocks.AddAsync(blocks);
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            
            ViewBag.pagesid = await _db.Pages.ToListAsync();
            return View(blocks);
        }

        // GET: blocks/Edit/5
        public async Task<IActionResult> Edit(string id, string lang)
        {
            if (!string.IsNullOrEmpty(lang))
            {
                ViewBag.lang = lang;
            }
            
            if (string.IsNullOrEmpty(id))
            {
                return BadRequest();
            }
            
            int blockId;
            if (!int.TryParse(id, out blockId))
            {
                return BadRequest();
            }
            
            var blocks = await _db.Blocks.FindAsync(blockId);
            if (blocks == null)
            {
                return NotFound();
            }
            
            ViewBag.pagesid = await _db.Pages.ToListAsync();
            return View(blocks);
        }

        // POST: blocks/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(blocks blocks)
        {
            blocks.modifiedIP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            blocks.datemodified = DateTime.Now;
            
            // Process keywords if not null
            if (!string.IsNullOrEmpty(blocks.keywords))
            {
                string kk = blocks.keywords.Replace("[{\"value\":\"", "");
                kk = kk.Replace("\"}]", "");
                kk = kk.Replace("}", "");
                kk = kk.Replace("{", "");
                kk = kk.Replace("\"value\":\"", "");
                kk = kk.Replace("\"", "");
                kk = kk.Replace(",", "@");
                blocks.keywords = _kasvolServices.setvalue(blocks.lang, kk);
            }
            
            blocks.ogtitle = _kasvolServices.setvalue(blocks.lang, blocks.ogtitle);
            blocks.ogdescription = _kasvolServices.setvalue(blocks.lang, blocks.ogdescription);
            
            if (ModelState.IsValid)
            {
                _db.Entry(blocks).State = EntityState.Modified;
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            
            ViewBag.pagesid = await _db.Pages.ToListAsync();
            return View(blocks);
        }

        // GET: blocks/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            var blocks = await _db.Blocks.FindAsync(id);
            if (blocks == null)
            {
                return NotFound();
            }
            
            return View(blocks);
        }

        // POST: blocks/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var blocks = await _db.Blocks.FindAsync(id);
            if (blocks != null)
            {
                _db.Blocks.Remove(blocks);
                await _db.SaveChangesAsync();
            }
            
            return RedirectToAction("Index");
        }
    }
}
