using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authorization;
using System;
using kasvol.service;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class project_categoriesController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;

        public project_categoriesController(KasvolDbContext db, IConfiguration configuration, kasvolservices kasvolServices, IHttpContextAccessor httpContextAccessor, UserManager<ApplicationUser> userManager)
            : base(db, configuration, kasvolServices, httpContextAccessor)
        {
            _userManager = userManager;
        }

        // GET: project_categories
        public ActionResult Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            return View(_db.ProjectCategories.ToList());
        }

        // GET: project_categories/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            project_categories project_categories = _db.ProjectCategories.Find(id);
            if (project_categories == null)
            {
                return NotFound();
            }
            return View(project_categories);
        }

        // GET: project_categories/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: project_categories/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create( project_categories project_categories)
        {
            var errors = ModelState.Select(x => x.Value.Errors)
                            .Where(y => y.Count > 0)
                            .ToList();
            kasvolservices t = new kasvolservices();
            project_categories.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            project_categories.user = _userManager.GetUserId(User);
            project_categories.title = t.setvalue(project_categories.lang, project_categories.title);
            if (project_categories.keywords != null)
            {
                string kk = project_categories.keywords.Replace("[{\"value\":\"", "");
                kk = kk.Replace("\"}]", "");
                kk = kk.Replace("}", "");
                kk = kk.Replace("{", "");
                kk = kk.Replace("\"value\":\"", "");
                kk = kk.Replace("\"", "");
                kk = kk.Replace(",", "@");
                project_categories.keywords = t.setvalue(project_categories.lang, kk);
            }
            project_categories.ogtitle = t.setvalue(project_categories.lang, project_categories.ogtitle);
            project_categories.ogdescription = t.setvalue(project_categories.lang, project_categories.ogdescription);
       

            if (ModelState.IsValid)
            {
                _db.ProjectCategories.Add(project_categories);
                _db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(project_categories);
        }

        // GET: project_categories/Edit/5
        public ActionResult Edit(int? id, string lang)
        {
            if (lang != null)
            {
                ViewBag.lang = lang;
            }
            if (id == null)
            {
                return BadRequest();
            }
            project_categories project_categories = _db.ProjectCategories.Find(id);
            if (project_categories == null)
            {
                return NotFound();
            }
            return View(project_categories);
        }

        // POST: project_categories/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit( project_categories project_categories)
        {
            kasvolservices t = new kasvolservices();
            project_categories.title = t.updatevalue(project_categories.lang, project_categories.title, project_categories.oldvalue);
            project_categories.ogtitle = t.updatevalue(project_categories.lang, project_categories.ogtitle, project_categories.oldvalue2);
            project_categories.ogdescription = t.updatevalue(project_categories.lang, project_categories.ogdescription, project_categories.oldvalue3);
            project_categories.keywords = t.updatevalue(project_categories.lang, project_categories.keywords, project_categories.oldvalue4);
            project_categories.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();

            if (ModelState.IsValid)
            {
                var users = (from d in _db.ProjectCategories
                             where d.project_categoriesid == project_categories.project_categoriesid
                             select d).Single();

                    users.title = project_categories.title;
                    users.ogtitle = project_categories.ogtitle;
                    users.ogdescription = project_categories.ogdescription;
                    users.keywords = project_categories.keywords;
                    users.datemodified = DateTime.Now;
                    users.modifiedIP = project_categories.modifiedIP;
                _db.SaveChanges();
                return RedirectToAction("Index");
            }
            return View(project_categories);
        }

        // GET: project_categories/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            project_categories project_categories = _db.ProjectCategories.Find(id);
            if (project_categories == null)
            {
                return NotFound();
            }
            return View(project_categories);
        }

        // POST: project_categories/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            project_categories project_categories = _db.ProjectCategories.Find(id);
            _db.ProjectCategories.Remove(project_categories);
            _db.SaveChanges();
            return RedirectToAction("Index");
        }


    }
}


