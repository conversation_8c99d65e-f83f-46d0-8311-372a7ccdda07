@model kasvol.Models.Order

@{
    ViewBag.Title = @Resources.Resource.String153;
    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}

@using kasvol.service;
@{
    kasvolservices t = new kasvolservices();





}

<div id="progress" class="modal1">
    <div class="throbber">
        <div class="curtain">
        </div>
        <div class="curtain-content">
            <div>

                <div id="preloader">
                    <div id="loader"></div>
                </div>
            </div>
        </div>
    </div>
</div><div class="card card-custom">
    <div class="card-header flex-wrap py-5">
        <div class="card-title">
            <h3 class="card-label">
                @Resources.Resource.String153

            </h3>
        </div>


    </div>
    <div class="card-body" style="margin-bottom:125px">
        <h2 class="m--font-danger m--align-center"></h2>
        <div class="row">
            <div class="col-md-12">

                @using (Ajax.BeginForm("Create", "Orders",
                                               new AjaxOptions
                                               {
                                                   OnSuccess = "OnSuccess",
                                                   OnFailure = "OnFailure",
                                                   OnBegin = "onLoginBegin",
                                                   LoadingElementId = "progress"
                                               }))
                {@Html.AntiForgeryToken()

                <div class="form-horizontal">
                    @Html.ValidationSummary(true, "", new { @class = "text-danger" })



                    <div class="form-group m-form__group row">
                        <label class="col-md-1 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.name </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.FirstName, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.FirstName, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row">
                        <label class="col-md-1 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.surname </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.LastName, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.LastName, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row">
                        <label class="col-md-1 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String155 </label>
                        <div class="col-sm-12" style="text-align:start">
                            @Html.EditorFor(model => model.CompanyName, new { htmlAttributes = new { @class = "form-control", required = "required" } })
                            @Html.ValidationMessageFor(model => model.CompanyName, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row">
                        <label class="col-md-1 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.tel </label>
                        <div class="col-sm-12" style="text-align:start">
                            @Html.EditorFor(model => model.Phone, new { htmlAttributes = new { @class = "form-control", required = "required" } })
                            @Html.ValidationMessageFor(model => model.Phone, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row">
                        <label class="col-md-1 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.email </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.Email, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.Email, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group m-form__group row">


                        <div class="col-sm-4" style="text-align:start">
                            <label class=" col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String186 </label>
                            <select class="form-control" name="countryid" required>

                                <option selected value="">@Resources.Resource.String15</option>


                                @foreach (var item in ViewBag.countryid)
                                {



                                    string value2 = t.getvalue(ViewBag.lang, item.countryname);

                                    @*if (item.countryid == 1)
                                    {
                                        <option selected value="@item.countryid">@value2</option>
                                    }
                                    else
                                    {*@

                                        <option value="@item.countryid">@value2</option>
                                    //}
                                }
                            </select>
                        </div>
                        <div class="col-sm-4" style="text-align:start">
                            <label class=" col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String187 </label>
                            <select class="form-control" name="regionid" required>

                                <option selected value="">@Resources.Resource.String15</option>

                                @foreach (var item in ViewBag.regionid)
                                {



                                    string value2 = t.getvalue(ViewBag.lang, item.regionname);



                                    <option value="@item.regionid">@value2</option>

                                }
                            </select>
                        </div>
                        <div class="col-sm-4" style="text-align:start">
                            <label class=" col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String188 </label>
                            <select class="form-control" name="cityid" required>

                                <option selected value="">@Resources.Resource.String15</option>


                                @foreach (var item in ViewBag.cityid)
                                {



                                    string value2 = t.getvalue(ViewBag.lang, item.cityname);

                                    if (item.cityid == 1)
                                    {
                                        <option value="@item.cityid">@value2</option>
                                    }
                                    else
                                    {

                                        <option value="@item.cityid">@value2</option>
                                    }
                                }
                            </select>
                        </div>
                    </div>

                    <div class="form-group m-form__group row">
                        <label class="col-md-1 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.address </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.address, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.address, "", new { @class = "text-danger" })
                        </div>
                    </div>


                    <div class="form-group m-form__group row">


                        <div class="col-sm-12" style="text-align:start">
                            <label class=" col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String175 </label>
                            <select class="form-control" name="agentid" required>


                                <option selected value="">@Resources.Resource.String15</option>
                                @foreach (var item in ViewBag.agentid)
                                {







                                    <option value="@item.agencyid">@item.companyname (@item.fullname)</option>

                                }
                                <option value="0">@Resources.Resource.String189</option>
                            </select>
                        </div>
                    </div>


                    <div class="form-group m-form__group row d-none">
                        @Html.LabelFor(model => model.datecreated, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.datecreated, new { htmlAttributes = new { @class = "form-control", @Value = System.DateTime.Now.ToString("yyyy-MM-dd hh:mm") } })
                            @Html.ValidationMessageFor(model => model.datecreated, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group m-form__group row d-none">
                        @Html.LabelFor(model => model.datemodified, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.datemodified, new { htmlAttributes = new { @class = "form-control", @Value = System.DateTime.Now.ToString("yyyy-MM-dd hh:mm") } })
                            @Html.ValidationMessageFor(model => model.datemodified, "", new { @class = "text-danger" })
                        </div>
                    </div>










                    <div class="form-group m-form__group">
                        <div class=" col-md-12" style="text-align:center">
                            <input type="submit" value="@Resources.Resource.String2" class="btn btn-info" />
                        </div>
                    </div>
                </div>
            }



                <div class=" col-md-12" style="text-align:center">
                    @Html.ActionLink("Back to List", "Index")
                </div>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" type="text/css" href="~/smart/css/smart-forms.css">
<link rel="stylesheet" type="text/css" href="~/smart/css/smart-addons.css">


@section Scripts {
    <script type="text/javascript">
    $(document).ready(function () {
        var lang = '@ViewBag.lang'; // Fetch the language from ViewBag

        // When a country is selected, fetch the regions for that country
        $('#countryid').change(function () {
            var countryId = $(this).val();
            if (countryId !== '') {
                $.ajax({
                    url: '@Url.Action("GetRegions")',
                    type: "GET",
                    data: { countryId: countryId, lang: lang }, // Pass lang to the server
                    success: function (regions) {
                        $('#regionid').empty();
                        $('#regionid').append('<option value="">@Resources.Resource.String15</option>');
                        $.each(regions, function (index, region) {
                            $('#regionid').append('<option value="' + region.Value + '">' + region.Text + '</option>');
                        });
                    }
                });
            } else {
                $('#regionid').empty();
                $('#cityid').empty();
            }
        });

        // When a region is selected, fetch the cities for that region
        $('#regionid').change(function () {
            var regionId = $(this).val();
            if (regionId !== '') {
                $.ajax({
                    url: '@Url.Action("GetCities")',
                    type: "GET",
                    data: { regionId: regionId, lang: lang }, // Pass lang to the server
                    success: function (cities) {
                        $('#cityid').empty();
                        $('#cityid').append('<option value="">@Resources.Resource.String15</option>');
                        $.each(cities, function (index, city) {
                            $('#cityid').append('<option value="' + city.Value + '">' + city.Text + '</option>');
                        });
                    }
                });
            } else {
                $('#cityid').empty();
            }
        });
    });
    </script>

    <script type="text/javascript">
        function OnSuccess() {
            location.href = '/Orders/Index';
        }
        function OnFailure() {
            alert("Programmer will know this error ");
            $('#progress').hide();
        }

    </script>
    <script type="text/javascript">

        jQuery(document).ready(function ($) {

            $('#form0').validate();


        });

    </script>
    <script type="text/javascript">
        $("#form0").on("submit", function (event) {



            if ($('#form0').valid()) {
                $('#progress').show();
            }
        });

    </script>

    <script type="text/javascript">

        $('#companyemail').keyup(function () {

            document.getElementById("username").value = document.getElementById("companyemail").value;

        });
    </script>

    <script type="text/javascript" src="~/smart/js/jquery.validate.min.js"></script>
    <script type="text/javascript" src="~/smart/js/additional-methods.min.js"></script>
}