<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.13.2" />
    <PackageReference Include="BouncyCastle.Cryptography" Version="2.5.1" />
    <PackageReference Include="DataAnnotationsExtensions" Version="5.0.1.27" />
    <PackageReference Include="Google.Apis.Drive.v3" Version="1.67.0.3309" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.1" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.1" />
    <PackageReference Include="MySql.Data" Version="8.2.0" />
    <PackageReference Include="NetTopologySuite" Version="2.5.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.8" />
    <PackageReference Include="SixLabors.ImageSharp.Web" Version="3.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.1" />
    <PackageReference Include="System.Text.Json" Version="9.0.4" />
    <PackageReference Include="X.PagedList.Mvc.Core" Version="10.5.7" />
  </ItemGroup>

</Project>