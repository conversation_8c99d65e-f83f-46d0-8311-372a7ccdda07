using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using kasvol.service;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Globalization;
using Microsoft.AspNetCore.Http;

namespace kasvol.Controllers
{
    public abstract class BaseController : Controller
    {
        protected readonly KasvolDbContext _db;
        protected readonly IConfiguration _configuration;
        protected readonly kasvolservices _kasvolServices;
        protected readonly IHttpContextAccessor _httpContextAccessor;

        public BaseController(KasvolDbContext db, IConfiguration configuration, kasvolservices kasvolServices, IHttpContextAccessor httpContextAccessor)
        {
            _db = db;
            _configuration = configuration;
            _kasvolServices = kasvolServices;
            _httpContextAccessor = httpContextAccessor;
        }


        public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            // Set notifications for orders
            ViewData["notif"] = await _db.Orders.CountAsync(a => a.isneedapproved && !a.isapproved);
            ViewData["notif1"] = await _db.Orders.CountAsync(a => !a.isneedapproved && a.isapproved && !a.issold);

            DateTime oneWeekAgo = DateTime.Now.AddDays(-7);

            // Get orders from last week that exceed max discount
            var recentOrders = await _db.Orders
                .Where(a => a.datecreated >= oneWeekAgo)
                .ToListAsync();

            var ordersExceedingDiscount = new List<Order>();
            int maxDiscount = _kasvolServices.maxdis();

            foreach (var order in recentOrders)
            {
                var orderDetails = await _db.OrderDetails
                    .Where(a => a.ordercode == order.kasvolcode)
                    .ToListAsync();

                foreach (var detail in orderDetails)
                {
                    detail.newUnitPrice ??= detail.UnitPrice;
                    decimal discountPercentage = (decimal)(((detail.UnitPrice - detail.newUnitPrice) / detail.UnitPrice) * 100);

                    if (discountPercentage > maxDiscount)
                    {
                        ordersExceedingDiscount.Add(order);
                        break;
                    }
                }
            }

            ViewData["notif2"] = ordersExceedingDiscount.Count;
            // Handle language settings based on URL and preferences
            var request = _httpContextAccessor.HttpContext.Request;
            var currentLanguage = "ar-AE";
            var shortLang = "ar";

            // Check URL path for language
            var path = request.Path.Value?.ToLower();
            if (path != null)
            {
                if (path.Contains("/en/") || path == "/en")
                {
                    currentLanguage = "en-En";
                    shortLang = "en";
                }
                else if (path.Contains("/tr/") || path == "/tr")
                {
                    currentLanguage = "tr-tr";
                    shortLang = "tr";
                }
            }

            // Check query string override
            var langQuery = request.Query["lang"].ToString();
            if (!string.IsNullOrEmpty(langQuery))
            {
                switch (langQuery.ToLower())
                {
                    case "en":
                        currentLanguage = "en-En";
                        shortLang = "en";
                        break;
                    case "tr":
                        currentLanguage = "tr-tr";
                        shortLang = "tr";
                        break;
                    case "ar":
                        currentLanguage = "ar-AE";
                        shortLang = "ar";
                        break;
                }
            }

            // Set culture info
            var cultureInfo = new CultureInfo(currentLanguage);
            CultureInfo.CurrentCulture = cultureInfo;
            CultureInfo.CurrentUICulture = cultureInfo;

            // Update cookie and session
            _httpContextAccessor.HttpContext.Response.Cookies.Append("CurrentLanguage", currentLanguage);
            _httpContextAccessor.HttpContext.Session.SetString("CurrentLanguage", currentLanguage);

            // Set ViewData for views
            ViewData["lang"] = shortLang;
            // Set OG URL for sharing
            string url = $"{request.Scheme}://{request.Host}{request.PathBase}{request.Path}{request.QueryString}";
            var domain = GetDomainPart(url);
            ViewData["OGUrl"] = $"https://{domain}{request.Path}{request.QueryString}";

            await next();
        }

        [HttpPost]
        public async Task<IActionResult> ChangeLanguageToArabic()
        {
            var referer = _httpContextAccessor.HttpContext.Request.Headers["Referer"].ToString();
            if (string.IsNullOrEmpty(referer)) return await Task.FromResult(Json(new { success = false }));

            var uri = new Uri(referer);
            var domain = GetDomainPart(referer);
            var result = referer;

            if (uri.AbsolutePath.Contains("/en/"))
            {
                result = referer.Replace("/en/", "/");
            }
            else if (uri.AbsolutePath.Contains("/tr/"))
            {
                result = referer.Replace("/tr/", "/");
            }

            _httpContextAccessor.HttpContext.Response.Cookies.Append("CurrentLanguage", "ar-AE");
            _httpContextAccessor.HttpContext.Session.SetString("CurrentLanguage", "ar-AE");

            return await Task.FromResult(Json(new { statusCode = 200, url = result }));
        }

        [HttpPost]
        public async Task<IActionResult> ChangeLanguageToEnglish()
        {
            var referer = _httpContextAccessor.HttpContext.Request.Headers["Referer"].ToString();
            if (string.IsNullOrEmpty(referer)) return await Task.FromResult(Json(new { success = false }));

            var uri = new Uri(referer);
            var domain = GetDomainPart(referer);
            var result = referer;

            if (!uri.AbsolutePath.Contains("/en/"))
            {
                var basePath = uri.AbsolutePath.Replace("/tr/", "/"); // Assuming if not /en/ and we want /en/, we might be coming from /tr/ or root
                if (uri.AbsolutePath.Contains("/ar/")) // Explicitly handle /ar/ to /en/
                {
                    basePath = uri.AbsolutePath.Replace("/ar/", "/");
                }
                else if (!uri.AbsolutePath.StartsWith("/en/") && !uri.AbsolutePath.StartsWith("/tr/")) // Coming from root or other non-specified lang path
                {
                    basePath = uri.AbsolutePath;
                }
                result = $"{uri.Scheme}://{domain}/en{basePath.TrimStart('/')}{uri.Query}";
            }

            _httpContextAccessor.HttpContext.Response.Cookies.Append("CurrentLanguage", "en-En");
            _httpContextAccessor.HttpContext.Session.SetString("CurrentLanguage", "en-En");

            return await Task.FromResult(Json(new { statusCode = 200, url = result }));
        }
        [HttpPost]
        public async Task<IActionResult> ChangeLanguageToTurkish()
        {
            var referer = _httpContextAccessor.HttpContext.Request.Headers["Referer"].ToString();
            if (string.IsNullOrEmpty(referer)) return await Task.FromResult(Json(new { success = false }));

            var uri = new Uri(referer);
            var domain = GetDomainPart(referer);
            var result = referer;

            if (!uri.AbsolutePath.Contains("/tr/"))
            {
                var basePath = uri.AbsolutePath.Replace("/en/", "/"); // Assuming if not /tr/ and we want /tr/, we might be coming from /en/ or root
                if (uri.AbsolutePath.Contains("/ar/")) // Explicitly handle /ar/ to /tr/
                {
                    basePath = uri.AbsolutePath.Replace("/ar/", "/");
                }
                else if (!uri.AbsolutePath.StartsWith("/en/") && !uri.AbsolutePath.StartsWith("/tr/")) // Coming from root or other non-specified lang path
                {
                    basePath = uri.AbsolutePath;
                }
                result = $"{uri.Scheme}://{domain}/tr{basePath.TrimStart('/')}{uri.Query}";
            }

            _httpContextAccessor.HttpContext.Response.Cookies.Append("CurrentLanguage", "tr-tr");
            _httpContextAccessor.HttpContext.Session.SetString("CurrentLanguage", "tr-tr");

            return await Task.FromResult(Json(new { statusCode = 200, url = result }));
        }
        private string GetDomainPart(string url)
        {
            var uri = new Uri(url);
            var host = uri.Host;
            return host.StartsWith("www.") ? host.Substring(4) : host;
        }
    }
}
