using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;

namespace kasvol.Models
{
    // Type alias for backward compatibility
    using kasvoldb = KasvolDbContext;

    public class KasvolDbContext : IdentityDbContext<ApplicationUser>
    {
        public KasvolDbContext(DbContextOptions<KasvolDbContext> options)
            : base(options)
        {
        }
   
        public DbSet<awards>  Awards { get; set; }
        public DbSet<banners> Banners { get; set; }
        public DbSet<blocks>  Blocks { get; set; }
        public DbSet<clients>   Clients { get; set; }
        public DbSet<documents>  Documents { get; set; }
        public DbSet<faqs>  Faqs { get; set; }
        public DbSet<g360>  G360s { get; set; }
        public DbSet<galleries>  Galleries { get; set; }
        public DbSet<icons>  Icons { get; set; }
        public DbSet<Jobs>  Jobs { get; set; }
        public DbSet<Languages>  Languages { get; set; }
        public DbSet<Media> Media { get; set; }
        public DbSet<pages>  Pages { get; set; }
        public DbSet<partners>  Partners { get; set; }
        public DbSet<portfolios> Portfolios { get; set; }
        public DbSet<portfolio_categories>  PortfolioCategories { get; set; }
        public DbSet<postcat>  Postcats { get; set; }
        public DbSet<Posts>  Posts  { get; set; }
        public DbSet<agencyroles>   AgencyRoles { get; set; }
        public DbSet<project_categories>  ProjectCategories { get; set; }
        public DbSet<Projects> Projects { get; set; }
        public DbSet<Reference>  References { get; set; }
        public DbSet<service_items>  ServiceItems { get; set; }
        public DbSet<OurServices>   OurServices { get; set; }
        public DbSet<Settings>  Settings { get; set; }
        public DbSet<Sliders>  Sliders { get; set; }
        public DbSet<Country>  Countries { get; set; }
        public DbSet<region>  Regions { get; set; }
        public DbSet<city> Cities { get; set; }
        public DbSet<agency>  Agencies { get; set; }
        public DbSet<Testimonials>  Testimonials { get; set; }
        public DbSet<translations>  Translations { get; set; }
        public DbSet<employee> Employees { get; set; }
        public DbSet<Videos>  Videos { get; set; }
        public DbSet<VideosKasvol> VideosKasvols { get; set; }
        public DbSet<videocategory> VideoCategories { get; set; }
        public DbSet<Roles> Roles { get; set; }
        public DbSet<Addresstable>  AddressTables { get; set; }
        public DbSet<CartItem>  CartItems { get; set; }
        public DbSet<Category>  Categories { get; set; }
        public DbSet<Currency> Currencies { get; set; }
        public DbSet<iconblock1>  IconBlocks { get; set; }
        public DbSet<iconcontainer1>  IconContainers { get; set; }
        public DbSet<imageblock1>  ImageBlocks { get; set; }
        public DbSet<OrderDetail>  OrderDetails { get; set; }
        public DbSet<Order>  Orders { get; set; }
        public DbSet<Orderstatus>  OrderStatuses { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<Productphotos>  ProductPhotos { get; set; }
        public DbSet<textblock1>  TextBlocks { get; set; }
        public DbSet<textblockcontainer1>  TextBlockContainers { get; set; }
        public DbSet<Reviewlist>  ReviewLists { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);
            // Customize the ASP.NET Identity model and override the defaults if needed.
            // For example, you can rename the ASP.NET Identity table names and more.
            // Add your customizations after calling base.OnModelCreating(builder);
        }
    }
}