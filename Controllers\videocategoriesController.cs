using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authorization;
using System;
using kasvol.service;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class videocategoriesController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;

        public videocategoriesController(KasvolDbContext db, IConfiguration configuration, kasvolservices kasvolServices, IHttpContextAccessor httpContextAccessor, UserManager<ApplicationUser> userManager)
            : base(db, configuration, kasvolServices, httpContextAccessor)
        {
            _userManager = userManager;
        }

        // GET: videocategories
        public ActionResult Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            return View(_db.VideoCategories.ToList());
        }

        // GET: videocategories/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            videocategory videocategories = _db.VideoCategories.Find(id);
            if (videocategories == null)
            {
                return NotFound();
            }
            return View(videocategories);
        }

        // GET: videocategories/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: videocategories/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(videocategory videocategories)
        {
            var errors = ModelState.Select(x => x.Value.Errors)
                            .Where(y => y.Count > 0)
                            .ToList();
            kasvolservices t = new kasvolservices();
            videocategories.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            videocategories.user = _userManager.GetUserId(User);
            videocategories.title = t.setvalue(videocategories.lang, videocategories.title);
            string kk = videocategories.keywords.Replace("[{\"value\":\"", "");
            kk = kk.Replace("\"}]", "");
            kk = kk.Replace("}", "");
            kk = kk.Replace("{", "");
            kk = kk.Replace("\"value\":\"", "");
            kk = kk.Replace("\"", "");
            kk = kk.Replace(",", "@");
            videocategories.ogtitle = t.setvalue(videocategories.lang, videocategories.ogtitle);
            videocategories.ogdescription = t.setvalue(videocategories.lang, videocategories.ogdescription);
            videocategories.keywords = t.setvalue(videocategories.lang, kk);

            if (ModelState.IsValid)
            {
                _db.VideoCategories.Add(videocategories);
                _db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(videocategories);
        }

        // GET: videocategories/Edit/5
        public ActionResult Edit(int? id, string lang)
        {
            if (lang != null)
            {
                ViewBag.lang = lang;
            }
            if (id == null)
            {
                return BadRequest();
            }
            videocategory videocategories = _db.VideoCategories.Find(id);
            if (videocategories == null)
            {
                return NotFound();
            }
            return View(videocategories);
        }

        // POST: videocategories/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(videocategory videocategories)
        {
            kasvolservices t = new kasvolservices();
            videocategories.title = t.updatevalue(videocategories.lang, videocategories.title, videocategories.oldvalue);
            videocategories.ogtitle = t.updatevalue(videocategories.lang, videocategories.ogtitle, videocategories.oldvalue2);
            videocategories.ogdescription = t.updatevalue(videocategories.lang, videocategories.ogdescription, videocategories.oldvalue3);
            videocategories.keywords = t.updatevalue(videocategories.lang, videocategories.keywords, videocategories.oldvalue4);
            videocategories.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();

            if (ModelState.IsValid)
            {
                var users = (from d in _db.VideoCategories
                             where d.videocategoryid == videocategories.videocategoryid
                             select d).Single();

                users.title = videocategories.title;
                users.ogtitle = videocategories.ogtitle;
                users.ogdescription = videocategories.ogdescription;
                users.keywords = videocategories.keywords;
                users.datemodified = DateTime.Now;
                users.modifiedIP = videocategories.modifiedIP;
                _db.SaveChanges();
                return RedirectToAction("Index");
            }
            return View(videocategories);
        }

        // GET: videocategories/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            videocategory videocategories = _db.VideoCategories.Find(id);
            if (videocategories == null)
            {
                return NotFound();
            }
            return View(videocategories);
        }

        // POST: videocategories/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            videocategory videocategories = _db.VideoCategories.Find(id);
            _db.VideoCategories.Remove(videocategories);
            _db.SaveChanges();
            return RedirectToAction("Index");
        }


    }
}


