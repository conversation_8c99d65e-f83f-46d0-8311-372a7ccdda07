using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace kasvol.Models
{
    public class OrderDetail:Baseclass
    {
        public int OrderDetailId { get; set; }

        public int OrderId { get; set; }
       
        public string userid { get; set; }
        public string storeid { get; set; }
        public DateTime storedatemodified { get; set; } = DateTime.Now;

        public int ProductId { get; set; }
        public virtual Product  Product { get; set; }
        public int Quantity { get; set; }
        public int currencyid { get; set; }

        public double? UnitPrice { get; set; }
        public double? newUnitPrice { get; set; }
  
        public string businessyear { get; set; }
        public string ordercode { get; set; } = Guid.NewGuid().ToString().Replace("-", string.Empty).Substring(0, 7);
        public string note { get; set; }
        public double? shopping { get; set; }
        public double? dropping { get; set; }


    }
}