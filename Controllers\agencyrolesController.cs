using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using System;
using kasvol.service;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    public class agencyrolesController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly KasvolDbContext _db;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public agencyrolesController(KasvolDbContext db, UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor) : base(db, httpContextAccessor)
        {
            _db = db;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
        }

        // GET: agencyroles
        public async Task<ActionResult> Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            return View(await _db.Agencyroles.ToListAsync());
        }

        // GET: agencyroles/Details/5
        public async Task<ActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            agencyroles agencyroles = await _db.Agencyroles.FindAsync(id);
            if (agencyroles == null)
            {
                return NotFound();
            }
            return View(agencyroles);
        }

        // GET: agencyroles/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: agencyroles/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Create([Bind("agencyrolesid,agencyrolescode,agencyrolesname,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] agencyroles agencyroles)
        {
            kasvolservices t = new kasvolservices();
            agencyroles.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            agencyroles.user = _userManager.GetUserId(User);
            agencyroles.agencyrolesname = t.setvalue("ar", agencyroles.agencyrolesname);
            if (ModelState.IsValid)
            {
                _db.Agencyroles.Add(agencyroles);
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }

            return View(agencyroles);
        }

        // GET: agencyroles/Edit/5
        public async Task<ActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            agencyroles agencyroles = await _db.Agencyroles.FindAsync(id);
            if (agencyroles == null)
            {
                return NotFound();
            }
            return View(agencyroles);
        }

        // POST: agencyroles/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Edit([Bind("agencyrolesid,agencyrolescode,agencyrolesname,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] agencyroles agencyroles)
        {
            kasvolservices t = new kasvolservices();
            agencyroles.agencyrolesname = t.updatevalue(agencyroles.lang, agencyroles.agencyrolesname, agencyroles.oldvalue);

            agencyroles.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();
            if (ModelState.IsValid)
            {
                _db.Entry(agencyroles).State = EntityState.Modified;
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            return View(agencyroles);
        }

        // GET: agencyroles/Delete/5
        public async Task<ActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            agencyroles agencyroles = await _db.Agencyroles.FindAsync(id);
            if (agencyroles == null)
            {
                return NotFound();
            }
            return View(agencyroles);
        }

        // POST: agencyroles/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> DeleteConfirmed(int id)
        {
            agencyroles agencyroles = await _db.Agencyroles.FindAsync(id);
            _db.Agencyroles.Remove(agencyroles);
            await _db.SaveChangesAsync();
            return RedirectToAction("Index");
        }


    }
}


