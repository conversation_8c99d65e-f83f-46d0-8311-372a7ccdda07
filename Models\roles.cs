using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace kasvol.Models
{
    public class Roles:Baseclass
    {
        [Key]
        
        public int roleid { get; set; }
        
        public string rolename { get; set; }
  
        public string asproleid { get; set; }
        public virtual ICollection<employee>  Employees { get; set; }
     
    }
}