
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace kasvol.Models
{
    public class agency: Baseclass
    {
        [Key]
        public int agencyid { get; set; }
        public string agentid { get; set; }
        [Required]
    
        public string username { get; set; }
        [Required(ErrorMessage = "The password field is required.")]
        [MinLength(6, ErrorMessage = "The password must be at least 6 characters long.")]
        [RegularExpression(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*\W).+$",
             ErrorMessage = "The password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character.")]
     

        public string password { get; set; }
        public string deposit { get; set; }
        public string cmark { get; set; }
        public string gmark { get; set; }
        public string comark { get; set; }
        public string key { get; set; }
        public string agentlogo { get; set; } = "/assets/media/avatars/blank.png";
        [Required]
        public string companyname { get; set; }
        [Required]
        [EmailAddress(ErrorMessage = "The Email Address is not valid")]
        public string companyemail { get; set; }
        [Required]
        public string CompanyPhone { get; set; }
        public string CompanyTaxNumber{ get; set; }
        public string where { get; set; }
        public string facebook { get; set; }
        public string twitter { get; set; }
        public string linkedin { get; set; }
        public string instagram { get; set; }
        public string viber { get; set; }
        public string whatsapp { get; set; }
        public string wechat { get; set; }
        public string website { get; set; }
        public string country { get; set; }
        public string city { get; set; }
        public string address { get; set; }
        public string zipcode { get; set; }
        public bool issubagent { get; set; } = false;
        public string parentid { get; set; }
        public int MaximumApplicationLimit { get; set; } = 20;
        public bool isemplyee { get; set; } = false;
        public string fullname { get; set; }
        public bool isactive { get; set; } = true;
        public int agencyrolesid { get; set; }
        public virtual agencyroles  Agencyroles { get; set; }


    }
}