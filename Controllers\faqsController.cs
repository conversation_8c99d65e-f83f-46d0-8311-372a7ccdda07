using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authorization;
using System;
using kasvol.service;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class faqsController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly KasvolDbContext _db;

        public faqsController(UserManager<ApplicationUser> userManager, KasvolDbContext context)
        {
            _userManager = userManager;
            _db = context;
        }

        // GET: faqs
        public ActionResult Index(string idd)
        {
            if (idd != null) {
                ViewBag.id = idd;
                return View(_db.faqs.Where(a=>a.ogtitle==idd).ToList());
                
            }
            else
            {
                return View(_db.faqs.ToList());
            }
        }

        // GET: faqs/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            faqs faqs = _db.faqs.Find(id);
            if (faqs == null)
            {
                return NotFound();
            }
            return View(faqs);
        }

        // GET: faqs/Create
        public ActionResult Create(string id)
        {
            string lang = ViewBag.lang;
            ViewBag.id = id;
            ViewBag.postcatid = _db.posts.Where(a=>a.lang== lang).ToList();
            return View();
        }

        // POST: faqs/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create( faqs faqs)
        {
            kasvolservices t = new kasvolservices();
            faqs.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            faqs.user = _userManager.GetUserId(User);
            faqs.question = t.setvalue("ar", faqs.question);
            faqs.answer = t.setvalue("ar", faqs.answer);
            faqs.question = t.updatevalue("en",faqs.oldvalue, faqs.question);
            faqs.answer = t.updatevalue("en", faqs.oldvalue1, faqs.answer);
            faqs.question = t.updatevalue("tr", faqs.oldvalue2, faqs.question);
            faqs.answer = t.updatevalue("tr", faqs.oldvalue3, faqs.answer);

            if (ModelState.IsValid)
            {
                _db.faqs.Add(faqs);
                _db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(faqs);
        }

        // GET: faqs/Edit/5
        public ActionResult Edit(int? id)
        {
            string lang = ViewBag.lang;
            if (id == null)
            {
                return BadRequest();
            }
            faqs faqs = _db.faqs.Find(id);
            if (faqs == null)
            {
                return NotFound();
            }
            ViewBag.postcatid = _db.posts.Where(a => a.lang == lang).ToList();
            return View(faqs);
        }

        // POST: faqs/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit( faqs faqs)
        {
            kasvolservices t = new kasvolservices();
            faqs.question = t.updatevalue("ar", faqs.question, faqs.oldvalue4);
            faqs.answer = t.updatevalue("ar", faqs.answer, faqs.oldvalue5);
            faqs.question = t.updatevalue("en", faqs.oldvalue, faqs.oldvalue4);
            faqs.answer = t.updatevalue("en", faqs.oldvalue1, faqs.oldvalue5);
            faqs.question = t.updatevalue("tr", faqs.oldvalue2, faqs.oldvalue4);
            faqs.answer = t.updatevalue("tr", faqs.oldvalue3, faqs.oldvalue5);

            faqs.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();

            if (ModelState.IsValid)
            {
                var users = (from d in _db.faqs
                             where d.faqsid == faqs.faqsid
                             select d).Single();

                users.question = faqs.question;
                users.answer = faqs.answer;
                users.ogtitle = faqs.ogtitle;
                users.datemodified = DateTime.Now;
                users.modifiedIP = faqs.modifiedIP;
                _db.SaveChanges();

                return RedirectToAction("Index");
            }
            return View(faqs);
        }

        // GET: faqs/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            faqs faqs = _db.faqs.Find(id);
            if (faqs == null)
            {
                return NotFound();
            }
            return View(faqs);
        }

        // POST: faqs/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            faqs faqs = _db.faqs.Find(id);
            _db.faqs.Remove(faqs);
            _db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}


