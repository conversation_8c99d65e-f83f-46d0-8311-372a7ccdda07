using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using kasvol.service;

namespace kasvol.Controllers.Core
{
    [Authorize]
    public class bannersController : BaseController
    {
        private readonly kasvoldb _db;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public bannersController(kasvoldb db, IConfiguration configuration, kasvolservices kasvolServices,
            UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor)
            : base(db, configuration, kasvolServices)
        {
            _db = db;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
        }

        // GET: banners
        public async Task<IActionResult> Index()
        {
            return View(await _db.banners.ToListAsync());
        }

        // GET: banners/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            var banners = await _db.banners.FindAsync(id);
            if (banners == null)
            {
                return NotFound();
            }
            
            return View(banners);
        }

        // GET: banners/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: banners/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(banners banners)
        {
            if (ModelState.IsValid)
            {
                banners.IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                banners.user = _userManager.GetUserId(User);
                
                await _db.banners.AddAsync(banners);
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }

            return View(banners);
        }

        // GET: banners/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            var banners = await _db.banners.FindAsync(id);
            if (banners == null)
            {
                return NotFound();
            }
            
            return View(banners);
        }

        // POST: banners/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(banners banners)
        {
            if (ModelState.IsValid)
            {
                banners.modifiedIP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                banners.datemodified = DateTime.Now;
                
                _db.Entry(banners).State = EntityState.Modified;
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            
            return View(banners);
        }

        // GET: banners/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            var banners = await _db.banners.FindAsync(id);
            if (banners == null)
            {
                return NotFound();
            }
            
            return View(banners);
        }

        // POST: banners/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var banners = await _db.banners.FindAsync(id);
            if (banners != null)
            {
                _db.banners.Remove(banners);
                await _db.SaveChangesAsync();
            }
            
            return RedirectToAction("Index");
        }
    }
}
