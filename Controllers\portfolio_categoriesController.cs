using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authorization;
using System;
using kasvol.service;
using System.Data;
using kasvol.Models;
using System.Net;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.portfolio_Categories
                                 where d.portfolio_categoriesid == portfolio_categories.portfolio_categoriesid
                                 select d).Single();
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class portfolio_categoriesController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;

        public portfolio_categoriesController(UserManager<ApplicationUser> userManager)
        {
            _userManager = userManager;
        }


        private kasvoldb db = new kasvoldb();

        // GET: portfolio_categories
        public ActionResult Index()
        {
            return View(db.portfolio_Categories.ToList());
        }

        // GET: portfolio_categories/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            portfolio_categories portfolio_categories = db.portfolio_Categories.Find(id);
            if (portfolio_categories == null)
            {
                return NotFound();
            }
            return View(portfolio_categories);
        }

        // GET: portfolio_categories/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: portfolio_categories/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create( portfolio_categories portfolio_categories)
        {
            kasvolservices t = new kasvolservices();
            portfolio_categories.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            portfolio_categories.user = _userManager.GetUserId(User);
            portfolio_categories.title = t.setvalue(portfolio_categories.lang, portfolio_categories.title);
          
            if (ModelState.IsValid)
            {
                db.portfolio_Categories.Add(portfolio_categories);
                db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(portfolio_categories);
        }

        // GET: portfolio_categories/Edit/5
        public ActionResult Edit(int? id, string lang)
        {
            if (lang != null)
            {
                ViewBag.lang = lang;
            }
            if (id == null)
            {
                return BadRequest();
            }
            portfolio_categories portfolio_categories = db.portfolio_Categories.Find(id);
            if (portfolio_categories == null)
            {
                return NotFound();
            }
            return View(portfolio_categories);
        }

        // POST: portfolio_categories/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit( portfolio_categories portfolio_categories)
        {
            kasvolservices t = new kasvolservices();
            portfolio_categories.title = t.updatevalue(portfolio_categories.lang, portfolio_categories.title, portfolio_categories.oldvalue);

            portfolio_categories.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();

            if (ModelState.IsValid)
            {
                using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authorization;
using System;
using kasvol.service;
using System.Data;
using kasvol.Models;
using System.Net;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.portfolio_Categories
                                 where d.portfolio_categoriesid == portfolio_categories.portfolio_categoriesid
                                 select d).Single();
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

users.title = portfolio_categories.title;

                    users.datemodified = DateTime.Now;
                    users.modifiedIP = portfolio_categories.modifiedIP;
                    context.SaveChanges();
                }
                return RedirectToAction("Index");
            }
            return View(portfolio_categories);
        }

        // GET: portfolio_categories/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            portfolio_categories portfolio_categories = db.portfolio_Categories.Find(id);
            if (portfolio_categories == null)
            {
                return NotFound();
            }
            return View(portfolio_categories);
        }

        // POST: portfolio_categories/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            portfolio_categories portfolio_categories = db.portfolio_Categories.Find(id);
            db.portfolio_Categories.Remove(portfolio_categories);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}


