using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using System;
using kasvol.service;
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers.Core
{
    [Authorize(Roles = "SuperAdmin")]
    public class PCategoriesController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public PCategoriesController(KasvolDbContext db, UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor)
            : base(db, httpContextAccessor)
        {
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
        }

        // GET: PCategories
        public async Task<ActionResult> Index(string word, int? take)
        {
            int pageSize = 50;
            if (take.HasValue)
            {
                pageSize = take.Value;
            }

            var query = _db.Pcategories.Where(a => a.lang == "ar").AsQueryable();

            if (!string.IsNullOrEmpty(word))
            {
                query = query.Where(a => a.CategoryName.Contains(word));
            }

            if (pageSize > 0)
            {
                return View(await query.OrderByDescending(a => a.CategoryId).Take(pageSize).ToListAsync());
            }
            else
            {
                return View(await query.OrderByDescending(a => a.CategoryId).ToListAsync());
            }
        }

        // GET: PCategories/Details/5
        public async Task<ActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }

            portfolio_categories pCategory = await _db.Pcategories.FindAsync(id);
            if (pCategory == null)
            {
                return NotFound();
            }

            return View(pCategory);
        }

        // GET: PCategories/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: PCategories/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Create([Bind("CategoryId,CategoryName,Description,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] portfolio_categories pCategory)
        {
            pCategory.IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();
            pCategory.user = _userManager.GetUserId(User);
            pCategory.datecreated = DateTime.Now;
            pCategory.guid = Guid.NewGuid().ToString();
            pCategory.lang = "ar";

            if (ModelState.IsValid)
            {
                _db.Pcategories.Add(pCategory);
                await _db.SaveChangesAsync();

                // Create English version
                portfolio_categories enCategory = new portfolio_categories
                {
                    CategoryName = pCategory.CategoryName,
                    Description = pCategory.Description,
                    arrange = pCategory.arrange,
                    datecreated = pCategory.datecreated,
                    IP = pCategory.IP,
                    user = pCategory.user,
                    guid = pCategory.guid,
                    lang = "en",
                    ogtitle = pCategory.ogtitle,
                    ogdescription = pCategory.ogdescription,
                    keywords = pCategory.keywords,
                    ogimage = pCategory.ogimage,
                    twimage = pCategory.twimage
                };

                _db.Pcategories.Add(enCategory);

                // Create Turkish version
                portfolio_categories trCategory = new portfolio_categories
                {
                    CategoryName = pCategory.CategoryName,
                    Description = pCategory.Description,
                    arrange = pCategory.arrange,
                    datecreated = pCategory.datecreated,
                    IP = pCategory.IP,
                    user = pCategory.user,
                    guid = pCategory.guid,
                    lang = "tr",
                    ogtitle = pCategory.ogtitle,
                    ogdescription = pCategory.ogdescription,
                    keywords = pCategory.keywords,
                    ogimage = pCategory.ogimage,
                    twimage = pCategory.twimage
                };

                _db.Pcategories.Add(trCategory);
                await _db.SaveChangesAsync();

                return RedirectToAction("Index");
            }

            return View(pCategory);
        }

        // GET: PCategories/Edit/5
        public async Task<ActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }

            portfolio_categories pCategory = await _db.Pcategories.FindAsync(id);
            if (pCategory == null)
            {
                return NotFound();
            }

            return View(pCategory);
        }

        // POST: PCategories/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Edit([Bind("CategoryId,CategoryName,Description,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] portfolio_categories pCategory)
        {
            pCategory.modifiedIP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress.ToString();
            pCategory.datemodified = DateTime.Now;

            if (ModelState.IsValid)
            {
                _db.Entry(pCategory).State = EntityState.Modified;
                await _db.SaveChangesAsync();

                // Update other language versions if this is the Arabic version
                if (pCategory.lang == "ar")
                {
                    var otherCategories = await _db.Pcategories
                        .Where(p => p.guid == pCategory.guid && p.lang != "ar")
                        .ToListAsync();

                    foreach (var otherCategory in otherCategories)
                    {
                        otherCategory.arrange = pCategory.arrange;
                        otherCategory.datemodified = pCategory.datemodified;
                        otherCategory.modifiedIP = pCategory.modifiedIP;
                        otherCategory.ogimage = pCategory.ogimage;
                        otherCategory.twimage = pCategory.twimage;
                    }

                    await _db.SaveChangesAsync();
                }

                return RedirectToAction("Index");
            }

            return View(pCategory);
        }

        // GET: PCategories/Delete/5
        public async Task<ActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }

            portfolio_categories pCategory = await _db.Pcategories.FindAsync(id);
            if (pCategory == null)
            {
                return NotFound();
            }

            return View(pCategory);
        }

        // POST: PCategories/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> DeleteConfirmed(int id)
        {
            portfolio_categories pCategory = await _db.Pcategories.FindAsync(id);
            string guid = pCategory.guid;

            // Delete all language versions
            var categoriesToDelete = await _db.Pcategories
                .Where(p => p.guid == guid)
                .ToListAsync();

            foreach (var category in categoriesToDelete)
            {
                _db.Pcategories.Remove(category);
            }

            await _db.SaveChangesAsync();
            return RedirectToAction("Index");
        }
    }
}
