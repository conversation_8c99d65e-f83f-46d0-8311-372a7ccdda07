using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.sliders
                                 where d.slidersid == sliders.slidersid
                                 select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

namespace kasvol.Controllers
{
    [Authorize]
    public class slidersController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public slidersController(KasvolDbContext db, IWebHostEnvironment webHostEnvironment, UserManager<ApplicationUser> userManager) : base(db)
        {
            _userManager = userManager;
            _webHostEnvironment = webHostEnvironment;
        }

        // GET: sliders
        public ActionResult Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            return View(_db.Sliders.ToList());
        }
        public async Task<ActionResult> uploadv(int? id)
        {
            kasvolservices t = new kasvolservices();  if (id == null)
            {
                return BadRequest();
            }
            sliders page = await _db.Sliders.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            return View(page);
        }
        [HttpPost]
        public async Task<ActionResult> uploadv(int? id, IFormFile ImageFile, IFormFile ImageFile1)
        {
            kasvolservices t = new kasvolservices();
            sliders page = await _db.Sliders.FindAsync(id);

            string path = Server.MapPath("~/kasvolfactory/vi/slider/" + page.slidersid + "/");


            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }


            if (ImageFile != null)
            {
                try {
                    string normalImagePath = System.IO.Path.Combine(path, ImageFile.FileName);
                    string webPFileName = System.IO.Path.GetFileNameWithoutExtension(ImageFile.FileName) + ".webp";
                    string webPImagePath = System.IO.Path.Combine(path, webPFileName);
                    // using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.sliders
                                 where d.slidersid == sliders.slidersid
                                 select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                    string ext = System.IO.Path.GetExtension(ImageFile.FileName);
                    if (ext != ".webp")
                    {
                        var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                        using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.sliders
                                 where d.slidersid == sliders.slidersid
                                 select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                    }

                    string yy = t.getsliderimage(page.guid, "1");
                    if (yy != null) {
                        var useredit = (from d in _db.Medias
                                        where d.guid == page.guid && page.lang == "1"
                                        select d).Single();
                        useredit.name = "kasvolfactory/vi/slider/" + id + "/" + webPFileName;
                        useredit.filename = webPFileName;
                        _db.SaveChanges();

                    }
                    else
                    {
                        media media = new media();

                        media.arrange = 1;

                        media.guid = page.guid;
                        media.type = "slider";
                        media.lang = "1";
                        media.name = "kasvolfactory/vi/slider/" + id + "/" + webPFileName;
                        media.filename = webPFileName;
                        _db.Medias.Add(media);
                        _db.SaveChanges();
                    }
                }
                 catch (Exception e)
                {
                Console.WriteLine("Overflow. {0}", e.Message);
            }
            }
            

            if (ImageFile1 != null)
            {

                string normalImagePath = System.IO.Path.Combine(path, ImageFile1.FileName);
                string webPFileName = System.IO.Path.GetFileNameWithoutExtension(ImageFile1.FileName) + ".webp";
                string webPImagePath = System.IO.Path.Combine(path, webPFileName);
                // using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.sliders
                                 where d.slidersid == sliders.slidersid
                                 select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                string ext = System.IO.Path.GetExtension(ImageFile1.FileName);
                if (ext != ".webp") { 
                    var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                    using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.sliders
                                 where d.slidersid == sliders.slidersid
                                 select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                }
                string yy = t.getsliderimage(page.guid, "2");
                if (yy != null)
                {
                    var useredit = (from d in _db.Medias
                                    where d.guid == page.guid && page.lang == "2"
                                    select d).Single();
                    useredit.name = "kasvolfactory/vi/slider/" + id + "/" + webPFileName;
                    useredit.filename = webPFileName;
                    _db.SaveChanges();

                }
                else
                {
                    media media = new media();

                    media.arrange = 2;

                    media.guid = page.guid;
                    media.type = "slider";
                    media.lang = "2";
                    media.name = "kasvolfactory/vi/slider/" + id + "/" + webPFileName;
                    media.filename = webPFileName;
                 
                    _db.Medias.Add(media);
                    _db.SaveChanges();
                }
            }

            return RedirectToAction("Index");
        }
        // GET: sliders/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            sliders sliders = _db.Sliders.Find(id);
            if (sliders == null)
            {
                return NotFound();
            }
            return View(sliders);
        }

        // GET: sliders/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: sliders/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create( sliders sliders)
        {
            kasvolservices t = new kasvolservices();
            sliders.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            sliders.user = _userManager.GetUserId(User);
            sliders.title = t.setvalue(sliders.lang, sliders.title);

            if (ModelState.IsValid)
            {
                _db.Sliders.Add(sliders);
                _db.SaveChanges();
                for(int i = 1; i <= 2; i++)
                {
                    media media = new media();

                    media.arrange = i;

                    media.guid = sliders.guid;
                    media.type = "slider";
                    media.lang = i.ToString();
              

                    _db.Medias.Add(media);
                     _db.SaveChanges();
                }
                return RedirectToAction("Index");
            }

            return View(sliders);
        }

        // GET: sliders/Edit/5
        public ActionResult Edit(int? id, string lang)
        {
            if (lang != null)
            {
                ViewBag.lang = lang;
            }
            if (id == null)
            {
                return BadRequest();
            }
            sliders sliders = _db.Sliders.Find(id);
            if (sliders == null)
            {
                return NotFound();
            }
            return View(sliders);
        }

        // POST: sliders/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit( sliders sliders)
        {
            kasvolservices t = new kasvolservices();
            sliders.title = t.updatevalue(sliders.lang, sliders.title, sliders.oldvalue);

            sliders.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();

            if (ModelState.IsValid)
            {
                using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using (var context = new kasvoldb())
                {

                    var users = (from d in context.sliders
                                 where d.slidersid == sliders.slidersid
                                 select d).Single();
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

users.title = sliders.title;

                    users.datemodified = DateTime.Now;
                    users.modifiedIP = sliders.modifiedIP;
                    context.SaveChanges();
                }
               _db.Entry(sliders).State = EntityState.Modified;
                _db.SaveChanges();
                return RedirectToAction("Index");
            }
            return View(sliders);
        }

        // GET: sliders/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            sliders sliders = _db.Sliders.Find(id);
            if (sliders == null)
            {
                return NotFound();
            }
            return View(sliders);
        }

        // POST: sliders/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            sliders sliders = _db.Sliders.Find(id);
            _db.Sliders.Remove(sliders);
            _db.SaveChanges();
            return RedirectToAction("Index");
        }


    }
}






