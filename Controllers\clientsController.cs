using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.clients
                                 where d.clientsid == dd
                                 select d).SingleOrDefault();
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid
                                            select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                {

                    var users = (from d in context.clients
                                 where d.clientsid == clients.clientsid
                                 select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

namespace kasvol.Controllers
{
    [Authorize]
    public class clientsController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;

                private readonly IWebHostEnvironment _webHostEnvironment;
public clientsController(IWebHostEnvironment webHostEnvironment, UserManager<ApplicationUser> userManager)
        {
            _userManager = userManager;
            _webHostEnvironment = webHostEnvironment;
        }


        private kasvoldb db = new kasvoldb();
        [HttpPost]
        public JsonResult editalt(int id, bool alt)
        {
            int dd = Convert.ToInt32(id);
            using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.clients
                                 where d.clientsid == dd
                                 select d).SingleOrDefault();
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid
                                            select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                {

                    var users = (from d in context.clients
                                 where d.clientsid == clients.clientsid
                                 select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

agentedit.showinhome = alt;
                context.SaveChanges();
            }


            dynamic showMessageString = string.Empty;
            showMessageString = new
            {
                param1 = 200,
                param2 = "Done !!!"
            };
            return Json(showMessageString, JsonRequestBehavior.AllowGet);





        }
        // GET: clients
        public ActionResult Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            return View(db.clients.ToList());
        }
        public async Task<ActionResult> uploadv(int? id)
        {
            kasvolservices t = new kasvolservices(); if (id == null)
            {
                return BadRequest();
            }
            clients page = await db.clients.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            return View(page);
        }
        [HttpPost]
        public async Task<ActionResult> uploadv(int? id, IFormFile ImageFile, IFormFile ImageFile1)
        {
            kasvolservices t = new kasvolservices();
            clients page = await db.clients.FindAsync(id);

            string path = Server.MapPath("~/kasvolfactory/vi/client/" + page.clientsid + "/");


            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }


            if (ImageFile != null)
            {
                try
                {
                    string normalImagePath = System.IO.Path.Combine(path, ImageFile.FileName);
                    string webPFileName = System.IO.Path.GetFileNameWithoutExtension(ImageFile.FileName) + ".webp";
                    string webPImagePath = System.IO.Path.Combine(path, webPFileName);
                    // using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.clients
                                 where d.clientsid == dd
                                 select d).SingleOrDefault();
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid
                                            select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                {

                    var users = (from d in context.clients
                                 where d.clientsid == clients.clientsid
                                 select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                    string ext = System.IO.Path.GetExtension(ImageFile.FileName);
                    if (ext != ".webp")
                    {
                        var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                        using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.clients
                                 where d.clientsid == dd
                                 select d).SingleOrDefault();
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid
                                            select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                {

                    var users = (from d in context.clients
                                 where d.clientsid == clients.clientsid
                                 select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                    }

                    string yy = t.getimage(page.guid);
                    if (yy != null)
                    {
                        using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.clients
                                 where d.clientsid == dd
                                 select d).SingleOrDefault();
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid
                                            select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                {

                    var users = (from d in context.clients
                                 where d.clientsid == clients.clientsid
                                 select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

useredit.name = "kasvolfactory/vi/clients/" + id + "/" + webPFileName;
                            useredit.filename = webPFileName;
                            context.SaveChanges();
                        }

                    }
                    else
                    {
                        media media = new media();

                        media.arrange = 1;

                        media.guid = page.guid;

                        media.type = "clients";
                        media.name = "kasvolfactory/vi/clients/" + id + "/" + webPFileName;
                        media.filename = webPFileName;
                        using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.clients
                                 where d.clientsid == dd
                                 select d).SingleOrDefault();
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid
                                            select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                {

                    var users = (from d in context.clients
                                 where d.clientsid == clients.clientsid
                                 select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

db1.SaveChanges();
                        }
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine("Overflow. {0}", e.Message);
                }
            }




            return RedirectToAction("Index");
        }
        // GET: clients/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            clients clients = db.clients.Find(id);
            if (clients == null)
            {
                return NotFound();
            }
            return View(clients);
        }

        // GET: clients/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: clients/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(clients clients)
        {
            kasvolservices t = new kasvolservices();
            clients.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            clients.user = _userManager.GetUserId(User);
            clients.title = t.setvalue(clients.lang, clients.title);
            if (ModelState.IsValid)
            {
                db.clients.Add(clients);
                db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(clients);
        }

        // GET: clients/Edit/5
        public ActionResult Edit(int? id, string lang)
        {
            if (lang != null)
            {
                ViewBag.lang = lang;
            }
            if (id == null)
            {
                return BadRequest();
            }
            clients clients = db.clients.Find(id);
            if (clients == null)
            {
                return NotFound();
            }
            return View(clients);
        }

        // POST: clients/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit( clients clients)
        {
            kasvolservices t = new kasvolservices();
            clients.title = t.updatevalue(clients.lang, clients.title, clients.oldvalue);

            clients.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();

            if (ModelState.IsValid)
            {
                using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.clients
                                 where d.clientsid == dd
                                 select d).SingleOrDefault();
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net;
using (kasvoldb db1 = new kasvoldb())
                        {
                            db1.medias.Add(media);
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using kasvol.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (var context = new kasvoldb())
                        {
                            var useredit = (from d in context.medias
                                            where d.guid == page.guid
                                            select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using (var context = new kasvoldb())
                {

                    var users = (from d in context.clients
                                 where d.clientsid == clients.clientsid
                                 select d).Single();
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

users.title = clients.title;

                    users.datemodified = DateTime.Now;
                    users.modifiedIP = clients.modifiedIP;
                    context.SaveChanges();
                }

                return RedirectToAction("Index");
            }
            return View(clients);
        }

        // GET: clients/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            clients clients = db.clients.Find(id);
            if (clients == null)
            {
                return NotFound();
            }
            return View(clients);
        }

        // POST: clients/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            clients clients = db.clients.Find(id);
            db.clients.Remove(clients);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}






