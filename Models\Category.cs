using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace kasvol.Models
{
    public class Category : Baseclass
    {
        [Key]
        public int CategoryID { get; set; }

        [Required]
        public string CategoryName { get; set; }

       
        public string Description { get; set; }
        public string ImagePath { get; set; }
        public string slogan { get; set; }
        public int numofvisit { get; set; } = 0;
        public int ParentcatID { get; set; } = 0;
        public virtual ICollection<Product> Products { get; set; }
    }
}