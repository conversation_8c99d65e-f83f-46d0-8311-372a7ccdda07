using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;


namespace kasvol.Models
{
    public class Baseclass
    {
     
      
        public int arrange { get; set; } = 0;
        public DateTime datecreated { get; set; } = DateTime.Now;
        public DateTime datemodified { get; set; } = DateTime.Now;
        public string IP { get; set; } 
        public string modifiedIP { get; set; } 
        public string user { get; set; }
        public string year { get; set; } = DateTime.Now.Year.ToString();
        public string guid { get; set; } = Guid.NewGuid().ToString();

        public string lang { get; set; }
        public string ogtitle { get; set; }
        public string ogdescription { get; set; }
        public string keywords { get; set; }
        public string ogimage { get; set; }
        public string twimage { get; set; }
        [NotMapped]
        public string oldvalue { get; set; }
        [NotMapped]
        public string oldvalue1 { get; set; }
        [NotMapped]
        public string oldvalue2 { get; set; }
        [NotMapped]
        public string oldvalue3 { get; set; }
        [NotMapped]
        public string oldvalue4 { get; set; }
        [NotMapped]
        public string oldvalue5 { get; set; }

    }
}