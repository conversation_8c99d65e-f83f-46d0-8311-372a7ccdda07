@model IEnumerable<kasvol.Models.Sliders>

@{
    ViewBag.Title = @Resources.Resource.String102;
    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}

<div class="card card-custom">
    <div class="card-header flex-wrap py-5">
        <div class="card-title">
            <h3 class="card-label">
                @Resources.Resource.String102

            </h3>
        </div>
        <div class="card-toolbar">
            <!--begin::Dropdown-->
            <!--end::Dropdown-->
            <!--begin::Button-->
            <a href="@Url.Action("Create", "sliders")" class="btn btn-primary font-weight-bolder">
                <span class="svg-icon svg-icon-md">
                    <!--begin::Svg Icon | path:assets/media/svg/icons/Design/Flatten.svg-->
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <rect x="0" y="0" width="24" height="24"></rect>
                            <circle fill="#000000" cx="9" cy="15" r="6"></circle>
                            <path d="M8.8012943,7.00241953 C9.83837775,5.20768121 11.7781543,4 14,4 C17.3137085,4 20,6.6862915 20,10 C20,12.2218457 18.7923188,14.1616223 16.9975805,15.1987057 C16.9991904,15.1326658 17,15.0664274 17,15 C17,10.581722 13.418278,7 9,7 C8.93357256,7 8.86733422,7.00080962 8.8012943,7.00241953 Z" fill="#000000" opacity="0.3"></path>
                        </g>
                    </svg>
                    <!--end::Svg Icon-->
                </span> @Resources.Resource.String12
            </a>
            <!--end::Button-->
        </div>

    </div>
    <div class="card-body" style="margin-bottom:125px">



        <table class="table table-separate table-head-custom table-checkable dataTable no-footer dtr-inline" id="example">
            <thead>
                <tr>
                    <th>
                        ID
                    </th>
                    <th>
                         @Html.Label(@Resources.Resource.String4)
                    </th>
                    
                    
                    <th>
                        @Html.DisplayNameFor(model => model.datemodified)
                    </th>
                   
                    <th></th>
                </tr>
            </thead>
            <tbody>
                @using kasvol.service;
                @{kasvolservices t = new kasvolservices(); }
                @foreach (var item in Model)
                {
                <tr>
                    @{


                        string value = t.getvalue(ViewBag.lang, item.title);



                    }
                    <td>
                        @Html.DisplayFor(modelItem => item.slidersid)
                    </td>
                 

                    <td>
                        @value
                    </td>

                    <td>
                        @item.datecreated.ToString("yyyy-MM-dd HH:mm")
                    </td>

                    <td>
                        <a href='@Url.Action("uploadv", "sliders", new { id = item.slidersid })' style="font-size:16px;"><i class="ace-icon fa fa-image" style="color:#ff00dc;margin-left:4px"></i> @Resources.Resource.String7 </a>|

                        <a href="@Url.Action("Edit", "sliders", new { id = item.slidersid, lang = "ar" })" style="font-size:16px;"><i class="ace-icon fa fa-pencil-alt " style="color:#057928;margin-left:4px"></i> @Resources.Resource.String9   AR</a>|
                        <a href="@Url.Action("Edit", "sliders", new { id = item.slidersid, lang = "en" })" style="font-size:16px;"><i class="ace-icon fa fa-pencil-alt " style="color:#057928;margin-left:4px"></i> @Resources.Resource.String9   EN</a>|
                        @*<a href="@Url.Action("Details", "sliders", new { id = item.slidersid })" style="font-size:16px;"><i class="ace-icon fa fa-list" style="color:#ff6a00;margin-left:4px"></i> @Resources.Resource.String10 </a>|*@


                        <a href="@Url.Action("Delete", "sliders", new { id = item.slidersid })" style="font-size:16px;"><i class="ace-icon fa fa-trash" style="color:#ff0000;margin-left:4px"></i> @Resources.Resource.String11 </a>


                    </td>
                </tr>

                }
            </tbody>
        </table>
    </div>

</div>
<link href="/assets/plugins/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
<link href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css" rel="stylesheet" type="text/css" />
@section Scripts {


    <script src="//cdn.datatables.net/1.10.22/js/jquery.dataTables.min.js" type="text/javascript"></script>
    <script>
        $(document).ready(function () {

            $('#example').dataTable({
                "scrollX": true, "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.20/i18n/@ViewBag.ar"}

            });
        });

    </script>
}