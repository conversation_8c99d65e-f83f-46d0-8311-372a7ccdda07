
<!DOCTYPE html>

<html>
<!--begin::Head-->
<head>
    <base href="">
    <meta charset="utf-8" />
    <meta name="robots" content="noindex, nofollow">
    <meta name="googlebot" content="noindex, nofollow">
    <title>@ViewBag.Title | kasvol</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Cairo&display=swap" rel="stylesheet">

    <!--end::Fonts-->
    <!--begin::Page Vendors Styles(used by this page)-->
    <link href="/dashboard/assets/plugins/custom/fullcalendar/fullcalendar.bundle.css" rel="stylesheet" type="text/css" />
    <!--end::Page Vendors Styles-->
    <!--begin::Global Theme Styles(used by all pages)-->
    <link href="/dashboard/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <link href="/dashboard/assets/plugins/custom/prismjs/prismjs.bundle.css" rel="stylesheet" type="text/css" />
    <link href="/dashboard/assets/css/style.bundle.css?v=1" rel="stylesheet" type="text/css" />
    <!--end::Global Theme Styles-->
    <!--begin::Layout Themes(used by all pages)-->
    <link href="/dashboard/assets/css/themes/layout/header/base/light.css" rel="stylesheet" type="text/css" />
    <link href="/dashboard/assets/css/themes/layout/header/menu/light.css" rel="stylesheet" type="text/css" />
    <link href="/dashboard/assets/css/themes/layout/brand/dark.css" rel="stylesheet" type="text/css" />
    <link href="/dashboard/assets/css/themes/layout/aside/dark.css" rel="stylesheet" type="text/css" />


    <!--end::Layout Themes-->
    <link rel="shortcut icon" href="/dashboard/assets/media/logos/favicon.ico" />
    <style>
        .error {
            color: red !important;
            text-align: start
        }

        .modal1 {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            background-color: white;
            z-index: 99;
            opacity: 0.5;
            filter: alpha(opacity=50);
            -moz-opacity: 0.5;
            min-height: 100%;
            width: 100%;
        }

        .throbber {
            display: block;
            z-index: 9999999
        }

            .throbber .curtain {
                background-color: #fff;
                filter: alpha(opacity=60);
                height: 100%;
                left: 0;
                opacity: 0.6;
                position: fixed;
                top: 0;
                width: 100%;
                z-index: 99999999999
            }

            .throbber .curtain-content {
                height: 50%;
                left: 50%;
                position: absolute;
                top: 50%;
                width: 50%;
                z-index: 99999999999
            }

                .throbber .curtain-content div {
                    color: #FFF;
                    text-align: center;
                    z-index: 99999999999
                }



        #preloader {
            position: fixed;
            top: 50%;
            left: 50%;
            z-index: 99999999999
        }

        #loader {
            z-index: 99999999999;
            display: block;
            position: relative;
            left: 50%;
            top: 50%;
            width: 150px;
            height: 150px;
            margin: -75px 0 0 -75px;
            border-radius: 50%;
            border: 3px solid transparent;
            border-top-color: #9370DB;
            -webkit-animation: spin 2s linear infinite;
            animation: spin 2s linear infinite;
            opacity: 0.9;
            filter: alpha(opacity=90);
            -moz-opacity: 0.9;
        }

            #loader:before {
                width: 150px;
                height: 150px;
                content: "";
                position: absolute;
                top: 5px;
                left: 5px;
                right: 5px;
                bottom: 5px;
                border-radius: 50%;
                border: 3px solid transparent;
                border-top-color: #BA55D3;
                -webkit-animation: spin 3s linear infinite;
                animation: spin 3s linear infinite;
                opacity: 0.9;
                filter: alpha(opacity=90);
                -moz-opacity: 0.9;
                z-index: 9999999
            }

            #loader:after {
                width: 150px;
                height: 150px;
                content: "";
                position: absolute;
                top: 15px;
                left: 15px;
                right: 15px;
                bottom: 15px;
                border-radius: 50%;
                border: 3px solid transparent;
                border-top-color: #FF00FF;
                -webkit-animation: spin 1.5s linear infinite;
                animation: spin 1.5s linear infinite;
                opacity: 0.9;
                filter: alpha(opacity=90);
                -moz-opacity: 0.9;
                z-index: 9999999
            }

        @@-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
                -ms-transform: rotate(0deg);
                transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
                -ms-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }

        @@keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
                -ms-transform: rotate(0deg);
                transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
                -ms-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<!--end::Head-->
<!--begin::Body-->
<body id="kt_body" class="header-fixed header-mobile-fixed subheader-enabled subheader-fixed aside-enabled aside-fixed aside-minimize aside-minimize-hoverable page-loading">
    <!--begin::Main-->
    <!--begin::Header Mobile-->
    <div id="kt_header_mobile" class="header-mobile align-items-center header-mobile-fixed">
        <!--begin::Logo-->
        <a href="@Url.Action("Index", "agent")">
            <img alt="Logo" src="/dashboard/assets/media/logos/logo-light.png" />
        </a>
        <!--end::Logo-->
        <!--begin::Toolbar-->
        <div class="d-flex align-items-center">
            <!--begin::Aside Mobile Toggle-->
            <button class="btn p-0 burger-icon burger-icon-left" id="kt_aside_mobile_toggle">
                <span></span>
            </button>
            <!--end::Aside Mobile Toggle-->
            <!--begin::Header Menu Mobile Toggle-->
            <button class="btn p-0 burger-icon ml-4" id="kt_header_mobile_toggle">
                <span></span>
            </button>
            <!--end::Header Menu Mobile Toggle-->
            <!--begin::Topbar Mobile Toggle-->
            <button class="btn btn-hover-text-primary p-0 ml-2" id="kt_header_mobile_topbar_toggle">
                <span class="svg-icon svg-icon-xl">
                    <!--begin::Svg Icon | path:assets/media/svg/icons/General/User.svg-->
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <polygon points="0 0 24 0 24 24 0 24" />
                            <path d="M12,11 C9.790861,11 8,9.209139 8,7 C8,4.790861 9.790861,3 12,3 C14.209139,3 16,4.790861 16,7 C16,9.209139 14.209139,11 12,11 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" />
                            <path d="M3.00065168,20.1992055 C3.38825852,15.4265159 7.26191235,13 11.9833413,13 C16.7712164,13 20.7048837,15.2931929 20.9979143,20.2 C21.0095879,20.3954741 20.9979143,21 20.2466999,21 C16.541124,21 11.0347247,21 3.72750223,21 C3.47671215,21 2.97953825,20.45918 3.00065168,20.1992055 Z" fill="#000000" fill-rule="nonzero" />
                        </g>
                    </svg>
                    <!--end::Svg Icon-->
                </span>
            </button>
            <!--end::Topbar Mobile Toggle-->
        </div>
        <!--end::Toolbar-->
    </div>
    <!--end::Header Mobile-->
    <div class="d-flex flex-column flex-root">
        <!--begin::Page-->
        <div class="d-flex flex-row flex-column-fluid page">
            <!--begin::Aside-->
            <div class="aside aside-left aside-fixed d-flex flex-column flex-row-auto" id="kt_aside">
                <!--begin::Brand-->
                <div class="brand flex-column-auto" id="kt_brand">
                    <!--begin::Logo-->
                    <a href="@Url.Action("Index", "agent")" class="brand-logo">
                        <img alt="Logo" src="/dashboard/assets/media/logos/logo-light.png" style="height:65px" />
                    </a>
                    <!--end::Logo-->
                    <!--begin::Toggle-->
                    <button class="brand-toggle btn btn-sm px-0" id="kt_aside_toggle">
                        <span class="svg-icon svg-icon svg-icon-xl">
                            <!--begin::Svg Icon | path:assets/media/svg/icons/Navigation/Angle-double-left.svg-->
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <polygon points="0 0 24 0 24 24 0 24" />
                                    <path d="M5.29288961,6.70710318 C4.90236532,6.31657888 4.90236532,5.68341391 5.29288961,5.29288961 C5.68341391,4.90236532 6.31657888,4.90236532 6.70710318,5.29288961 L12.7071032,11.2928896 C13.0856821,11.6714686 13.0989277,12.281055 12.7371505,12.675721 L7.23715054,18.675721 C6.86395813,19.08284 6.23139076,19.1103429 5.82427177,18.7371505 C5.41715278,18.3639581 5.38964985,17.7313908 5.76284226,17.3242718 L10.6158586,12.0300721 L5.29288961,6.70710318 Z" fill="#000000" fill-rule="nonzero" transform="translate(8.999997, 11.999999) scale(-1, 1) translate(-8.999997, -11.999999)" />
                                    <path d="M10.7071009,15.7071068 C10.3165766,16.0976311 9.68341162,16.0976311 9.29288733,15.7071068 C8.90236304,15.3165825 8.90236304,14.6834175 9.29288733,14.2928932 L15.2928873,8.29289322 C15.6714663,7.91431428 16.2810527,7.90106866 16.6757187,8.26284586 L22.6757187,13.7628459 C23.0828377,14.1360383 23.1103407,14.7686056 22.7371482,15.1757246 C22.3639558,15.5828436 21.7313885,15.6103465 21.3242695,15.2371541 L16.0300699,10.3841378 L10.7071009,15.7071068 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" transform="translate(15.999997, 11.999999) scale(-1, 1) rotate(-270.000000) translate(-15.999997, -11.999999)" />
                                </g>
                            </svg>
                            <!--end::Svg Icon-->
                        </span>
                    </button>
                    <!--end::Toolbar-->
                </div>
                <!--end::Brand-->
                <!--begin::Aside Menu-->
                <div class="aside-menu-wrapper flex-column-fluid" id="kt_aside_menu_wrapper">
                 
                </div>
                <!--end::Aside Menu-->
            </div>
            <!--end::Aside-->
            <!--begin::Wrapper-->
            <div class="d-flex flex-column flex-row-fluid wrapper" id="kt_wrapper">
                <!--begin::Header-->
                <div id="kt_header" class="header header-fixed ">
                    <!--begin::Container-->
                    <div class="container-fluid d-flex align-items-stretch justify-content-between" style="direction: ltr;">
                        <div class="header-menu-wrapper header-menu-wrapper-left" id="kt_header_menu_wrapper">
                            <!--begin::Header Menu-->
                            <div id="kt_header_menu" class="header-menu header-menu-mobile header-menu-layout-default">
                                <!--begin::Header Nav-->
                                <ul class="menu-nav">
                                  
                                    <li class="menu-item menu-item-open menu-item-here menu-item-submenu menu-item-rel menu-item-open menu-item-here menu-item-active" data-menu-toggle="click" aria-haspopup="true">
                                        <a href="@Url.Action("Index", "agent")" class="menu-link ">
                                            <span class="menu-text">@Resources.Resource.String133</span>

                                        </a>

                                    </li>
                                 

                                </ul>
                                <!--end::Header Nav-->
                            </div>
                            <!--end::Header Menu-->
                        </div>
                        <!--begin::Topbar-->
                        <div class="topbar  float-right">
                            <!--<div class="topbar-item">
                            <div class="btn btn-icon btn-clean btn-lg mr-1">
                                <a href="@Url.Action("Create", "services")">
                                    <span class="svg-icon svg-icon-xl svg-icon-primary">-->
                            <!--begin::Svg Icon | path:assets/media/svg/icons/Layout/Layout-4-blocks.svg-->
                            <!--<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <rect x="0" y="0" width="24" height="24"></rect>
                                    <rect fill="#000000" x="4" y="4" width="7" height="7" rx="1.5"></rect>
                                    <path d="M5.5,13 L9.5,13 C10.3284271,13 11,13.6715729 11,14.5 L11,18.5 C11,19.3284271 10.3284271,20 9.5,20 L5.5,20 C4.67157288,20 4,19.3284271 4,18.5 L4,14.5 C4,13.6715729 4.67157288,13 5.5,13 Z M14.5,4 L18.5,4 C19.3284271,4 20,4.67157288 20,5.5 L20,9.5 C20,10.3284271 19.3284271,11 18.5,11 L14.5,11 C13.6715729,11 13,10.3284271 13,9.5 L13,5.5 C13,4.67157288 13.6715729,4 14.5,4 Z M14.5,13 L18.5,13 C19.3284271,13 20,13.6715729 20,14.5 L20,18.5 C20,19.3284271 19.3284271,20 18.5,20 L14.5,20 C13.6715729,20 13,19.3284271 13,18.5 L13,14.5 C13,13.6715729 13.6715729,13 14.5,13 Z" fill="#000000" opacity="0.3"></path>
                                </g>
                            </svg>-->
                            <!--end::Svg Icon-->
                            <!--</span>
                                    </a>
                                </div>
                            </div>-->
                         
                            <!--begin::Languages-->
                            <div class="dropdown">
                                <!--begin::Toggle-->
                                <div class="topbar-item" data-toggle="dropdown" data-offset="10px,0px">
                                    <div class="btn btn-icon btn-clean btn-dropdown btn-lg mr-1">
                                        @if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                                        {<img class="h-20px w-20px rounded-sm" src="/dashboard/assets/media/svg/flags/133-saudi-arabia.svg" alt="" />
                                        }
                                        else
                                        {
                                            <img class="h-20px w-20px rounded-sm" src="/dashboard/assets/media/svg/flags/226-united-states.svg" alt="" />
                                        }
                                    </div>
                                </div>
                                <!--end::Toggle-->
                                <!--begin::Dropdown-->
                                <div class="dropdown-menu p-0 m-0 dropdown-menu-anim-up dropdown-menu-sm dropdown-menu-right">
                                    <!--begin::Nav-->
                                    <ul class="navi navi-hover py-4">
                                        <!--begin::Item-->
                                        <li class="navi-item active">
                                            <a href="@Url.Action("Chlanarcp")" class="navi-link">
                                                <span class="symbol symbol-20 mr-3">
                                                    <img src="/dashboard/assets/media/svg/flags/133-saudi-arabia.svg" alt="" />
                                                </span>
                                                <span class="navi-text">Arabic</span>
                                            </a>
                                        </li>
                                        <!--end::Item-->
                                        <!--begin::Item-->
                                        <li class="navi-item">
                                            <a href="@Url.Action("Chlanencp")" class="navi-link">
                                                <span class="symbol symbol-20 mr-3">
                                                    <img src="/dashboard/assets/media/svg/flags/226-united-states.svg" alt="" />
                                                </span>
                                                <span class="navi-text">English</span>
                                            </a>
                                        </li>
                                        <!--end::Item-->


                                    </ul>
                                    <!--end::Nav-->
                                </div>
                                <!--end::Dropdown-->
                            </div>
                            
                            @if (@User.Identity.GetUserName().Length <= 0)
                            {
                                <script>

                                    function gotologin() {
                                        location.href = '/Account/login';
                                    }
                                    gotologin();
                                </script>
                            }
                            @if (Request.IsAuthenticated)
                            {
                                <div class="topbar-item float-right">
                                    <div class="btn btn-icon btn-icon-mobile w-auto btn-clean d-flex align-items-center btn-lg px-2" id="kt_quick_user_toggle">
                                        <span class="text-muted font-weight-bold font-size-base d-none d-md-inline mr-1">Hi,</span>
                                        <span class="text-dark-50 font-weight-bolder font-size-base d-none d-md-inline mr-3">@User.Identity.GetUserName().Replace("@kasvolfactory.com", "")</span>
                                        <span class="symbol symbol-lg-35 symbol-25 symbol-light-success">
                                            <span class="symbol-label font-size-h5 font-weight-bold">@User.Identity.GetUserName().Substring(0, 1)</span>

                                        </span>
                                    </div>
                                </div>}
                            else
                            {
                                <script>

                                    function gotologin() {
                                        location.href = '/Account/login';
                                    }
                                    gotologin();
                                </script>
                            }
                            <!--end::Languages-->
                            <!--begin::User-->
                            <!--end::User-->
                        </div>
                        <!--end::Topbar-->
                    </div>
                    <!--end::Container-->
                </div>
                <!--end::Header-->
                <!--begin::Content-->
                @RenderBody()

                <!--end::Content-->
                <!--begin::Footer-->
                <div class="footer bg-white py-4 d-flex flex-lg-column " style="text-align:center">
                    <!--begin::Container-->
                    <div class="container-fluid d-flex flex-column flex-md-row align-items-center justify-content-between">
                        <!--begin::Copyright-->
                        <div class="text-dark order-2 order-md-1">
                            <span class="text-muted font-weight-bold mr-2">2024©</span>
                            <a href="https://kasvol.com" target="_blank" class="text-dark-75 text-hover-primary">kasvol</a>
                        </div>
                        <!--end::Copyright-->
                        <!--begin::Nav-->
                        <div class="nav nav-dark">
                            <a href="https://perfectjobline.com" target="_blank" class="nav-link pl-0 pr-5">Perfectjobline</a>

                        </div>
                        <!--end::Nav-->
                    </div>
                    <!--end::Container-->
                </div>
                <!--end::Footer-->
            </div>
            <!--end::Wrapper-->
        </div>
        <!--end::Page-->
    </div>
    <!--end::Main-->
    <!-- begin::User Panel-->
    <div id="kt_quick_user" class="offcanvas offcanvas-right p-10 ">
        <!--begin::Header-->
        <div class="offcanvas-header d-flex align-items-center justify-content-between pb-5" style="" kt-hidden-height="40">
            <h3 class="font-weight-bold m-0">
                User Profile
                <small class="text-muted font-size-sm ml-2"></small>
            </h3>
            <a href="#" class="btn btn-xs btn-icon btn-light btn-hover-primary" id="kt_quick_user_close">
                <i class="ki ki-close icon-xs text-muted"></i>
            </a>
        </div>
        <!--end::Header-->
        <!--begin::Content-->
        <div class="offcanvas-content pr-5 mr-n5 scroll ps ps--active-y" style="height: 232px; overflow: hidden;">
            <!--begin::Header-->
            @using kasvol.service;
            @{ kasvolservices yc18998 = new kasvolservices();

                //string ys18998 = yc18998.photolink(User.Identity.GetUserName().Replace("@kasvolfactory.com", ""));
                string ys1899 = yc18998.name(User.Identity.GetUserName().Replace("@kasvolfactory.com", ""));
                string ys1898 = yc18998.work(User.Identity.GetUserName().Replace("@kasvolfactory.com", ""));
            }
            <div class="d-flex align-items-center mt-5">
                <div class="symbol symbol-100 mr-5">
                    <div class="symbol-label" style="background-image: url('https://kasvol.com/ys18998')"></div>
                    <i class="symbol-badge bg-success"></i>
                </div>
                <div class="d-flex flex-column">
                    <a href="#" class="font-weight-bold font-size-h5 text-dark-75 text-hover-primary">@ys1899</a>
                    <div class="text-muted mt-1">@ys1898</div>

                </div>
            </div> <div>
                @using (Html.BeginForm("LogOff", "Account", FormMethod.Post, new { id = "logoutForm", @class = "navbar-right" }))
                {
                    @Html.AntiForgeryToken()
                    <a href="javascript:document.getElementById('logoutForm').submit()" class="btn btn-sm btn-light-primary font-weight-bolder py-2 px-5 float-right">Sign Out</a>
                }
            </div>
        </div>
        <!--end::Content-->
    </div>


    <!--end::Demo Panel-->
    <!--end::Global Config-->
    <!--begin::Global Theme Bundle(used by all pages)-->
    <script src="/dashboard/assets/plugins/global/plugins.bundle.js"></script>
    <script src="/dashboard/assets/plugins/custom/prismjs/prismjs.bundle.js"></script>
    <script src="/dashboard/assets/js/scripts.bundle.js"></script>
    <!--end::Global Theme Bundle-->
    <!--begin::Page Vendors(used by this page)-->
    <script src="/dashboard/assets/plugins/custom/fullcalendar/fullcalendar.bundle.js"></script>
    <!--end::Page Vendors-->
    <!--begin::Page Scripts(used by this page)-->
    <script src="/dashboard/assets/js/pages/widgets.js"></script>
    <!--end::Page Scripts-->
    @{ if (Request.Cookies["CurrentLanguage"].Value != null)
        {
            if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
            {
                <script>
                    $('html').attr('dir', 'rtl');
                    $('html').attr('lang', 'ar');</script>
            }
            else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
            {
                <script>
                    $('html').attr('dir', 'ltr');
                    $('html').attr('lang', 'en');</script>

            }
            else
            {
                <script>
                    $('html').attr('dir', 'ltr');
                    $('html').attr('lang', 'tr');</script>

            }
        } }
    @RenderSection("scripts", required: false)

</body>
<!--end::Body-->
</html>




