using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using System;
using kasvol.service;
using kasvol.services;
using kasvol.Models;
using System.Net;
using System.Net.Http;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{



   
    public class poapiController : ControllerBase
    {
        private kasvoldb db = new kasvoldb();

        [HttpGet]


        public IActionResult postapi(string lang, string pageNumber)
        {
            kasvolservices t = new kasvolservices();
            int defaultPageNumber = 1; // ?????? ??????????
            int pageSize = 12; // ??? ??????? ?? ?? ????

            int requestedPage;
            if (!int.TryParse(pageNumber, out requestedPage))
            {
                requestedPage = defaultPageNumber;
            }
            db.Configuration.ProxyCreationEnabled = false;
            var initialData = db.posts.Where(a => a.lang == lang).OrderByDescending(a => a.arrange).Skip((requestedPage - 1) * pageSize).Take(pageSize).ToList();
            // ????? ???????? ??? ????? ??????? ??????? (View Models) ???????? LINQ to Objects

            if (lang == "ar")
            {

                var postList = initialData.Select(p => new PViewModel
                {
                    description = p.content.Length > 250 ? p.content.Substring(0, 250) : p.content,
                    title = p.title,
                    link = "https://kasvol.com/post/" + p.slogan /*t.EncodeUrlIfNeeded(p.slogan)*/,
                    pubDate = p.datemodified,
                    image = "https://kasvol.com/" + t.EncodeSpaces(t.getimagewithid(p.guid, "1"))
                }).ToList();
                return Ok(postList);
            }
            else
            {
                var postList = initialData.Select(p => new PViewModel
                {
                    description = p.content.Length > 250 ? p.content.Substring(0, 250) : p.content,
                    title = p.title,
                    link = "https://kasvol.com/en/post/" + p.slogan /*t.EncodeUrlIfNeeded(p.slogan)*/,
                    pubDate = p.datemodified,
                    image = "https://kasvol.com/" + t.EncodeSpaces(t.getimagewithid(p.guid, "1"))
                }).ToList();
                return Ok(postList);

            }
        }
        
        public class PViewModel
        {
            public string title { get; set; }
            public string link { get; set; }
            public string description { get; set; }
            public DateTime pubDate { get; set; }
            public string image { get; set; }
        }
    }
}

