using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using Microsoft.AspNetCore.Antiforgery;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;

namespace kasvol.Filters
{
    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = false, Inherited = true)]
    public sealed class ValidateHeaderAntiForgeryToken : Attribute, IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext filterContext)
        {
            if (filterContext == null)
            {
                throw new ArgumentNullException(nameof(filterContext));
            }

            var antiforgery = filterContext.HttpContext.RequestServices.GetService<IAntiforgery>();
            var tokenValid = antiforgery.IsRequestValidAsync(filterContext.HttpContext).GetAwaiter().GetResult();
            
            if (!tokenValid)
            {
                filterContext.Result = new UnauthorizedResult();
            }
        }
    }
}
    