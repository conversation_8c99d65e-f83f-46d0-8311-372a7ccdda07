using Microsoft.AspNetCore.Authorization;
using System;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class mediaController : Controller
    {
        private kasvoldb db = new kasvoldb();

        // GET: media
        public ActionResult Index()
        {
            return View(db.medias.ToList());
        }

        // GET: media/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            media media = db.medias.Find(id);
            if (media == null)
            {
                return NotFound();
            }
            return View(media);
        }

        // GET: media/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: media/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("mediaid,filename,name,type,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] media media)
        {
            if (ModelState.IsValid)
            {
                db.medias.Add(media);
                db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(media);
        }

        // GET: media/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            media media = db.medias.Find(id);
            if (media == null)
            {
                return NotFound();
            }
            return View(media);
        }

        // POST: media/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("mediaid,filename,name,type,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] media media)
        {
            if (ModelState.IsValid)
            {
                db.Entry(media).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("Index");
            }
            return View(media);
        }

        // GET: media/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            media media = db.medias.Find(id);
            if (media == null)
            {
                return NotFound();
            }
            return View(media);
        }

        // POST: media/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            media media = db.medias.Find(id);
            db.medias.Remove(media);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}


