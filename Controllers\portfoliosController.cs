using Microsoft.AspNetCore.Authorization;
using System;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class portfoliosController : Controller
    {
        private kasvoldb db = new kasvoldb();

        // GET: portfolios
        public ActionResult Index()
        {
            var portfolios = db.portfolios.Include(p => p.Portfolio_Categories);
            return View(portfolios.ToList());
        }

        // GET: portfolios/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            portfolios portfolios = db.portfolios.Find(id);
            if (portfolios == null)
            {
                return NotFound();
            }
            return View(portfolios);
        }

        // GET: portfolios/Create
        public ActionResult Create()
        {
            ViewBag.portfolio_categoriesid = new SelectList(db.portfolio_Categories, "portfolio_categoriesid", "title");
            return View();
        }

        // POST: portfolios/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("portfoliosid,title,portfolio_categoriesid,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] portfolios portfolios)
        {
            if (ModelState.IsValid)
            {
                db.portfolios.Add(portfolios);
                db.SaveChanges();
                return RedirectToAction("Index");
            }

            ViewBag.portfolio_categoriesid = new SelectList(db.portfolio_Categories, "portfolio_categoriesid", "title", portfolios.portfolio_categoriesid);
            return View(portfolios);
        }

        // GET: portfolios/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            portfolios portfolios = db.portfolios.Find(id);
            if (portfolios == null)
            {
                return NotFound();
            }
            ViewBag.portfolio_categoriesid = new SelectList(db.portfolio_Categories, "portfolio_categoriesid", "title", portfolios.portfolio_categoriesid);
            return View(portfolios);
        }

        // POST: portfolios/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("portfoliosid,title,portfolio_categoriesid,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] portfolios portfolios)
        {
            if (ModelState.IsValid)
            {
                db.Entry(portfolios).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("Index");
            }
            ViewBag.portfolio_categoriesid = new SelectList(db.portfolio_Categories, "portfolio_categoriesid", "title", portfolios.portfolio_categoriesid);
            return View(portfolios);
        }

        // GET: portfolios/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            portfolios portfolios = db.portfolios.Find(id);
            if (portfolios == null)
            {
                return NotFound();
            }
            return View(portfolios);
        }

        // POST: portfolios/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            portfolios portfolios = db.portfolios.Find(id);
            db.portfolios.Remove(portfolios);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}


