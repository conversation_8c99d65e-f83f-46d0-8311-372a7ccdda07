using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace kasvol.Models
{
    public class Currency : Baseclass
    {
        public int currencyid { get; set; }
        public string currencyname { get; set; }
        public string currencycode { get; set; }
        public string Currencysymbol { get; set; }
        public double rate { get; set; }
        public bool ismanual { get; set; }
        public string flag { get; set; }
        public string countrycode { get; set; }
        public virtual ICollection<Product> Products { get; set; }

    }
}