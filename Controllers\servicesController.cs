using Microsoft.AspNetCore.Authorization;
using System;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class servicesController : Controller
    {
        private kasvoldb db = new kasvoldb();

        // GET: services
        public ActionResult Index()
        {
            var services = db.ourservices.Include(s => s.Service_Items);
            return View(services.ToList());
        }

        // GET: services/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            ourservices services = db.ourservices.Find(id);
            if (services == null)
            {
                return NotFound();
            }
            return View(services);
        }

        // GET: services/Create
        public ActionResult Create()
        {
            ViewBag.service_itemsid = db.service_Items.ToList();
            return View();
        }

        // POST: services/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("servicesid,title,content,slogan,numofvisit,auther,publish,allowcomments,allowwhatsapp,service_itemsid,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] ourservices services)
        {
            if (ModelState.IsValid)
            {
                db.ourservices.Add(services);
                db.SaveChanges();
                return RedirectToAction("Index");
            }

            ViewBag.service_itemsid = db.service_Items.ToList();
            return View(services);
        }

        // GET: services/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            ourservices services = db.ourservices.Find(id);
            if (services == null)
            {
                return NotFound();
            }
            ViewBag.service_itemsid = db.service_Items.ToList();
            return View(services);
        }

        // POST: services/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("servicesid,title,content,slogan,numofvisit,auther,publish,allowcomments,allowwhatsapp,service_itemsid,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] ourservices services)
        {
            if (ModelState.IsValid)
            {
                db.Entry(services).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("Index");
            }
            ViewBag.service_itemsid = db.service_Items.ToList();
            return View(services);
        }

        // GET: services/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            ourservices services = db.ourservices.Find(id);
            if (services == null)
            {
                return NotFound();
            }
            return View(services);
        }

        // POST: services/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            ourservices services = db.ourservices.Find(id);
            db.ourservices.Remove(services);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}


