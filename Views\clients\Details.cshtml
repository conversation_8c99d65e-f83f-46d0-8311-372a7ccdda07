@model kasvol.Models.clients

@{
    ViewBag.Title = "Details";
    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}

<h2>Details</h2>

<div>
    <h4>clients</h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
             @Html.Label(@Resources.Resource.String4)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.title)
        </dd>

        <dt>
            @Html.Label(@Resources.Resource.String18)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.content)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.link)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.link)
        </dd>

        <dt>
             @Html.Label(@Resources.Resource.String6)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.showinhome)
        </dd>

        <dt>
            @Html.Label(@Resources.Resource.String83)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.arrange)
        </dd>

        <dt>
             @Html.Label(@Resources.Resource.String330)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.datecreated)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.datemodified)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.datemodified)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.IP)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.IP)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.modifiedIP)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.modifiedIP)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.user)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.user)
        </dd>

        <dt>
             @Html.Label(@Resources.Resource.String5)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.year)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.guid)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.guid)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.lang)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.lang)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ogtitle)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ogtitle)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ogdescription)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ogdescription)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.keywords)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.keywords)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ogimage)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ogimage)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.twimage)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.twimage)
        </dd>

    </dl>
</div>
<p>
    @Html.ActionLink("Edit", "Edit", new { id = Model.clientsid }) |
    @Html.ActionLink("Back to List", "Index")
</p>
