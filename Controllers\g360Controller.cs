using System.Collections.Generic;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "tr"
                                        select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "ar"
                                        select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Net;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "en"
                                        select d).Single();
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media);
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media2);
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media1);
using kasvol.service;

namespace kasvol.Controllers
{
    [Authorize]
    public class g360Controller : BaseController
    {
        private readonly IWebHostEnvironment _webHostEnvironment;

        private kasvoldb db = new kasvoldb();

        // GET: g360
        public ActionResult Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            return View(db.g360s.Where(a => a.lang == "ar").ToList());
        }

        // GET: g360/Details/5
        public async Task<ActionResult> uploadv(int? id)
        {
            kasvolservices t = new kasvolservices(); if (id == null)
            {
                return BadRequest();
            }
            g360 page = await db.g360s.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            return View(page);
        }
        [HttpPost]
        public async Task<ActionResult> uploadv(int? g360id, IFormFile ImageFile)
        {
            kasvolservices t = new kasvolservices();
            g360 page = await db.g360s.FindAsync(g360id);

            string path = Server.MapPath("~/kasvolfactory/vi/g360/" + page.g360id + "/");


            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }


            if (ImageFile != null)
            {

                try
                {
                    string normalImagePath = System.IO.Path.Combine(path, ImageFile.FileName);
                    string webPFileName = System.IO.Path.GetFileNameWithoutExtension(ImageFile.FileName) + ".webp";

                    string webPImagePath = System.IO.Path.Combine(path, webPFileName);

                    // using System.Collections.Generic;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "tr"
                                        select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "ar"
                                        select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Net;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "en"
                                        select d).Single();
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media);
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media2);
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media1);
using kasvol.service;

}
                    string ext = System.IO.Path.GetExtension(ImageFile.FileName);
                    if (ext != ".webp")
                    {
                        var webPFileStream = new FileStream(webPImagePath, FileMode.Create);

                        using System.Collections.Generic;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "tr"
                                        select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "ar"
                                        select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Net;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "en"
                                        select d).Single();
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media);
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media2);
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media1);
using kasvol.service;

}

                    }


                    using System.Collections.Generic;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "tr"
                                        select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "ar"
                                        select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Net;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "en"
                                        select d).Single();
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media);
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media2);
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media1);
using kasvol.service;

useredit.coverimagelink = "kasvolfactory/vi/g360/" + page.g360id + "/" + webPFileName;

                        context.SaveChanges();
                    }
                    using System.Collections.Generic;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "tr"
                                        select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "ar"
                                        select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Net;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "en"
                                        select d).Single();
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media);
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media2);
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media1);
using kasvol.service;

useredit.coverimagelink = "kasvolfactory/vi/g360/" + page.g360id + "/" + webPFileName;

                        context.SaveChanges();
                    }
                    using System.Collections.Generic;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "tr"
                                        select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "ar"
                                        select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Net;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "en"
                                        select d).Single();
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media);
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media2);
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media1);
using kasvol.service;

useredit.coverimagelink = "kasvolfactory/vi/g360/" + page.g360id + "/" + webPFileName;

                        context.SaveChanges();
                    }


                }
                catch (Exception e)
                {
                    Console.WriteLine("Overflow. {0}", e.Message);
                }
            }




            return RedirectToAction("Index");
        }

        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            g360 g360 = db.g360s.Find(id);
            if (g360 == null)
            {
                return NotFound();
            }
            return View(g360);
        }

        // GET: g360/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: g360/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create( string g360Degreear, string g360Degreeen, string g360Degreetr, string titlear, string titleen, string titletr)
        {
            kasvolservices t = new kasvolservices();

            string guid = Guid.NewGuid().ToString();
      
                g360 media = new g360();

                media.arrange = 1;
                media.lang = "ar";
                media.guid = guid;

            media.title = titlear;
                media.link = g360Degreear;
                using System.Collections.Generic;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "tr"
                                        select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "ar"
                                        select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Net;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "en"
                                        select d).Single();
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media);
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media2);
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media1);
using kasvol.service;

db1.SaveChanges();
                }

    
                g360 media1 = new g360();

                media1.arrange = 2;
                media1.lang = "en";
                media1.guid = guid;

            media1.title = titleen;
            media1.link = g360Degreeen;
                using System.Collections.Generic;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "tr"
                                        select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "ar"
                                        select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Net;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "en"
                                        select d).Single();
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media);
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media2);
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media1);
using kasvol.service;

db1.SaveChanges();
                }

            
        
                g360 media2 = new g360();

                media2.arrange = 3;
                media2.lang = "tr";
                media2.guid = guid;
            media2.title = titletr;

            media2.link = g360Degreetr;
                using System.Collections.Generic;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "tr"
                                        select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "ar"
                                        select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Net;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "en"
                                        select d).Single();
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media);
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media2);
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media1);
using kasvol.service;

db1.SaveChanges();
                }

            


            return RedirectToAction("Index");
     

           
        }

        // GET: g360/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
         
            g360 g360 = db.g360s.Find(id);
            if (g360 == null)
            {
                return NotFound();
            }
            return View(g360);
        }

        // POST: g360/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(int g360id, string g360Degreear, string g360Degreeen, string g360Degreetr, string titlear, string titleen, string titletr)
        {string guid= db.g360s.Where(a => a.g360id == g360id).Select(a => a.guid).Single();
            kasvolservices t = new kasvolservices();
          
            if ( g360Degreear != "")
            {
                using System.Collections.Generic;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "tr"
                                        select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "ar"
                                        select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Net;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "en"
                                        select d).Single();
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media);
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media2);
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media1);
using kasvol.service;

useredit.link = g360Degreear;
                    useredit.title = titlear;
                    context.SaveChanges();
                }

            }
            if(g360Degreeen!="")
            {

          
                using System.Collections.Generic;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "tr"
                                        select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "ar"
                                        select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Net;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "en"
                                        select d).Single();
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media);
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media2);
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media1);
using kasvol.service;

useredit.link = g360Degreeen;
                    useredit.title = titleen;
                    context.SaveChanges();
                }

            }
             if (g360Degreetr != "")
            {
                using System.Collections.Generic;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "tr"
                                        select d).Single();
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "ar"
                                        select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "tr"
                                    select d).Single();
using System.Net;
using (var context = new kasvoldb())
                    {
                        var useredit = (from d in context.g360s
                                        where d.guid == page.guid && d.lang == "en"
                                        select d).Single();
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media);
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media2);
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "ar"
                                    select d).Single();
using (var context = new kasvoldb())
                {
                    var useredit = (from d in context.g360s
                                    where d.guid == guid && d.lang == "en"
                                    select d).Single();
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (kasvoldb db1 = new kasvoldb())
                {
                    db1.g360s.Add(media1);
using kasvol.service;

useredit.link = g360Degreetr;
                    useredit.title = titletr;

                    context.SaveChanges();
                }

            }
           

          
            return RedirectToAction("Index");
            
      
        }

        // GET: g360/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            g360 g360 = db.g360s.Find(id);
            if (g360 == null)
            {
                return NotFound();
            }
            return View(g360);
        }

        // POST: g360/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            g360 g360 = db.g360s.Find(id);
            db.g360s.Remove(g360);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}






