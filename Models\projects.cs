using kasvol.Models;
using System;
using System.ComponentModel.DataAnnotations;

namespace kasvol.Models
{
    public class Projects : Baseclass
    {
        public int projectsid { get; set; }
        public string title { get; set; }
        [DataType(DataType.MultilineText)]        public string content { get; set; }
        public string slogan { get; set; }
        public string oldslogan { get; set; }
        public string oldslogan1 { get; set; }
        public int numofvisit { get; set; } = 0;
        public double Latitude { get; set; } = 0;
        public double Longitude { get; set; } = 0;
        public string auther { get; set; }
        public bool publish { get; set; }
        public bool allowcomments { get; set; }
        public bool allowwhatsapp { get; set; }
        public int project_categoriesid { get; set; }
        public virtual project_categories  Project_Categories { get; set; }
        public DateTime startdate { get; set; } = DateTime.Now;
        public DateTime enddate { get; set; } = DateTime.Now;
        public string vediolink { get; set; }
        public string g360link { get; set; }
    }
}
