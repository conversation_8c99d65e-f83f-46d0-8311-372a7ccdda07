@model IEnumerable<kasvol.Models.Projects>

@{
    ViewBag.Title = @Resources.Resource.project;
    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}

<div class="card card-custom">
    <div class="card-header flex-wrap py-5">
        <div class="card-title">
            <h3 class="card-label">
                @Resources.Resource.project

            </h3>
        </div>
        <div class="card-toolbar">
            <!--begin::Dropdown-->
            <!--end::Dropdown-->
            <!--begin::Button-->
            <a href="@Url.Action("Create", "projects")" class="btn btn-primary font-weight-bolder">
                <span class="svg-icon svg-icon-md">
                    <!--begin::Svg Icon | path:assets/media/svg/icons/Design/Flatten.svg-->
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <rect x="0" y="0" width="24" height="24"></rect>
                            <circle fill="#000000" cx="9" cy="15" r="6"></circle>
                            <path d="M8.8012943,7.00241953 C9.83837775,5.20768121 11.7781543,4 14,4 C17.3137085,4 20,6.6862915 20,10 C20,12.2218457 18.7923188,14.1616223 16.9975805,15.1987057 C16.9991904,15.1326658 17,15.0664274 17,15 C17,10.581722 13.418278,7 9,7 C8.93357256,7 8.86733422,7.00080962 8.8012943,7.00241953 Z" fill="#000000" opacity="0.3"></path>
                        </g>
                    </svg>
                    <!--end::Svg Icon-->
                </span> @Resources.Resource.String12
            </a>
            <!--end::Button-->
        </div>

    </div>
    <div class="card-body" style="margin-bottom:125px">

        <div class="container" style="text-align:center">
            <div class="form-group m-form__group row">

                <div class="col-sm-12 mb-4">


                    <input class="form-control " id="name90a" name="name90a" placeholder="???? ?? ???????" />




                </div>
                <div class="col-sm-12 mb-4">


                    <input class="form-control " type="number" id="mobile90a" name="mobile90a" placeholder="??? ??????" />




                </div>   <div class="col-sm-12">


                    <a class="btn btn-danger" id="unpub"> ?????</a>




                </div>
            </div>

            <table class="table table-separate table-head-custom table-checkable dataTable no-footer dtr-inline" id="example">
                <thead>
                    <tr>
                        <th>
                            ID
                        </th>
                        <th>
                            @Html.Label(@Resources.Resource.String4)
                        </th>
                        <th>
                            Old
                        </th>
                        <td>
                            @Html.Label(@Resources.Resource.String83)
                        </td>
                        <th>
                            @Html.Label(@Resources.Resource.String72)
                        </th>



                        <th>
                            @Html.Label(@Resources.Resource.String87)
                        </th>

                        <th>
                            @Html.Label(@Resources.Resource.String330)
                        </th>

                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    @using kasvol.service;
                    @{kasvolservices t = new kasvolservices(); }
                    @foreach (var item in Model)
                    {
                        <tr>
                            @{


                                string value = t.projectcategory(item.project_categoriesid, ViewBag.lang);



                            }
                            <td>
                                <a href="https://kasvol.com/project/@item.slogan"> @Html.DisplayFor(modelItem => item.projectsid)</a>
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.title)
                            </td>
                            <td>
                                <div id="<EMAIL>">
                                    @Html.DisplayFor(modelItem => item.oldslogan1)
                                    <a onclick="openModal3a('@item.projectsid');"><i class="ace-icon fa fa-pencil-alt " style="color:#057928;margin-left:4px"> </i></a>
                                </div>
                                <div id="<EMAIL>" style="display:none">

                                    <input class="form-control text-box single-line" id="<EMAIL>" name="<EMAIL>" type="text" value="@item.oldslogan1"> <br />
                                    <a onclick="openModal31a('@item.projectsid');">
                                        <i class="ace-icon fa fa-check " style="color:#057928;margin-left:4px"></i>
                                    </a>
                                    <a onclick="openModal32a('@item.projectsid');"> <i class="ace-icon fa fa-window-close " style="color:#ff0000;margin-left:4px"></i></a>
                                </div>
                            </td>
                            <td>
                                <div id="<EMAIL>">
                                    @Html.DisplayFor(modelItem => item.arrange)
                                    <a onclick="openModal3('@item.projectsid');"><i class="ace-icon fa fa-pencil-alt " style="color:#057928;margin-left:4px"> </i></a>
                                </div>
                                <div id="<EMAIL>" style="display:none">

                                    <input class="form-control text-box single-line" id="<EMAIL>" name="<EMAIL>" type="text" value="@item.arrange"> <br />
                                    <a onclick="openModal31('@item.projectsid');">
                                        <i class="ace-icon fa fa-check " style="color:#057928;margin-left:4px"></i>
                                    </a>
                                    <a onclick="openModal32('@item.projectsid');"> <i class="ace-icon fa fa-window-close " style="color:#ff0000;margin-left:4px"></i></a>
                                </div>
                            </td>

                            <td>
                                @Html.DisplayFor(modelItem => item.numofvisit)
                            </td>




                            <td>
                                @value
                            </td>

                            <td>
                                @item.datecreated.ToString("yyyy-MM-dd HH:mm")
                            </td>

                            <td>
                                <a href='@Url.Action("uploadv", "projects", new { id = item.projectsid })' style="font-size:14px;"><i class="ace-icon fa fa-image" style="color:#ff00dc;margin-left:4px"></i> @Resources.Resource.String7 </a>|

                                <a href='@Url.Action("updatevideo", "projects", new { id = item.projectsid })' style="font-size:14px;"><i class="ace-icon fas fa-file-video" style="color:#ff00dc;margin-left:4px"></i> @Resources.Resource.video </a>|


                                <a href='@Url.Action("update360", "projects", new { id = item.projectsid })' style="font-size:14px;"><i class="ace-icon fas fa-external-link-alt" style="color:#ff00dc;margin-left:4px"></i> @Resources.Resource.String65 </a>|


                                <a href='@Url.Action("gallary", "projects", new { id = item.projectsid })' style="font-size:14px;"><i class="ace-icon fa fa-images" style="color:#ff00dc;margin-left:4px"></i> @Resources.Resource.String94 </a>|




                                <a href="@Url.Action("Edit", "projects", new { id = item.guid, lang = "ar" })" style="font-size:14px;"><i class="ace-icon fa fa-pencil-alt " style="color:#057928;margin-left:4px"></i> @Resources.Resource.String9   AR</a>|
                                <a href="@Url.Action("Edit", "projects", new { id = item.guid, lang = "en" })" style="font-size:14px;"><i class="ace-icon fa fa-pencil-alt " style="color:#057928;margin-left:4px"></i> @Resources.Resource.String9   EN</a>|
                                 @*<a href="@Url.Action("Details", "projects", new { id = item.projectsid })" style="font-size:14px;"><i class="ace-icon fa fa-list" style="color:#ff6a00;margin-left:4px"></i> @Resources.Resource.String10 </a>|*@


                                <a href="@Url.Action("Delete", "projects", new { id = item.projectsid })" style="font-size:14px;"><i class="ace-icon fa fa-trash" style="color:#ff0000;margin-left:4px"></i> @Resources.Resource.String11 </a>

                            </td>
                        </tr>


                    }
                </tbody>
            </table>
        </div>

    </div><
    <link href="/assets/plugins/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
    <link href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css" rel="stylesheet" type="text/css" />
    @section Scripts {


        <script src="//cdn.datatables.net/1.10.22/js/jquery.dataTables.min.js" type="text/javascript"></script>
        <script>
        $(document).ready(function () {

            $('#example').dataTable({
                "order": [[0, "desc"]],
                "scrollX": true, "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.20/i18n/@ViewBag.ar"}

            });
        });

        </script>
        <script type="text/javascript">
        function openModal3(id) {

            $('#d_' + id).show();
            $('#c_' + id).hide();
        }
        function openModal31(id) {
            var kk = $("#alt_" + id).val();
             $.ajax({
                        type: 'POST',
                        url: '@Url.Action("editalt")',
                        traditional: true,
                        dataType: 'json',

                        data: { id: id,alt:kk },


                        success: function (states) {

                            $('#c_' + id).text(kk);
                                toastr.success('Done')

                            $('#d_' + id).hide();
                            $('#c_' + id).show();

                        },
                        error: function (ex) {
                            alert('-' + ex);
                        }
                    });
                    return false;


        }
        function openModal32(id) {

            $('#d_' + id).hide();
            $('#c_' + id).show();
        }
           function openModal3a(id) {

            $('#d1_' + id).show();
            $('#c1_' + id).hide();
        }
        function openModal31a(id) {
            var kk = $("#alt1_" + id).val();
             $.ajax({
                        type: 'POST',
                        url: '@Url.Action("editalt22")',
                        traditional: true,
                        dataType: 'json',

                        data: { id: id,alt:kk },


                        success: function (states) {

                            $('#c1_' + id).text(kk);
                                toastr.success('Done')

                            $('#d1_' + id).hide();
                            $('#c1_' + id).show();

                        },
                        error: function (ex) {
                            alert('-' + ex);
                        }
                    });
                    return false;


        }
        function openModal32a(id) {

            $('#d1_' + id).hide();
            $('#c1_' + id).show();
        }
        </script>
        <script type="text/javascript">
            $(document).ready(function () {
                $("#unpub").click(function () {


                    var name90a = document.getElementById("name90a").value;
                    var mobile90a = document.getElementById("mobile90a").value;
                    if (name90a != "" && mobile90a == "") {
                        location.href = '/projects/Index?word=' + name90a;

                    } else if (name90a == "" && mobile90a != "") {
                        location.href = '/projects/Index?take=' + mobile90a;

                    }
                    else if (name90a != "" && mobile90a != "") {
                        location.href = '/projects/Index?word=' + name90a + '&take=' + mobile90a;
                    } else {

                    }
                });
            });</script>
    }
