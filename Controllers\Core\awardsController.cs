using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using kasvol.service;

namespace kasvol.Controllers.Core
{
    [Authorize]
    public class awardsController : BaseController
    {
        private readonly kasvoldb _db;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<awardsController> _logger;

        public awardsController(kasvoldb db, IConfiguration configuration, kasvolservices kasvolServices,
            UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor,
            ILogger<awardsController> logger)
            : base(db, configuration, kasvolServices)
        {
            _db = db;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
        }

        // GET: awards
        public async Task<IActionResult> Index()
        {
            try
            {
                // Get language from cookie
                string currentLanguage = Request.Cookies["CurrentLanguage"] ?? "en-En";
                ViewBag.ar = currentLanguage switch
                {
                    "ar-AE" => "Arabic.json",
                    "en-En" => "English.json",
                    "tr-tr" => "Turkish.json",
                    _ => "English.json"
                };

                var awards = await _db.awards
                    .OrderByDescending(a => a.datemodified ?? a.date)
                    .ToListAsync();
                return View(awards);
            }
            catch (Exception ex)
            {
                // Log the error
                _logger.LogError(ex, "Error occurred while fetching awards");
                return StatusCode(500, "An error occurred while processing your request.");
            }
        }

        // GET: awards/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            var award = await _db.awards.FindAsync(id);
            if (award == null)
            {
                return NotFound();
            }
            
            return View(award);
        }

        // GET: awards/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: awards/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(award award)
        {
            try
            {
                if (award == null)
                {
                    return BadRequest("Invalid award data");
                }

                award.IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                award.user = _userManager.GetUserId(User);
                award.date = DateTime.Now;
                
                if (ModelState.IsValid)
                {
                    await _db.awards.AddAsync(award);
                    await _db.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Award created successfully";
                    return RedirectToAction("Index");
                }
                
                return View(award);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating award");
                ModelState.AddModelError("", "An error occurred while saving the award.");
                return View(award);
            }
        }

        // GET: awards/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            var award = await _db.awards.FindAsync(id);
            if (award == null)
            {
                return NotFound();
            }
            
            return View(award);
        }

        // POST: awards/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(award award)
        {
            try
            {
                if (award == null)
                {
                    return BadRequest("Invalid award data");
                }

                award.modifiedIP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                award.datemodified = DateTime.Now;
                
                if (ModelState.IsValid)
                {
                    var existingAward = await _db.awards.FindAsync(award.id);
                    if (existingAward == null)
                    {
                        return NotFound();
                    }

                    _db.Entry(existingAward).CurrentValues.SetValues(award);
                    await _db.SaveChangesAsync();
                    TempData["SuccessMessage"] = "Award updated successfully";
                    return RedirectToAction("Index");
                }
                
                return View(award);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while updating award");
                ModelState.AddModelError("", "An error occurred while updating the award.");
                return View(award);
            }
        }

        // GET: awards/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            var award = await _db.awards.FindAsync(id);
            if (award == null)
            {
                return NotFound();
            }
            
            return View(award);
        }

        // POST: awards/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var award = await _db.awards.FindAsync(id);
                if (award == null)
                {
                    return NotFound();
                }

                _db.awards.Remove(award);
                await _db.SaveChangesAsync();
                TempData["SuccessMessage"] = "Award deleted successfully";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while deleting award");
                TempData["ErrorMessage"] = "An error occurred while deleting the award.";
                return RedirectToAction("Index");
            }
        }
    }
}
