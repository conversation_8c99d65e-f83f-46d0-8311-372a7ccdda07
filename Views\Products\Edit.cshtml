@model kasvol.Models.Product

@{
    ViewBag.Title = "Edit";
    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}

@using kasvol.service;
@{
    kasvolservices t = new kasvolservices();

    string value1 = t.getvalue("en", Model.Category.CategoryName);


}
<div id="progress" class="modal1">
    <div class="throbber">
        <div class="curtain">
        </div>
        <div class="curtain-content">
            <div>

                <div id="preloader">
                    <div id="loader"></div>
                </div>
            </div>
        </div>
    </div>
</div><div class="card card-custom">
    <div class="card-header flex-wrap py-5">
        <div class="card-title">
            <h3 class="card-label">
                @Resources.Resource.String173  <span style="text-align: center; color: #ff0000">@Resources.Resource.String14  @ViewBag.lang</span>

            </h3>
        </div>


    </div>
    <div class="card-body" style="margin-bottom:125px">
        <h2 class="m--font-danger m--align-center"></h2>
        <div class="row">
            <div class="col-md-12">

                @using (Ajax.BeginForm("Edit", "Products",
                                               new AjaxOptions
                                               {
                                                   OnSuccess = "OnSuccess",
                                                   OnFailure = "OnFailure",
                                                   OnBegin = "onLoginBegin",
                                                   LoadingElementId = "progress"
                                               }))
                {@Html.AntiForgeryToken()


                <div class="form-horizontal">
                    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                    @Html.HiddenFor(model => model.ProductID)


                    <div class="form-group m-form__group row">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String145 </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.ProductName, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.ProductName, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    @if (ViewBag.lang == "ar")
                    {
                        <div class="form-group m-form__group row">
                            <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String16 </label>
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.slogan, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.slogan, "", new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="form-group m-form__group row ">
                            <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String159 </label>
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.Productnumber, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.Productnumber, "", new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="form-group m-form__group row">

                            <div class="col-sm-6" style="text-align:start">
                                <label class="col-form-label m--align-right" for="form-field-1">  @Resources.Resource.String140 </label>
                                @Html.EditorFor(model => model.UnitPrice, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.UnitPrice, "", new { @class = "text-danger" })
                            </div>
                            <div class="col-sm-6" style="text-align:start">
                                <label class="col-form-label m--align-right" for="form-field-1">  @Resources.Resource.String160 </label>
                                @Html.EditorFor(model => model.countofcontainer, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.countofcontainer, "", new { @class = "text-danger" })
                            </div>

                        </div>
                        <div class="form-group m-form__group row">

                            <div class="col-sm-3" style="text-align:start">
                                <label class="col-form-label m--align-right" for="form-field-1">  @Resources.Resource.String161 </label>
                                @Html.EditorFor(model => model.weight, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.weight, "", new { @class = "text-danger" })
                            </div>
                            <div class="col-sm-3" style="text-align:start">
                                <label class="col-form-label m--align-right" for="form-field-1">  @Resources.Resource.String162 </label>
                                @Html.EditorFor(model => model.length, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.length, "", new { @class = "text-danger" })
                            </div>
                            <div class="col-sm-3" style="text-align:start">
                                <label class="col-form-label m--align-right" for="form-field-1">  @Resources.Resource.String163 </label>
                                @Html.EditorFor(model => model.width, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.width, "", new { @class = "text-danger" })
                            </div>
                            <div class="col-sm-3" style="text-align:start">
                                <label class="col-form-label m--align-right" for="form-field-1">  @Resources.Resource.String164 </label>
                                @Html.EditorFor(model => model.height, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.height, "", new { @class = "text-danger" })
                            </div>
                            <div class="col-sm-6 d-none" style="text-align:start">
                                <label class="col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String141 </label>
                                @Html.EditorFor(model => model.noofUnit, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.noofUnit, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group m-form__group row d-none">
                            <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String91 </label>
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.auther, new { htmlAttributes = new { @class = "form-control", @Value = "kasvol" } })
                                @Html.ValidationMessageFor(model => model.auther, "", new { @class = "text-danger" })
                            </div>
                        </div>
                    }
                    <div class="form-group m-form__group row">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String136 </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.shortDescription, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.shortDescription, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group m-form__group row">
                        <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String137 </label>
                        <div class="col-sm-12">
                            @Html.EditorFor(model => model.Description, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.Description, "", new { @class = "text-danger" })
                        </div>
                    </div>




                    @if (ViewBag.lang == "ar")
                    {


                        <div class="form-group m-form__group row">
                            <div class="col-md-12 " style="text-align:center">
                                <div class="checkbox checkbox-success">
                                    <label style="margin:0 auto">
                                        @Html.EditorFor(model => model.ispublish)
                                        @Html.ValidationMessageFor(model => model.ispublish, "", new { @class = "text-danger" })
                                        <span class="lbl">@Resources.Resource.String88</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group m-form__group row">
                            <label class="col-md-1 col-sm-12 col-form-label m--align-center" for="form-field-1">  @Resources.Resource.String134</label>


                            <div class="col-sm-12">

                                <select class="form-control" name="CategoryID" required>
                                    <option selected value="@Model.CategoryID">@value1 </option>


                                    @foreach (var item in ViewBag.product_Categories)
                                    {


                                        string value = t.getvalue(ViewBag.lang, item.CategoryName);



                                        <option value="@item.CategoryID">@value</option>
                                    }
                                </select>
                            </div>
                        </div>
                    }
                        <div class="form-group m-form__group row m-form__group row d-none">
                            @Html.LabelFor(model => model.arrange, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.arrange, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.arrange, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group d-none">
                            @Html.LabelFor(model => model.datecreated, htmlAttributes: new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.datecreated, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.datecreated, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group m-form__group row m-form__group row d-none">
                            @Html.LabelFor(model => model.datemodified, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.datemodified, new { htmlAttributes = new { @class = "form-control", @Value = System.DateTime.Now.ToString("yyyy-MM-dd hh:mm") } })
                                @Html.ValidationMessageFor(model => model.datemodified, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group d-none">
                            @Html.LabelFor(model => model.IP, htmlAttributes: new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.IP, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.IP, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group d-none">
                            @Html.LabelFor(model => model.modifiedIP, htmlAttributes: new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.modifiedIP, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.modifiedIP, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group d-none">
                            @Html.LabelFor(model => model.user, htmlAttributes: new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.user, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.user, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group d-none">
                            @Html.LabelFor(model => model.year, htmlAttributes: new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.year, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.year, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group d-none">
                            @Html.LabelFor(model => model.guid, htmlAttributes: new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.guid, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.guid, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group" style="display:none">
                            @Html.LabelFor(model => model.lang, htmlAttributes: new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.lang, new { htmlAttributes = new { @class = "form-control", @Value = ViewBag.lang } })
                                @Html.ValidationMessageFor(model => model.lang, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group m-form__group row m-form__group row">
                            @Html.LabelFor(model => model.ogtitle, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.ogtitle, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.ogtitle, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group m-form__group row m-form__group row">
                            @Html.LabelFor(model => model.ogdescription, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.ogdescription, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.ogdescription, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group m-form__group row m-form__group row">
                            @Html.LabelFor(model => model.keywords, htmlAttributes: new { @class = "col-md-12 col-sm-12 col-form-label m--align-center" })
                            <div class="col-sm-12">
                                @Html.EditorFor(model => model.keywords, new { htmlAttributes = new { @class = "form-control" } })
                                @Html.ValidationMessageFor(model => model.keywords, "", new { @class = "text-danger" })
                            </div>
                        </div>



                        <div class="form-group m-form__group row m-form__group">
                            <div class=" col-md-12" style="text-align:center">
                                <input type="submit" value="@Resources.Resource.String9" class="btn btn-info" />
                            </div>
                        </div>
                    </div>
            }



                <div class=" col-md-12" style="text-align:center">
                    @Html.ActionLink("Back to List", "Index")
                </div>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" type="text/css" href="~/smart/css/smart-forms.css">
<link rel="stylesheet" type="text/css" href="~/smart/css/smart-addons.css">


@section Scripts {
  
        <script type="text/javascript">
            $('#ProductName').keyup(function () {
                document.getElementById("ogtitle").value = document.getElementById("ProductName").value;


            });

        </script>

    
    <script src="/dashboard/assets/js/pages/crud/forms/widgets/tagify.js"></script>
    <script type="text/javascript">
        var input = document.querySelector('input[name=keywords]');

        // initialize Tagify on the above input node reference
        new Tagify(input)
    </script>
    <script type="text/javascript">
        function OnSuccess() {
            location.href = '/ProductName/Index';
        }
        function OnFailure() {
            alert("Programmer will know this error ");
            $('#progress').hide();
        }

    </script>
    <script type="text/javascript">

        jQuery(document).ready(function ($) {

            $('#form0').validate();


        });

    </script>
    <script type="text/javascript">
        $("#form0").on("submit", function (event) {



            if ($('#form0').valid()) {
                $('#progress').show();
            }
        });

    </script>

    <script type="text/javascript" src="~/smart/js/jquery.validate.min.js"></script>
    <script type="text/javascript" src="~/smart/js/additional-methods.min.js"></script>
    <script src="/dashboard/assets/plugins/custom/tinymce/tinymce.bundle.js"></script>
    <!--end::Page Vendors-->
    <!--begin::Page Scripts(used by this page)-->
    @if (ViewBag.lang == "ar")
    {


        <script src="/dashboard/assets/js/pages/crud/forms/editors/tinymce.js"></script>
    }
    else
    {
        <script src="/dashboard/assets/js/pages/crud/forms/editors/tinymceen.js"></script>
    }
}
