using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.galleries
                                 where d.galleriesid == dd
                                 select d).SingleOrDefault();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

namespace kasvol.Controllers
{
    [Authorize]
    public class galleriesController : BaseController
    {
        private readonly IWebHostEnvironment _webHostEnvironment;

        private readonly KasvolDbContext _db;

        public galleriesController(KasvolDbContext context, IWebHostEnvironment webHostEnvironment)
        {
            _db = context;
            _webHostEnvironment = webHostEnvironment;
        }
        [HttpPost]
        public JsonResult editalt(string id, string alt)
        {
            int dd = Convert.ToInt32(id);
            using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.galleries
                                 where d.galleriesid == dd
                                 select d).SingleOrDefault();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

agentedit.alt = alt;
                context.SaveChanges();
            }


            dynamic showMessageString = string.Empty;
            showMessageString = new
            {
                param1 = 200,
                param2 = "Done !!!"
            };
            return Json(showMessageString, JsonRequestBehavior.AllowGet);





        }
        // GET: galleries
        public ActionResult Index(string id,string id2)
        {
            ViewBag.id = id;
            ViewBag.id2 = id2;
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            if (id2 != null) { return View(_db.galleries.Where(a => a.year == id&&a.lang== "product").ToList()); }
            else
            {
                return View(_db.galleries.Where(a => a.year == id && a.lang == "project").ToList());
            }
        }

        // GET: galleries/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            galleries galleries = _db.galleries.Find(id);
            if (galleries == null)
            {
                return NotFound();
            }
            return View(galleries);
        }

        // GET: galleries/Create
        public ActionResult Create()
        {
            return View();
        }
        [HttpPost]
        public async Task<ActionResult> upload(  IFormFile[] postedFile, int? ProductID=0,int? projectsid = 0)
        {
            if (projectsid != 0)
            {
                Projects page = await _db.Projects.FindAsync(projectsid);

                string path = Server.MapPath("~/kasvolfactory/vi/project/" + projectsid + "/gallary/");



                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }


                if (postedFile != null)
                {
                    foreach (IFormFile file in postedFile)
                    {

                        if (file != null)
                        {
                            string normalImagePath = System.IO.Path.Combine(path, file.FileName);
                            string webPFileName = System.IO.Path.GetFileNameWithoutExtension(file.FileName) + ".webp";
                            string webPImagePath = System.IO.Path.Combine(path, webPFileName);
                            // using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.galleries
                                 where d.galleriesid == dd
                                 select d).SingleOrDefault();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                            string ext = System.IO.Path.GetExtension(file.FileName);
                            if (ext != ".webp")
                            {
                                var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                                using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.galleries
                                 where d.galleriesid == dd
                                 select d).SingleOrDefault();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                            }
                            string fileName = Path.GetFileName(file.FileName);
                            // using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.galleries
                                 where d.galleriesid == dd
                                 select d).SingleOrDefault();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                            var usersfile1 = new galleries();
                            usersfile1.lang = "project";
                            usersfile1.link = "kasvolfactory/vi/project/" + projectsid + "/gallary/" + webPFileName;
                            usersfile1.guid = page.guid;
                            usersfile1.alt = System.IO.Path.GetFileNameWithoutExtension(file.FileName);
                            usersfile1.year = page.projectsid.ToString();
                            _db.galleries.Add(usersfile1);
                            _db.SaveChanges();


                        }
                    }



                }

                return RedirectToAction("Index", new { id = projectsid,id2=1 });
            }
            else
            {
                Product page = await _db.products.FindAsync(ProductID);

                string path = Server.MapPath("~/kasvolfactory/vi/product/" + ProductID + "/gallary/");



                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }


                if (postedFile != null)
                {
                    foreach (IFormFile file in postedFile)
                    {

                        if (file != null)
                        {
                            string normalImagePath = System.IO.Path.Combine(path, file.FileName);
                            string webPFileName = System.IO.Path.GetFileNameWithoutExtension(file.FileName) + ".webp";
                            string webPImagePath = System.IO.Path.Combine(path, webPFileName);
                            // using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.galleries
                                 where d.galleriesid == dd
                                 select d).SingleOrDefault();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                            string ext = System.IO.Path.GetExtension(file.FileName);
                            if (ext != ".webp")
                            {
                                var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                                using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.galleries
                                 where d.galleriesid == dd
                                 select d).SingleOrDefault();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                            }
                            string fileName = Path.GetFileName(file.FileName);
                            // using System.Collections.Generic;
using System.Linq;
using System.IO;
using (var context = new kasvoldb())
            {

                var agentedit = (from d in context.galleries
                                 where d.galleriesid == dd
                                 select d).SingleOrDefault();
using System.Net;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });

}
                            var usersfile1 = new galleries();
                            usersfile1.lang = "product";
                            usersfile1.link = "kasvolfactory/vi/product/" + ProductID + "/gallary/" + webPFileName;
                            usersfile1.guid = page.guid;
                            usersfile1.alt = System.IO.Path.GetFileNameWithoutExtension(file.FileName);
                            usersfile1.year = page.ProductID.ToString();
                            _db.galleries.Add(usersfile1);
                            _db.SaveChanges();


                        }
                    }



                }

                return RedirectToAction("Index", new { id = projectsid, id2 = 1 });


            }
        }
        // POST: galleries/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("galleriesid,title,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] galleries galleries)
        {
            if (ModelState.IsValid)
            {
                _db.galleries.Add(galleries);
                _db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(galleries);
        }

        // GET: galleries/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            galleries galleries = _db.galleries.Find(id);
            if (galleries == null)
            {
                return NotFound();
            }
            return View(galleries);
        }

        // POST: galleries/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("galleriesid,title,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] galleries galleries)
        {
            if (ModelState.IsValid)
            {
                _db.Entry(galleries).State = EntityState.Modified;
                _db.SaveChanges();
                return RedirectToAction("Index");
            }
            return View(galleries);
        }

        // GET: galleries/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            galleries galleries = _db.galleries.Find(id);
            if (galleries == null)
            {
                return NotFound();
            }
            return View(galleries);
        }

        // POST: galleries/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            galleries galleries = _db.galleries.Find(id);
            _db.galleries.Remove(galleries);
            _db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}






