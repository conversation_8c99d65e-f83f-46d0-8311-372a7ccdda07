using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using Microsoft.Extensions.Configuration;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using SixLabors.ImageSharp.Processing;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json.Linq;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using kasvol.Models;
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

namespace kasvol.Controllers
{
    [Authorize]
    public class postsController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;

                private readonly IWebHostEnvironment _webHostEnvironment;
public postsController(KasvolDbContext db, IConfiguration configuration, kasvolservices kasvolServices, IHttpContextAccessor httpContextAccessor, IWebHostEnvironment webHostEnvironment, UserManager<ApplicationUser> userManager) : base(db, configuration, kasvolServices, httpContextAccessor)
        {
            _userManager = userManager;
            _webHostEnvironment = webHostEnvironment;
        }
        [HttpPost]
        public JsonResult editalt(int id, int alt)
        {
            posts page = _db.Posts.Find(id);
            int dd = Convert.ToInt32(alt);

            var agenteditAr = (from d in _db.Posts
                             where d.guid == page.guid && d.lang == "ar"
                             select d).SingleOrDefault();
            if (agenteditAr != null) agenteditAr.arrange = dd;

            var agenteditEn = (from d in _db.Posts
                             where d.guid == page.guid && d.lang == "en"
                             select d).SingleOrDefault();
            if (agenteditEn != null) agenteditEn.arrange = dd;

            var agenteditTr = (from d in _db.Posts
                             where d.guid == page.guid && d.lang == "tr"
                             select d).SingleOrDefault();
            if (agenteditTr != null) agenteditTr.arrange = dd;

            _db.SaveChanges();

            dynamic showMessageString = string.Empty;
            showMessageString = new
            {
                param1 = 200,
                param2 = "Done !!!"
            };
            return Json(showMessageString);
        }

        [HttpPost]
        public JsonResult editalt22(int id, string alt)
        {
            posts page = _db.Posts.Find(id);

            var agenteditAr = (from d in _db.Posts
                             where d.guid == page.guid && d.lang == "ar"
                             select d).SingleOrDefault();
            if (agenteditAr != null) agenteditAr.oldslogan1 = alt;

            var agenteditEn = (from d in _db.Posts
                             where d.guid == page.guid && d.lang == "en"
                             select d).SingleOrDefault();
            if (agenteditEn != null) agenteditEn.oldslogan1 = alt;

            var agenteditTr = (from d in _db.Posts
                             where d.guid == page.guid && d.lang == "tr"
                             select d).SingleOrDefault();
            if (agenteditTr != null) agenteditTr.oldslogan1 = alt;

            _db.SaveChanges();

            dynamic showMessageString = string.Empty;
            showMessageString = new
            {
                param1 = 200,
                param2 = "Done !!!"
            };
            return Json(showMessageString);





        }
        // GET: posts
        public ActionResult Index(string word,int? take)
        {
            int kk = 12;
            if (take != null)
            {
                kk = take ?? 12;
            }
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            var posts = _db.Posts.Include(p => p.Postcat);
            if (word != null && take == null) { return View(posts.Where(a => a.title.Contains(word)&& a.lang == "ar").OrderByDescending(a => a.postsid).Take(kk).ToList()); }
            else if (word == null && take != null) { return View(posts.Where(a => a.lang == "ar").OrderByDescending(a => a.postsid).Take(kk).ToList()); }
            else if (word != null && take != null)
            { return View(posts.Where(a => a.title.Contains(word)&& a.lang == "ar").OrderByDescending(a => a.postsid).Take(kk).ToList()); }
            else if (take == 0)
            {
                return View(posts.Where(a => a.lang == "ar").OrderByDescending(a => a.postsid).ToList());
            }
            else
            {
                return View(posts.Where(a => a.lang == "ar").OrderByDescending(a => a.postsid).Take(kk).ToList());
            }
        }
        public async Task<ActionResult> uploadv(int? id)
        {
            kasvolservices t = new kasvolservices(); if (id == null)
            {
                return BadRequest();
            }
            posts page = await _db.Posts.FindAsync(id);
            if (page == null)
            {
                return NotFound();
            }

            return View(page);
        }
        [HttpPost]
        public async Task<ActionResult> uploadv(int? id, IFormFile ImageFile, IFormFile ImageFile1)
        {
            try
            {

           
            kasvolservices t = new kasvolservices();
            posts page = await _db.Posts.FindAsync(id);

            string path = Server.MapPath("~/kasvolfactory/vi/blog/" + page.postsid + "/");


            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }


            if (ImageFile != null)
            {
                Size size = new Size(573, 521);
                try
                {
                    string normalImagePath = System.IO.Path.Combine(path, ImageFile.FileName);
                    string webPFileName = System.IO.Path.GetFileNameWithoutExtension(ImageFile.FileName) + ".webp";
                    string webPFileName1 = System.IO.Path.GetFileNameWithoutExtension(ImageFile.FileName) + "1.webp";
                    string webPImagePath = System.IO.Path.Combine(path, webPFileName);
                    string webPImagePath1 = System.IO.Path.Combine(path, webPFileName1);
                    // using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using Microsoft.Extensions.Configuration;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using SixLabors.ImageSharp.Processing;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json.Linq;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using kasvol.Models;
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                    string ext = System.IO.Path.GetExtension(ImageFile.FileName);
                    if (ext != ".webp")
                    {
                        var webPFileStream = new FileStream(webPImagePath, FileMode.Create);
                        var webPFileStream1 = new FileStream(webPImagePath1, FileMode.Create);
                        using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using Microsoft.Extensions.Configuration;
using System.Net;
using Microsoft.AspNetCore.Authorization;
using (var image = Image.Load(normalImagePath)) { image.SaveAsWebp(webPFileStream, new WebpEncoder { Quality = 80 });
using SixLabors.ImageSharp.Processing;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using SixLabors.ImageSharp;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json.Linq;
using Microsoft.AspNetCore.Mvc;
using (var stream = new FileStream(filePath, FileMode.Create)) { file.CopyTo(stream);
using kasvol.Models;
using System.Data;
using SixLabors.ImageSharp.Formats.Webp;
using kasvol.service;

}
                        imageFactory.Load(normalImagePath)
                           .Resize(size)
                                   .Format(new WebPFormat())
                                   .Quality(80)
                                   .Save(webPFileStream1);
                    }

                    string yy = t.getimage(page.guid);
                    if (yy != null)
                    {
                        var useredit1 = (from d in _db.Medias
                                        where d.guid == page.guid&&d.lang=="1"
                                        select d).Single();
                        useredit1.name = "kasvolfactory/vi/blog/" + id + "/" + webPFileName;
                        useredit1.filename = webPFileName;
                        _db.SaveChanges();
                        
                        var useredit2 = (from d in _db.Medias
                                        where d.guid == page.guid && d.lang == "2"
                                        select d).Single();
                        useredit2.name = "kasvolfactory/vi/blog/" + id + "/" + webPFileName1;
                        useredit2.filename = webPFileName1;
                        _db.SaveChanges();

                    }
                    else
                    {
                        media media = new media();

                        media.arrange = 1;
                        media.lang = "1";
                        media.guid = page.guid;

                        media.type = "blog";
                        media.name = "kasvolfactory/vi/blog/" + id + "/" + webPFileName;
                        media.filename = webPFileName;
                        _db.Medias.Add(media);
                        _db.SaveChanges();
                        media media1 = new media();

                        media1.arrange = 2;
                        media1.lang = "2";
                        media1.guid = page.guid;

                        media1.type = "blog";
                        media1.name = "kasvolfactory/vi/blog/" + id + "/" + webPFileName1;
                        media1.filename = webPFileName1;
                        _db.Medias.Add(media1);
                        _db.SaveChanges();
                    }
                }
                catch (Exception ex)
                {
                    Response.StatusCode = 500;
                    return Json(new { success = false, message = "??? ??? ????? ?????: " + ex.Message });
                }
            }




                return Json(new { success = true, message = "?? ????? ?????" });
            }
            catch (Exception ex)
            {
                Response.StatusCode = 500;
                return Json(new { success = false, message = "??? ??? ????? ?????: " + ex.Message });
            }
        }
        // GET: posts/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            posts posts = _db.Posts.Find(id);
            if (posts == null)
            {
                return NotFound();
            }
            return View(posts);
        }

        // GET: posts/Create
        public ActionResult Create()
        {
            ViewBag.postcatid = _db.Postcats.ToList();
            return View();
        }

        // POST: posts/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create( posts posts)
        {
            var errors = ModelState.Select(x => x.Value.Errors)
                           .Where(y => y.Count > 0)
                           .ToList();
            kasvolservices t = new kasvolservices();
            posts.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
            posts.user = _userManager.GetUserId(User);
            posts.slogan = t.FriendlyURLTitle(posts.slogan);
            if (ModelState.IsValid)
            {
                _db.Posts.Add(posts);
                _db.SaveChanges();
                if (posts.keywords != null)
                {
                    if (IsValidJson(posts.keywords))
                    {
                        int i = 0;
                        var jArray = JArray.Parse(posts.keywords);
                        foreach (var jObj in jArray)
                        {
                            string tt = jObj.Value<string>("value");
                            if (!_db.Jobs.Where(a => a.title == tt).Any())
                            {
                                i++;
                                string guid = Guid.NewGuid().ToString();

                                jobs media = new jobs();

                                media.arrange = i;
                                media.lang = "ar";
                                media.guid = guid;
                                media.title = t.FriendlyURLTitle(tt);


                                _db.Jobs.Add(media);
                                _db.SaveChanges();
                            }
                        }
                    }
                    else
                    {
                        int i = 0;
                        string tt = posts.keywords;
                        if (!_db.Jobs.Where(a => a.title == tt).Any())
                        {
                            i++;
                            string guid = Guid.NewGuid().ToString();

                            jobs media = new jobs();

                            media.arrange = i;
                            media.lang = "ar";
                            media.guid = guid;
                            media.title = t.FriendlyURLTitle(tt);


                            _db.Jobs.Add(media);
                            _db.SaveChanges();
                        }
                    }
                }
                return RedirectToAction("Index");
            }

            ViewBag.postcatid = _db.Postcats.ToList();
            return View(posts);
        }
        bool IsValidJson(string strInput)
        {
            strInput = strInput.Trim();
            if ((strInput.StartsWith("{") && strInput.EndsWith("}")) || //For object
                (strInput.StartsWith("[") && strInput.EndsWith("]"))) //For array
            {
                try
                {
                    var obj = JToken.Parse(strInput);
                    return true;
                }
                catch
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        // GET: posts/Edit/5
        public ActionResult Edit(string id, string lang)
        {
            if (lang != null)
            {
                ViewBag.lang = lang;
            }
            if (id == null)
            {
                return BadRequest();
            }
            bool here = _db.Posts.Where(a => a.guid == id&&a.lang==lang).Any();
            int idd = 0;
            if (here) {
                idd = _db.Posts.Where(a => a.guid == id && a.lang == lang).Select(a => a.postsid).Single();
            } else
            {
                idd = _db.Posts.Where(a => a.guid == id && a.lang == "ar").Select(a => a.postsid).Single();
            }
            posts posts = _db.Posts.Find(idd);
            if (posts == null)
            {
                return NotFound();
            }
            ViewBag.postcatid = _db.Postcats.ToList();
            return View(posts);
        }

        // POST: posts/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit( posts posts)
        {
            kasvolservices t = new kasvolservices();
            bool here = _db.Posts.Where(a => a.guid == posts.guid && a.lang ==posts.lang).Any();
            posts.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();
            if (posts.lang == "tr" || posts.lang == "en")
            {
                posts.allowwhatsapp = true;
                posts.allowcomments = true;
                posts.allowwhatsapp = true;
                posts.publish = true;
                posts.auther = "kasvol";


            }
            if (ModelState.IsValid)
            {
                if (here)
                {

                        var users = (from d in _db.Posts
                                     where d.guid == posts.guid && d.lang == posts.lang
                                     select d).Single();

                        users.title = posts.title;
                        users.ogtitle = posts.ogtitle;
                        users.ogdescription = posts.ogdescription;
                        users.keywords = posts.keywords;
                        users.content = posts.content;
                        users.allowwhatsapp = posts.allowwhatsapp;
                        users.allowcomments = posts.allowcomments;
                        users.allowwhatsapp = posts.allowwhatsapp;
                        users.auther = posts.auther;

                        users.publish = posts.publish;
                        users.datemodified = DateTime.Now;
                        users.modifiedIP = posts.modifiedIP;
                        _db.SaveChanges();
                        if (posts.keywords != null)
                        {
                            if (IsValidJson(posts.keywords))
                            {
                                int i = 0;
                                var jArray = JArray.Parse(posts.keywords);
                                foreach (var jObj in jArray)
                                {
                                    string tt = jObj.Value<string>("value");
                                    if (!_db.Jobs.Where(a => a.title == tt).Any())
                                    {
                                        i++;
                                        string guid = Guid.NewGuid().ToString();

                                        jobs media = new jobs();

                                        media.arrange = i;
                                        media.lang = posts.lang;
                                        media.guid = guid;
                                        media.title = t.FriendlyURLTitle(tt);


                                        _db.Jobs.Add(media);
                                        _db.SaveChanges();
                                    }
                                }
                            }
                            else
                            {
                                int i = 0;
                                string tt = posts.keywords;
                                if (!_db.Jobs.Where(a => a.title == tt).Any())
                                {
                                    i++;
                                    string guid = Guid.NewGuid().ToString();

                                    jobs media = new jobs();

                                    media.arrange = i;
                                    media.lang = posts.lang;
                                    media.guid = guid;
                                    media.title = t.FriendlyURLTitle(tt);


                                    _db.Jobs.Add(media);
                                    _db.SaveChanges();
                                }
                            }
                        }
                    }
                }
                else
                {
                    posts.slogan = _db.Posts.Where(a => a.guid == posts.guid && a.lang == "ar").Select(a => a.slogan).Single();
                    posts.oldslogan = _db.Posts.Where(a => a.guid == posts.guid && a.lang == "ar").Select(a => a.oldslogan).Single();
                    posts.auther = _db.Posts.Where(a => a.guid == posts.guid && a.lang == "ar").Select(a => a.auther).Single();
                    posts.allowwhatsapp = _db.Posts.Where(a => a.guid == posts.guid && a.lang == "ar").Select(a => a.allowwhatsapp).Single();
                    posts.postcatid = _db.Posts.Where(a => a.guid == posts.guid && a.lang == "ar").Select(a => a.postcatid).Single();
                    posts.allowcomments = _db.Posts.Where(a => a.guid == posts.guid && a.lang == "ar").Select(a => a.allowcomments).Single();
                    posts.publish = _db.Posts.Where(a => a.guid == posts.guid && a.lang == "ar").Select(a => a.publish).Single();
                    posts.IP = HttpContext.Connection.RemoteIpAddress?.ToString();
                    posts.user = _userManager.GetUserId(User);
                    _db.Posts.Add(posts);
                    _db.SaveChanges();
                    if (posts.keywords != null)
                    {
                        if (IsValidJson(posts.keywords))
                        {
                            int i = 0;
                            var jArray = JArray.Parse(posts.keywords);
                            foreach (var jObj in jArray)
                            {
                                string tt = jObj.Value<string>("value");
                                if (!_db.Jobs.Where(a => a.title == tt).Any())
                                {
                                    i++;
                                    string guid = Guid.NewGuid().ToString();

                                    jobs media = new jobs();

                                    media.arrange = i;
                                    media.lang = "ar";
                                    media.guid = guid;
                                    media.title = tt;


                                    _db.Jobs.Add(media);
                                    _db.SaveChanges();
                                }
                            }
                        }
                        else
                        {
                            int i = 0;
                            string tt = posts.keywords;
                            if (!_db.Jobs.Where(a => a.title == tt).Any())
                            {
                                i++;
                                string guid = Guid.NewGuid().ToString();

                                jobs media = new jobs();

                                media.arrange = i;
                                media.lang = "ar";
                                media.guid = guid;
                                media.title = tt;


                                _db.Jobs.Add(media);
                                _db.SaveChanges();
                            }
                        }
                    }
                }
                return RedirectToAction("Index");
            ViewBag.postcatid = _db.Postcats.ToList();
            return View(posts);
        }

        // GET: posts/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            posts posts = _db.Posts.Find(id);
            if (posts == null)
            {
                return NotFound();
            }
            return View(posts);
        }

        // POST: posts/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            posts posts = _db.Posts.Find(id);
            _db.Posts.Remove(posts);
            _db.SaveChanges();
            return RedirectToAction("Index");
        }
    }
}






