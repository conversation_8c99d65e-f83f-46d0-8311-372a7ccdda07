using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace kasvol.Models
{
    public class textblockcontainer1 : Baseclass
    {
        [Key]
        public int textblockcontainerid { get; set; }
        public string code { get; set; }
        public string image { get; set; }
        public string column { get; set; }
        public string headline { get; set; }
        [DataType(DataType.MultilineText)]        public string headdes { get; set; }
        public string headlineen { get; set; }

        [DataType(DataType.MultilineText)]        public string headdesen { get; set; }
        public string headlinefr { get; set; }
        [DataType(DataType.MultilineText)]        public string headdesfr { get; set; }
        public string headlineru { get; set; }
        [DataType(DataType.MultilineText)]        public string headdesru { get; set; }
        public string unicode { get; set; }
        public virtual ICollection<textblock1> Textblock1s { get; set; }
        public string type { get; set; }
    }
}