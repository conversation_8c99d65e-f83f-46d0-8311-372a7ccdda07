using Microsoft.AspNetCore.Authorization;
using System;
using kasvol.service;
using System.Data;
using kasvol.Models;
using System.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Collections.Generic;

namespace kasvol.Controllers
{
    [Authorize]
    public class settingsController : BaseController
    {
        public settingsController(KasvolDbContext db, IConfiguration configuration, kasvolservices kasvolServices, IHttpContextAccessor httpContextAccessor)
            : base(db, configuration, kasvolServices, httpContextAccessor)
        {
        }

        // GET: settings
        public ActionResult Index()
        {
            if (Request.Cookies["CurrentLanguage"] != null)
            {
                if (Request.Cookies["CurrentLanguage"].Value == "ar-AE")
                {
                    ViewBag.ar = "Arabic.json";
                }
                else if (Request.Cookies["CurrentLanguage"].Value == "en-En")
                {
                    ViewBag.ar = "English.json";
                }

                else if (Request.Cookies["CurrentLanguage"].Value == "tr-tr")
                {
                    ViewBag.ar = "Turkish.json";
                }

            }
            return View(_db.Settings.ToList());
        }

        // GET: settings/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            Settings settings = _db.Settings.Find(id);
            if (settings == null)
            {
                return NotFound();
            }
            return View(settings);
        }

        // GET: settings/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: settings/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind("settingsID,key,value,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] Settings settings)
        {
            if (ModelState.IsValid)
            {
                _db.Settings.Add(settings);
                _db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(settings);
        }

        // GET: settings/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            Settings settings = _db.Settings.Find(id);
            if (settings == null)
            {
                return NotFound();
            }
            return View(settings);
        }

        // POST: settings/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind("settingsID,key,value,arrange,datecreated,datemodified,IP,modifiedIP,user,year,guid,lang,ogtitle,ogdescription,keywords,ogimage,twimage")] Settings settings)
        {
            if (ModelState.IsValid)
            {
                _db.Entry(settings).State = EntityState.Modified;
                _db.SaveChanges();
                return RedirectToAction("Index");
            }
            return View(settings);
        }

        // GET: settings/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            Settings settings = _db.Settings.Find(id);
            if (settings == null)
            {
                return NotFound();
            }
            return View(settings);
        }

        // POST: settings/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            Settings settings = _db.Settings.Find(id);
            _db.Settings.Remove(settings);
            _db.SaveChanges();
            return RedirectToAction("Index");
        }


    }
}


