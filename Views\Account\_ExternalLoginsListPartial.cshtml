@model kasvol.Models.ExternalLoginListViewModel

<h4>Use another service to log in.</h4>
<hr />
@{
    // ASP.NET Core Identity external login providers
    var loginProviders = ViewData["ExternalLogins"] as IEnumerable<Microsoft.AspNetCore.Authentication.AuthenticationScheme>;
    if (loginProviders == null || !loginProviders.Any()) {
        <div>
            <p>
                There are no external authentication services configured. See <a href="https://go.microsoft.com/fwlink/?LinkId=403804">this article</a>
                for details on setting up this ASP.NET application to support logging in via external services.
            </p>
        </div>
    }
    else {
        using (Html.BeginForm("ExternalLogin", "Account", new { ReturnUrl = Model.ReturnUrl })) {
            @Html.AntiForgeryToken()
            <div id="socialLoginList">
                <p>
                    @foreach (var p in loginProviders) {
                        <button type="submit" class="btn btn-default" id="@p.Name" name="provider" value="@p.Name" title="Log in using your @p.DisplayName account">@p.Name</button>
                    }
                </p>
            </div>
        }
    }
}
