using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Authorization;
using kasvol.Models;
using System.Net;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using kasvol.service;

namespace kasvol.Controllers.Core
{
    [Authorize]
    public class clientsController : BaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public clientsController(KasvolDbContext db, IConfiguration configuration, kasvolservices kasvolServices,
            UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor)
            : base(db, configuration, kasvolServices)
        {
            _db = db;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
        }

        // GET: clients
        public async Task<IActionResult> Index()
        {
            // Get language from cookie
            string currentLanguage = Request.Cookies["CurrentLanguage"];
            if (!string.IsNullOrEmpty(currentLanguage))
            {
                ViewBag.ar = currentLanguage switch
                {
                    "ar-AE" => "Arabic.json",
                    "en-En" => "English.json",
                    "tr-tr" => "Turkish.json",
                    _ => "English.json"
                };
            }
            
            return View(await _db.Clients.ToListAsync());
        }

        // GET: clients/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            var client = await _db.Clients.FindAsync(id);
            if (client == null)
            {
                return NotFound();
            }
            
            return View(client);
        }

        // GET: clients/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: clients/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(client client)
        {
            client.IP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            client.user = _userManager.GetUserId(User);
            
            if (ModelState.IsValid)
            {
                await _db.Clients.AddAsync(client);
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            
            return View(client);
        }

        // GET: clients/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            var client = await _db.Clients.FindAsync(id);
            if (client == null)
            {
                return NotFound();
            }
            
            return View(client);
        }

        // POST: clients/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(client client)
        {
            client.modifiedIP = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            client.datemodified = DateTime.Now;
            
            if (ModelState.IsValid)
            {
                _db.Entry(client).State = EntityState.Modified;
                await _db.SaveChangesAsync();
                return RedirectToAction("Index");
            }
            
            return View(client);
        }

        // GET: clients/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return BadRequest();
            }
            
            var client = await _db.Clients.FindAsync(id);
            if (client == null)
            {
                return NotFound();
            }
            
            return View(client);
        }

        // POST: clients/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var client = await _db.Clients.FindAsync(id);
            if (client != null)
            {
                _db.Clients.Remove(client);
                await _db.SaveChangesAsync();
            }
            
            return RedirectToAction("Index");
        }
    }
}
