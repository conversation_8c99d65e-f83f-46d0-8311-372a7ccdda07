@model kasvol.Models.icons


@{
    ViewBag.Title = @Resources.Resource.String126;
    Layout = "~/Views/Shared/_Layoutcp.cshtml";
}
<div id="progress" class="modal1">
    <div class="throbber">
        <div class="curtain">
        </div>
        <div class="curtain-content">
            <div>

                <div id="preloader">
                    <div id="loader"></div>
                </div>
            </div>
        </div>
    </div>
</div><div class="card card-custom">
    <div class="card-header flex-wrap py-5">
        <div class="card-title">
            <h3 class="card-label">
                @Resources.Resource.String126
            </h3>   

        </div>

        <a class="btn btn-primary" href="/icons/index">@Resources.Resource.go_back</a>
    </div>
    <div class="card-body" style="margin-bottom:125px">
        <h2 class="m--font-danger m--align-center"></h2>
        <div class="row">
            <div class="col-md-12">

                @using (Html.BeginForm("upload", "icons"
, new { enctype = "multipart/form-data" }))
                {@Html.AntiForgeryToken()




                <div class="form-horizontal " style="text-align:center">
                    @Html.ValidationSummary(true, "", new { @class = "text-danger" })


                 

                    <div class="form-group" id="user-profile-3">
                        <label class="col-sm-12 control-label no-padding-right" for="form-field-1"> Photos   </label>

                        <div class="col-sm-12">
                            <input type="file" name="postedFile" multiple="multiple" />
                        </div>
                    </div>

                    <div class="progress progress-striped">
                        <div class="progress-bar progress-bar-success">0%</div>
                    </div>
                    <br /><br />

                    <div class="form-group">
                        <div class=" col-md-12" style="text-align:center">
                            <input type="submit" value="@Resources.Resource.String69" class="btn btn-primary" />
                        </div>
                    </div>
                </div>
            }


                <div id="status">@ViewBag.mes</div>
                <div class=" col-md-12" style="text-align:center">
                    @Html.ActionLink("Back to List", "Index")
                </div>
            </div>
        </div>
    </div>
    </div>

    @section Scripts {


        <script src="/Scripts/jquery.1.7.js"></script>
        <script src="/Scripts/jquery.form.js"></script>

        <script>
                (function () {

                    var bar = $('.progress-bar');
                    var percent = $('.progress-bar');
                    var status = $('#status');

                    $('form').ajaxForm({
                        beforeSend: function () {
                            status.empty();
                            var percentVal = '0%';
                            bar.width(percentVal)
                            percent.html(percentVal);
                        },
                        uploadProgress: function (event, position, total, percentComplete) {
                            var percentVal = percentComplete + '%';
                            bar.width(percentVal)
                            percent.html(percentVal);
                        },
                        success: function () {
                            var percentVal = '100%';
                            bar.width(percentVal)
                            percent.html(percentVal);
                        },
                        complete: function (xhr) {
                            location.href = '/icons/Index';
                        }
                    });

                })();
        </script>



    }

