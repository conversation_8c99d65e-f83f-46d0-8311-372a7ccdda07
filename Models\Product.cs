using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace kasvol.Models
{
    public class Product:Baseclass
    {
        [Key]
        public int ProductID { get; set; }

        [Required]
        public string ProductName { get; set; }        public string shortDescription { get; set; }

        [Required,  DataType(DataType.MultilineText)]        public string Description { get; set; }

        public string ImagePath { get; set; } = "/co/vi/Product.png";
        public string thumbPath { get; set; } = "/co/vi/Product.png";
      
        public double? UnitPrice { get; set; } = 0.0;
        public int noofUnit { get; set; } = 0;
        public int numofvisit { get; set; } = 0;
        public string Productnumber { get; set; }  = Guid.NewGuid().ToString().Replace("-", string.Empty).Substring(0, 7);
        public double? weight { get; set; } = 0;
        public double? length { get; set; } = 0;
        public double? width { get; set; } = 0;
        public double? height { get; set; } = 0;
        public int countofcontainer { get; set; } = 0;
        public string slogan { get; set; }
        public string vediolink { get; set; }
        public string g360link { get; set; }
        public string auther { get; set; }
        public int seen { get; set; } = 0;
        public int sold { get; set; } = 0;
        public int CategoryID { get; set; }
        public virtual Category Category { get; set; }
        public int currencyid { get; set; } = 1;
        public virtual Currency Currency { get; set; }

        public int rating { get; set; } = 5;
        public bool ispublish { get; set; } = true;
        public bool isadminapproved { get; set; } = false;

        public bool isforpublic { get; set; } = false;


        public string Productcode { get; set; } = Guid.NewGuid().ToString();
     
        public string shippinginfo { get; set; }
        public bool deleted { get; set; } = false;
    
        public virtual ICollection<Reviewlist> Reviewlists { get; set; }
        public virtual ICollection<Productphotos> GetProductphotos { get; set; }
        public virtual ICollection<CartItem> CartItems { get; set; }
        public double totalrating { get; set; } = 0;
        [NotMapped]
        public double unitpriceforsort { get; set; } = 0;
        public string productlang { get; set; }



    }
}